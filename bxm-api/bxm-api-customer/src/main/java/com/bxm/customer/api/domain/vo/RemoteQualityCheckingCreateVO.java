package com.bxm.customer.api.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteQualityCheckingCreateVO {

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("账期")
    private Integer period;

    @ApiModelProperty("质检事项id")
    private Long qualityCheckingItemId;

    @ApiModelProperty("质检类型")
    private Integer qualityCheckingType;

    @ApiModelProperty("质检周期")
    private Integer qualityCheckingCycle;

    @ApiModelProperty("批次号")
    private String batchNo;

    private Long deptId;

    private Long userId;

    private String operName;

    private Integer deptType;

    private String topDeptId;

    private String companyDeptId;

    private String lastDeptId;
}
