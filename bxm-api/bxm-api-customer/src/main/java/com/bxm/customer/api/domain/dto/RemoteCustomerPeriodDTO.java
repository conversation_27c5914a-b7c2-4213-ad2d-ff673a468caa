package com.bxm.customer.api.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerPeriodDTO {

    private Long id;

    private Long customerServiceId;

    private Integer period;

    private String creditCode;

    private String taxNumber;

    private Long advisorTopDeptId;

    private Long advisorDeptId;

    private Long accountingTopDeptId;

    private Long accountingDeptId;

    private String customerName;

    private Boolean hasMedical;

    private Boolean hasSocial;

    private Integer serviceType;

    private Integer settleAccountStatus;

    private Integer deliverReportType;
}
