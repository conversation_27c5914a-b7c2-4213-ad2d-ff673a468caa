package com.bxm.thirdpart.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCompanyInfoDTO {

    @ApiModelProperty("第三方主键")
    private String KeyNo;

    @ApiModelProperty("公司名称")
    private String Name;

    @ApiModelProperty("统一社会信用代码")
    private String CreditCode;

    @ApiModelProperty("成立日期")
    private String StartDate;

    @ApiModelProperty("法定代表人")
    private String OperName;

    @ApiModelProperty("公司状态")
    private String Status;

    @ApiModelProperty("注册号")
    private String No;

    @ApiModelProperty("注册地址")
    private String Address;

    @ApiModelProperty("纳税人识别号")
    private String taxNumber;

    @ApiModelProperty("纳税人类型，1-小规模，2-一般纳税人")
    private Integer taxType;

    @ApiModelProperty("主体类型名称")
    private String entityTypeName;
}
