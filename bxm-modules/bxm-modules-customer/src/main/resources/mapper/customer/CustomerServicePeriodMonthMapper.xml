<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServicePeriodMonthMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServicePeriodMonth" id="CustomerServicePeriodMonthResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="period"    column="period"    />
        <result property="businessTopDeptId" column="business_top_dept_id" />
        <result property="businessDeptId" column="business_dept_id" />
        <result property="accountingDeptId" column="accounting_dept_id" />
        <result property="advisorDeptId" column="advisor_dept_id" />
        <result property="accountingTopDeptId" column="accounting_top_dept_id" />
        <result property="advisorTopDeptId" column="advisor_top_dept_id" />
        <result property="customerName" column="customer_name" />
        <result property="creditCode" column="credit_code" />
        <result property="taxNumber" column="tax_number" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="settlementStatus" column="settlement_status" />
    </resultMap>

    <sql id="selectCustomerServicePeriodMonthVo">
        select id, customer_service_id, period, create_by, create_time, update_by, update_time from c_customer_service_period_month
    </sql>

    <select id="selectCustomerServicePeriodMonthList" parameterType="com.bxm.customer.domain.CustomerServicePeriodMonth" resultMap="CustomerServicePeriodMonthResult">
        <include refid="selectCustomerServicePeriodMonthVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="period != null "> and period = #{period}</if>
        </where>
    </select>
    
    <select id="selectCustomerServicePeriodMonthById" parameterType="Long" resultMap="CustomerServicePeriodMonthResult">
        <include refid="selectCustomerServicePeriodMonthVo"/>
        where id = #{id}
    </select>
    <select id="selectCountBySql" resultType="java.lang.Long">
        select count(1) from (${monthConditionSql}) a
    </select>
    <select id="selectDataBySql" resultType="com.bxm.customer.domain.CustomerServicePeriodMonth">
        ${monthConditionSql}
    </select>

    <insert id="insertCustomerServicePeriodMonth" parameterType="com.bxm.customer.domain.CustomerServicePeriodMonth" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_period_month
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="period != null">period,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="period != null">#{period},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="savePeriodMonthTagRelationByCustomerIds">
        insert into c_business_tag_relation
        select null,2,ccspm.id,cbtr.tag_id,cbtr.tag_param_value,'',now(),'',now()
        from c_customer_service ccs join c_business_tag_relation cbtr on ccs.id = cbtr.business_id and cbtr.business_type = 1
        join c_customer_service_period_month ccspm on ccs.id = ccspm.customer_service_id
        where ccs.id in
        <foreach collection="customerServiceIds" open="(" close=")" item="customerServiceId" separator=",">
            #{customerServiceId}
        </foreach>
        <if test="startPeriod != null">
            and ccspm.period &gt;= #{startPeriod}
        </if>
        <if test="endPeriod != null">
            and ccspm.period &lt;= #{endPeriod}
        </if>
    </insert>
    <insert id="savePeriodMonthAdvisorEmployeeByCustomerIds">
        insert into c_customer_service_period_employee
        select null,ccspm.id,1,se.employee_id,se.employee_name ,'',now(),'',now() from c_customer_service_period_month ccspm
                                                                                         join sys_employee  se on ccspm.advisor_dept_id = se.dept_id
        where ccspm.customer_service_id in
        <foreach collection="customerServiceIds" open="(" close=")" item="customerServiceId" separator=",">
            #{customerServiceId}
        </foreach>
    </insert>
    <insert id="savePeriodMonthAccountingEmployeeByCustomerIds">
        insert into c_customer_service_period_employee
        select null,ccspm.id,2,se.employee_id,se.employee_name ,'',now(),'',now() from c_customer_service_period_month ccspm
                                                                                         join sys_employee  se on ccspm.accounting_dept_id  = se.dept_id
        where ccspm.customer_service_id in
        <foreach collection="customerServiceIds" open="(" close=")" item="customerServiceId" separator=",">
            #{customerServiceId}
        </foreach>
    </insert>
    <insert id="savePeriodMonthTaxCheckByExistsTaxCheckCustomerIds">
        insert into c_customer_service_period_month_tax_type_check
        select null,cspm.id,cctt.report_type,cctt.tax_type,'',now(),'',now()
        from c_customer_service ccs join c_customer_tax_type_check cctt on ccs.id = cctt.customer_service_id
                                    join c_customer_service_period_month cspm on ccs.id = cspm.customer_service_id
        where ccs.id in
        <foreach collection="customerServiceIds" open="(" close=")" item="customerServiceId" separator=",">
            #{customerServiceId}
        </foreach>
        <if test="startPeriod != null">
            and cspm.period &gt;= #{startPeriod}
        </if>
        <if test="endPeriod != null">
            and cspm.period &lt;= #{endPeriod}
        </if>
    </insert>
    <insert id="savePeriodMonthTaxCheckByNotExistsTaxCheckSmallCustomerIds">
        insert into c_customer_service_period_month_tax_type_check
        select null,cspm.id,cctt.report_type,cctt.tax_type,'',now(),'',now()
        from (
                 select ccs.*
                 from c_customer_service ccs left join c_customer_tax_type_check cctt on ccs.id = cctt.customer_service_id
                 where cctt.id is null and ccs.tax_type = 1) a join c_customer_tax_type_check cctt on cctt.customer_service_id = 0
                                                               join c_customer_service_period_month cspm on a.id = cspm.customer_service_id
        where a.id in
        <foreach collection="customerServiceIds" open="(" close=")" item="customerServiceId" separator=",">
            #{customerServiceId}
        </foreach>
        <if test="startPeriod != null">
            and cspm.period &gt;= #{startPeriod}
        </if>
        <if test="endPeriod != null">
            and cspm.period &lt;= #{endPeriod}
        </if>
    </insert>
    <insert id="savePeriodMonthTaxCheckByNotExistsTaxCheckCommlyCustomerIds">
        insert into c_customer_service_period_month_tax_type_check
        select null,cspm.id,cctt.report_type,cctt.tax_type,'',now(),'',now()
        from (
                 select ccs.*
                 from c_customer_service ccs left join c_customer_tax_type_check cctt on ccs.id = cctt.customer_service_id
                 where cctt.id is null and ccs.tax_type = 2) a join c_customer_tax_type_check cctt on cctt.customer_service_id = -1
                                                               join c_customer_service_period_month cspm on a.id = cspm.customer_service_id
        where a.id in
        <foreach collection="customerServiceIds" open="(" close=")" item="customerServiceId" separator=",">
            #{customerServiceId}
        </foreach>
        <if test="startPeriod != null">
            and cspm.period &gt;= #{startPeriod}
        </if>
        <if test="endPeriod != null">
            and cspm.period &lt;= #{endPeriod}
        </if>
    </insert>

    <update id="updateCustomerServicePeriodMonth" parameterType="com.bxm.customer.domain.CustomerServicePeriodMonth">
        update c_customer_service_period_month
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateSettlementStatusBySettlementOrderDatas">
        update c_customer_service_period_month set settlement_status = #{settlementStatus}
        where id in (select distinct business_id from c_settlement_order_data where settlement_order_id = #{settlementOrderId})
    </update>
    <update id="updatePrepayStatusBySettlementOrderDatas">
        update c_customer_service_period_month set prepay_status = #{prepayStatus}
        where id in (select distinct business_id from c_settlement_order_data where settlement_order_id = #{settlementOrderId})
    </update>
    <update id="updateDeliverStatus">
        UPDATE
            c_customer_service_period_month spm
        SET
            medical_deliver_status =
                CASE
                    WHEN
                        (NOT EXISTS (
                            SELECT 1
                            FROM c_customer_deliver ccd
                            WHERE ccd.customer_service_period_month_id = spm.id
                              AND ccd.deliver_type = 1
                              AND ccd.is_del = 0
                        )
                            AND NOT EXISTS (
                                SELECT 1
                                FROM c_business_tag_relation btr
                                WHERE btr.business_id = spm.id
                                  AND btr.business_type = 2
                                  AND btr.tag_id = 2
                            )) OR spm.service_type = 2
                        THEN -2
                    WHEN NOT EXISTS (
                        SELECT 1
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                    )
                        AND EXISTS (
                            SELECT 1
                            FROM c_business_tag_relation btr
                            WHERE btr.business_id = spm.id
                              AND btr.business_type = 2
                              AND btr.tag_id = 2
                        )
                        AND spm.service_type = 1
                        THEN -1
                    ELSE (
                        SELECT ccd.status
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                        LIMIT 1
            )
        END,
social_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND NOT EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
person_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
            LIMIT 1
        )
        END ,
operation_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
national_tax_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
          AND MOD(spm.period, 100) IN (3, 6, 9, 12)
		  AND spm.service_type = 1
		  THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
		  THEN -1
		  WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
		  THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
 pre_auth_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )
        AND spm.tax_type = 2
        AND spm.service_type = 1 THEN -1
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )) OR spm.service_type = 2 THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
medical_deliver_should_report_status =
                CASE
                    WHEN
                        NOT EXISTS (
                                SELECT 1
                                FROM c_business_tag_relation btr
                                WHERE btr.business_id = spm.id
                                  AND btr.business_type = 2
                                  AND btr.tag_id = 2
                            ) OR spm.service_type = 2
                        THEN -2
                    ELSE -1
        END,
social_deliver_should_report_status =
    CASE
        WHEN
		   NOT EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        ) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END,
person_tax_deliver_should_report_status =
    CASE
        WHEN (
        MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END ,
operation_tax_deliver_should_report_status =
    CASE
        WHEN (
        MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) = 6
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) = 12
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END,
national_tax_deliver_should_report_status =
    CASE
        WHEN MOD(spm.period, 100) IN (3, 6, 9, 12)
		  AND spm.service_type = 1
		  THEN -1
        WHEN
        MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
		  THEN -1
		  ELSE -2
        END
        where spm.period = #{nowPeriod}
    </update>
    <update id="updateDeliverStatusById">
        UPDATE
            c_customer_service_period_month spm
        SET
            medical_deliver_status =
                CASE
                    WHEN
                        (NOT EXISTS (
                            SELECT 1
                            FROM c_customer_deliver ccd
                            WHERE ccd.customer_service_period_month_id = spm.id
                              AND ccd.deliver_type = 1
                              AND ccd.is_del = 0
                        )
                            AND NOT EXISTS (
                                SELECT 1
                                FROM c_business_tag_relation btr
                                WHERE btr.business_id = spm.id
                                  AND btr.business_type = 2
                                  AND btr.tag_id = 2
                            )) OR spm.service_type = 2
                        THEN -2
                    WHEN NOT EXISTS (
                        SELECT 1
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                    )
                        AND EXISTS (
                            SELECT 1
                            FROM c_business_tag_relation btr
                            WHERE btr.business_id = spm.id
                              AND btr.business_type = 2
                              AND btr.tag_id = 2
                        )
                        AND spm.service_type = 1
                        THEN -1
                    ELSE (
                        SELECT ccd.status
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                        LIMIT 1
            )
        END,
social_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND NOT EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
person_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
            LIMIT 1
        )
        END ,
operation_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
national_tax_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
          AND MOD(spm.period, 100) IN (3, 6, 9, 12)
		  AND spm.service_type = 1
		  THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
		  THEN -1
		  WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
		  THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
 pre_auth_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )
        AND spm.tax_type = 2
        AND spm.service_type = 1 THEN -1
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )) OR spm.service_type = 2 THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
            LIMIT 1
        )
        END
        where spm.id = #{customerServicePeriodMonthId}
    </update>
    <update id="updateDeliverStatusByCustomerServiceId">
        UPDATE
            c_customer_service_period_month spm
        SET
            medical_deliver_status =
                CASE
                    WHEN
                        (NOT EXISTS (
                            SELECT 1
                            FROM c_customer_deliver ccd
                            WHERE ccd.customer_service_period_month_id = spm.id
                              AND ccd.deliver_type = 1
                              AND ccd.is_del = 0
                        )
                            AND NOT EXISTS (
                                SELECT 1
                                FROM c_business_tag_relation btr
                                WHERE btr.business_id = spm.id
                                  AND btr.business_type = 2
                                  AND btr.tag_id = 2
                            )) OR spm.service_type = 2
                        THEN -2
                    WHEN NOT EXISTS (
                        SELECT 1
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                    )
                        AND EXISTS (
                            SELECT 1
                            FROM c_business_tag_relation btr
                            WHERE btr.business_id = spm.id
                              AND btr.business_type = 2
                              AND btr.tag_id = 2
                        )
                        AND spm.service_type = 1
                        THEN -1
                    ELSE (
                        SELECT ccd.status
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                        LIMIT 1
            )
        END,
social_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND NOT EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
person_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
            LIMIT 1
        )
        END ,
operation_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
national_tax_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
          AND MOD(spm.period, 100) IN (3, 6, 9, 12)
		  AND spm.service_type = 1
		  THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
		  THEN -1
		  WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
		  THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
 pre_auth_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )
        AND spm.tax_type = 2
        AND spm.service_type = 1 THEN -1
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )) OR spm.service_type = 2 THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
medical_deliver_should_report_status =
                CASE
                    WHEN
                        NOT EXISTS (
                                SELECT 1
                                FROM c_business_tag_relation btr
                                WHERE btr.business_id = spm.id
                                  AND btr.business_type = 2
                                  AND btr.tag_id = 2
                            ) OR spm.service_type = 2
                        THEN -2
                    ELSE -1
        END,
social_deliver_should_report_status =
    CASE
        WHEN
		   NOT EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        ) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END,
person_tax_deliver_should_report_status =
    CASE
        WHEN (
        MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END ,
operation_tax_deliver_should_report_status =
    CASE
        WHEN (
        MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) = 6
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) = 12
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END,
national_tax_deliver_should_report_status =
    CASE
        WHEN MOD(spm.period, 100) IN (3, 6, 9, 12)
		  AND spm.service_type = 1
		  THEN -1
        WHEN
        MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
		  THEN -1
		  ELSE -2
        END
        WHERE spm.customer_service_id = #{customerServiceId}
    </update>
    <update id="updateDeliverStatusByCustomerServiceIds">
        UPDATE
        c_customer_service_period_month spm
        SET
        medical_deliver_status =
        CASE
        WHEN
        (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 1
        AND ccd.is_del = 0
        )
        AND NOT EXISTS (
        SELECT 1
        FROM c_business_tag_relation btr
        WHERE btr.business_id = spm.id
        AND btr.business_type = 2
        AND btr.tag_id = 2
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 1
        AND ccd.is_del = 0
        )
        AND EXISTS (
        SELECT 1
        FROM c_business_tag_relation btr
        WHERE btr.business_id = spm.id
        AND btr.business_type = 2
        AND btr.tag_id = 2
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
        SELECT ccd.status
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 1
        AND ccd.is_del = 0
        LIMIT 1
        )
        END,
        social_deliver_status =
        CASE
        WHEN (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 2
        AND ccd.is_del = 0
        )
        AND NOT EXISTS (
        SELECT 1
        FROM c_business_tag_relation btr
        WHERE btr.business_id = spm.id
        AND btr.business_type = 2
        AND btr.tag_id = 3
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 2
        AND ccd.is_del = 0
        )
        AND EXISTS (
        SELECT 1
        FROM c_business_tag_relation btr
        WHERE btr.business_id = spm.id
        AND btr.business_type = 2
        AND btr.tag_id = 3
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
        SELECT ccd.status
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 2
        AND ccd.is_del = 0
        LIMIT 1
        )
        END,
        person_tax_deliver_status =
        CASE
        WHEN (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 3
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type = '个人所得税'
        AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 3
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type = '个人所得税'
        AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 3
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type = '个人所得税'
        AND spmtc.report_type IN (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 3
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type = '个人所得税'
        AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
        SELECT ccd.status
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 3
        AND ccd.is_del = 0
        LIMIT 1
        )
        END ,
        operation_tax_deliver_status =
        CASE
        WHEN (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 6
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
        AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 6
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
        AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 6
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
        AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 6
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
        AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 6
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
        AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 6
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
        AND spmtc.report_type in (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 6
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
        AND spmtc.report_type in (1, 2, 5)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 6
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
        AND spmtc.report_type in (1, 2, 3, 5)
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
        SELECT ccd.status
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 6
        AND ccd.is_del = 0
        LIMIT 1
        )
        END,
        national_tax_deliver_status =
        CASE
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 4
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 4
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type not like '个人所得税%'
        AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        WHEN (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 4
        AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type not like '个人所得税%'
        AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        ELSE (
        SELECT ccd.status
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 4
        AND ccd.is_del = 0
        LIMIT 1
        )
        END,
        pre_auth_deliver_status =
        CASE
        WHEN NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 5
        AND ccd.is_del = 0
        )
        AND spm.tax_type = 2
        AND spm.service_type = 1 THEN -1
        WHEN (NOT EXISTS (
        SELECT 1
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 5
        AND ccd.is_del = 0
        )) OR spm.service_type = 2 THEN -2
        ELSE (
        SELECT ccd.status
        FROM c_customer_deliver ccd
        WHERE ccd.customer_service_period_month_id = spm.id
        AND ccd.deliver_type = 5
        AND ccd.is_del = 0
        LIMIT 1
        )
        END,
        medical_deliver_should_report_status =
        CASE
        WHEN
        NOT EXISTS (
        SELECT 1
        FROM c_business_tag_relation btr
        WHERE btr.business_id = spm.id
        AND btr.business_type = 2
        AND btr.tag_id = 2
        ) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END,
        social_deliver_should_report_status =
        CASE
        WHEN
        NOT EXISTS (
        SELECT 1
        FROM c_business_tag_relation btr
        WHERE btr.business_id = spm.id
        AND btr.business_type = 2
        AND btr.tag_id = 3
        ) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END,
        person_tax_deliver_should_report_status =
        CASE
        WHEN (
        MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type = '个人所得税'
        AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type = '个人所得税'
        AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END ,
        operation_tax_deliver_should_report_status =
        CASE
        WHEN (
        MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
        AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
        AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) = 6
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
        AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) = 12
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
        AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END,
        national_tax_deliver_should_report_status =
        CASE
        WHEN MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND spm.service_type = 1
        THEN -1
        WHEN
        MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
        SELECT 1
        FROM c_customer_service_period_month_tax_type_check spmtc
        WHERE spmtc.customer_service_period_month_id = spm.id
        AND spmtc.tax_type not like '个人所得税%'
        AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        ELSE -2
        END
        WHERE spm.customer_service_id in
        <foreach collection="customerServiceIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </update>
    <update id="updateBankPaymentResult">
        UPDATE
        c_customer_service_period_month spm
        LEFT JOIN
        c_customer_service_bank_account bank
        ON
        spm.customer_service_id = bank.customer_service_id
        AND (bank.account_open_date IS NULL OR spm.period &gt;= DATE_FORMAT(bank.account_open_date, '%Y%m'))
        AND (bank.account_close_date IS NULL OR spm.period &lt;= DATE_FORMAT(bank.account_close_date, '%Y%m'))

        SET spm.bank_payment_result = if(bank.id IS NULL, 0, 1)
        where spm.period = #{nowPeriod}
    </update>
    <update id="updateInEndAccountStatus">
        UPDATE
            c_customer_service_period_month spm
        set spm.in_account_status = 0,spm.settle_account_status = 1
        where spm.period = #{nowPeriod}
    </update>
    <update id="updateDeliverStatusAll">
        UPDATE
            c_customer_service_period_month spm
        SET
            medical_deliver_status =
                CASE
                    WHEN
                        (NOT EXISTS (
                            SELECT 1
                            FROM c_customer_deliver ccd
                            WHERE ccd.customer_service_period_month_id = spm.id
                              AND ccd.deliver_type = 1
                              AND ccd.is_del = 0
                        )
                            AND NOT EXISTS (
                                SELECT 1
                                FROM c_business_tag_relation btr
                                WHERE btr.business_id = spm.id
                                  AND btr.business_type = 2
                                  AND btr.tag_id = 2
                            )) OR spm.service_type = 2
                        THEN -2
                    WHEN NOT EXISTS (
                        SELECT 1
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                    )
                        AND EXISTS (
                            SELECT 1
                            FROM c_business_tag_relation btr
                            WHERE btr.business_id = spm.id
                              AND btr.business_type = 2
                              AND btr.tag_id = 2
                        )
                        AND spm.service_type = 1
                        THEN -1
                    ELSE (
                        SELECT ccd.status
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                        LIMIT 1
            )
        END,
social_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND NOT EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
person_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
            LIMIT 1
        )
        END ,
operation_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
national_tax_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
          AND MOD(spm.period, 100) IN (3, 6, 9, 12)
		  AND spm.service_type = 1
		  THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
		  THEN -1
		  WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
		  THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
 pre_auth_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )
        AND spm.tax_type = 2
        AND spm.service_type = 1 THEN -1
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )) OR spm.service_type = 2 THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
medical_deliver_should_report_status =
                CASE
                    WHEN
                        NOT EXISTS (
                                SELECT 1
                                FROM c_business_tag_relation btr
                                WHERE btr.business_id = spm.id
                                  AND btr.business_type = 2
                                  AND btr.tag_id = 2
                            ) OR spm.service_type = 2
                        THEN -2
                    ELSE -1
        END,
social_deliver_should_report_status =
    CASE
        WHEN
		   NOT EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        ) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END,
person_tax_deliver_should_report_status =
    CASE
        WHEN (
        MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END ,
operation_tax_deliver_should_report_status =
    CASE
        WHEN (
        MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) = 6
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (
        MOD(spm.period, 100) = 12
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' AND spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        ELSE -1
        END,
national_tax_deliver_should_report_status =
    CASE
        WHEN MOD(spm.period, 100) IN (3, 6, 9, 12)
		  AND spm.service_type = 1
		  THEN -1
        WHEN
        MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
		  THEN -1
		  ELSE -2
        END
        where `period` > 0
    </update>
    <update id="updateNewInAccountStatus">
        update c_customer_service_period_month
        set in_account_status = 1,settle_account_status = 1
        where period = #{period}
        and in_account_status = 0
        and not exists (
                select 1 from c_business_tag_relation where tag_id = 6 and business_id = c_customer_service_period_month.id and business_type = 2
                )
          and not exists (
            select 1 from c_business_tag_relation where tag_id = 6 and business_id = c_customer_service_period_month.customer_service_id and business_type = 1
        )
          and not exists(
            select 1 from c_customer_service_cashier_accounting where customer_service_period_month_id = c_customer_service_period_month.id and type = 1 and is_del = 0
        )
    </update>
    <update id="updateExceptionInAccountStatus">
        update c_customer_service_period_month
        set in_account_status = 3,settle_account_status = 1
        where period = #{period}
          and in_account_status = 0
          AND (exists (
            select 1 from c_business_tag_relation where tag_id = 6 and business_id = c_customer_service_period_month.id and business_type = 2
        ) OR
               exists (
                   select 1 from c_business_tag_relation where tag_id = 6 and business_id = c_customer_service_period_month.customer_service_id and business_type = 1
               ))
          and not exists(
            select 1 from c_customer_service_cashier_accounting where customer_service_period_month_id = c_customer_service_period_month.id and type = 1 and is_del = 0
        )
    </update>
    <update id="updateDeliverStatusByIds">
        UPDATE
            c_customer_service_period_month spm
        SET
            medical_deliver_status =
                CASE
                    WHEN
                        (NOT EXISTS (
                            SELECT 1
                            FROM c_customer_deliver ccd
                            WHERE ccd.customer_service_period_month_id = spm.id
                              AND ccd.deliver_type = 1
                              AND ccd.is_del = 0
                        )
                            AND NOT EXISTS (
                                SELECT 1
                                FROM c_business_tag_relation btr
                                WHERE btr.business_id = spm.id
                                  AND btr.business_type = 2
                                  AND btr.tag_id = 2
                            )) OR spm.service_type = 2
                        THEN -2
                    WHEN NOT EXISTS (
                        SELECT 1
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                    )
                        AND EXISTS (
                            SELECT 1
                            FROM c_business_tag_relation btr
                            WHERE btr.business_id = spm.id
                              AND btr.business_type = 2
                              AND btr.tag_id = 2
                        )
                        AND spm.service_type = 1
                        THEN -1
                    ELSE (
                        SELECT ccd.status
                        FROM c_customer_deliver ccd
                        WHERE ccd.customer_service_period_month_id = spm.id
                          AND ccd.deliver_type = 1
                          AND ccd.is_del = 0
                        LIMIT 1
            )
        END,
social_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND NOT EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
        )
        AND EXISTS (
            SELECT 1
            FROM c_business_tag_relation btr
            WHERE btr.business_id = spm.id
              AND btr.business_type = 2
              AND btr.tag_id = 3
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 2
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
person_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type IN (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type = '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 3
              AND ccd.is_del = 0
            LIMIT 1
        )
        END ,
operation_tax_deliver_status =
    CASE
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )) OR spm.service_type = 2
        THEN -2
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (1, 2, 4, 5, 7, 8, 10, 11)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) IN (3, 9)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 6
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 5)
        )
        AND spm.service_type = 1
        THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) = 12
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type like '个人所得税%' and spmtc.tax_type != '个人所得税'
              AND spmtc.report_type in (1, 2, 3, 5)
        )
        AND spm.service_type = 1
        THEN -1
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 6
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
national_tax_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
          AND MOD(spm.period, 100) IN (3, 6, 9, 12)
		  AND spm.service_type = 1
		  THEN -1
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )
        AND spm.service_type = 1
		  THEN -1
		  WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
        )
        AND MOD(spm.period, 100) NOT IN (3, 6, 9, 12)
        AND NOT EXISTS (
            SELECT 1
            FROM c_customer_service_period_month_tax_type_check spmtc
            WHERE spmtc.customer_service_period_month_id = spm.id
              AND spmtc.tax_type not like '个人所得税%'
              AND spmtc.report_type = 1
        )) OR spm.service_type = 2
		  THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 4
              AND ccd.is_del = 0
            LIMIT 1
        )
        END,
 pre_auth_deliver_status =
    CASE
        WHEN NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )
        AND spm.tax_type = 2
        AND spm.service_type = 1 THEN -1
        WHEN (NOT EXISTS (
            SELECT 1
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
        )) OR spm.service_type = 2 THEN -2
        ELSE (
            SELECT ccd.status
            FROM c_customer_deliver ccd
            WHERE ccd.customer_service_period_month_id = spm.id
              AND ccd.deliver_type = 5
              AND ccd.is_del = 0
            LIMIT 1
        )
        END
        where spm.id in
        <foreach collection="customerServicePeriodMonthIds" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </update>

    <delete id="deleteCustomerServicePeriodMonthById" parameterType="Long">
        delete from c_customer_service_period_month where id = #{id}
    </delete>

    <delete id="deleteCustomerServicePeriodMonthByIds" parameterType="String">
        delete from c_customer_service_period_month where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deletePeriodMonthTagRelationByPeriodIds">
        delete from c_business_tag_relation where business_type = 2 and business_id in
        <foreach collection="periodIds" open="(" close=")" item="periodId" separator=",">
            #{periodId}
        </foreach>
    </delete>
    <delete id="deletePeriodMonthTaxCheckByPeriodIds">
        delete from c_customer_service_period_month_tax_type_check where customer_service_period_month_id in
        <foreach collection="periodIds" open="(" close=")" item="periodId" separator=",">
            #{periodId}
        </foreach>
    </delete>

    <select id="customerServicePeriodMonthList" resultType="com.bxm.customer.domain.dto.CustomerServicePeriodMonthDTO">
        select * from c_customer_service_period_month
        <where>
            <if test="tagIncludeFlag != null">
                <if test="tagIncludeFlag == 0">
                    and id not in
                    <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeFlag == 1">
                    and id in
                    <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="searchIds != null and searchIds.size > 0">
                and id in
                <foreach collection="searchIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and service_number like concat('%', #{vo.serviceNumber}, '%')
            </if>
            <if test="vo.startPeriod != null">
                and `period` &gt;= #{vo.startPeriod}
            </if>
            <if test="vo.endPeriod != null">
                and `period` &lt;= #{vo.endPeriod}
            </if>
            <if test="vo.taxType != null">
                and tax_type = #{vo.taxType}
            </if>
            <if test="vo.businessDeptId != null">
                and business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.advisorDeptId != null">
                and advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and accounting_dept_id = #{vo.accountingDeptId}
            </if>
        </where>
        order by `period` desc, customer_service_id desc
    </select>

    <select id="customerServicePeriodMonthListWithServiceStatus" resultType="com.bxm.customer.domain.dto.CustomerServicePeriodMonthDTO">
        select c_customer_service_period_month.* from c_customer_service_period_month
        left join c_customer_service on c_customer_service.id = c_customer_service_period_month.customer_service_id
        <where>
            c_customer_service.is_del = 0 and c_customer_service.service_status = #{vo.serviceStatus}
            <if test="tagIncludeFlag != null">
                <if test="tagIncludeFlag == 0">
                    and c_customer_service_period_month.id not in
                    <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeFlag == 1">
                    and c_customer_service_period_month.id in
                    <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="searchIds != null and searchIds.size > 0">
                and c_customer_service_period_month.id in
                <foreach collection="searchIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and c_customer_service_period_month.customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and c_customer_service_period_month.service_number like concat('%', #{vo.serviceNumber}, '%')
            </if>
            <if test="vo.startPeriod != null">
                and c_customer_service_period_month.`period` &gt;= #{vo.startPeriod}
            </if>
            <if test="vo.endPeriod != null">
                and c_customer_service_period_month.`period` &lt;= #{vo.endPeriod}
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.businessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.advisorDeptId != null">
                and c_customer_service_period_month.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
        </where>
        order by c_customer_service_period_month.`period` desc, c_customer_service_period_month.customer_service_id desc
    </select>

    <select id="listWithServiceStatusInAuth" resultType="com.bxm.customer.domain.dto.CustomerServicePeriodMonthDTO">
        select c_customer_service_period_month.* from c_customer_service_period_month
        <if test="vo.inAccountStatus != null">join c_customer_service_in_account on c_customer_service_in_account.customer_service_period_month_id = c_customer_service_period_month.id and c_customer_service_in_account.is_del = 0</if>
        <if test="vo.docHandoverStatus != null">left join (select customer_service_period_month_id, max(whole_level) as whole_level from c_customer_service_doc_handover where is_del = 0 group by customer_service_period_month_id) temp on c_customer_service_period_month.id = temp.customer_service_period_month_id</if>
        <where>
            <if test="userDept.isAdmin == null or userDept.isAdmin == 0">
                <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                    and 1 = -1
                </if>
                <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.serviceType != null">
                and c_customer_service_period_month.service_type = #{vo.serviceType}
            </if>
            <if test="vo.settlementStatus != null">
                <if test="vo.settlementStatus == 4">
                    and c_customer_service_period_month.settlement_status in (4,5,6)
                </if>
                <if test="vo.settlementStatus != 4">
                    and c_customer_service_period_month.settlement_status = #{vo.settlementStatus}
                </if>
            </if>
            <if test="vo.inAccountStatus != null">
                and c_customer_service_in_account.status = #{vo.inAccountStatus}
            </if>
            <if test="vo.docHandoverStatus != null">
                <if test="vo.docHandoverStatus == 0">
                    and temp.customer_service_period_month_id is null
                </if>
                <if test="vo.docHandoverStatus == 1">
                    and temp.whole_level = 1
                </if>
                <if test="vo.docHandoverStatus == 2">
                    and temp.whole_level = 2
                </if>
                <if test="vo.docHandoverStatus == 3">
                    and temp.whole_level = 3
                </if>
                <if test="vo.docHandoverStatus == 4">
                    and temp.whole_level = 1 and c_customer_service_period_month.id in (select distinct customer_service_period_month_id from c_customer_service_doc_handover where is_del = 0 and status = 3)
                </if>
            </if>
            <if test="vo.serviceStatus != null">
                and c_customer_service_period_month.service_status = #{vo.serviceStatus}
            </if>
            <if test="tagIncludeFlag != null">
                <if test="tagIncludeFlag == 0">
                    and c_customer_service_period_month.id not in
                    <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeFlag == 1">
                    and c_customer_service_period_month.id in
                    <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="searchIds != null and searchIds.size > 0">
                and c_customer_service_period_month.id in
                <foreach collection="searchIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_customer_service_period_month.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_customer_service_period_month.credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and c_customer_service_period_month.customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and c_customer_service_period_month.service_number like concat('%', #{vo.serviceNumber}, '%')
            </if>
            <if test="vo.startPeriod != null">
                and c_customer_service_period_month.`period` &gt;= #{vo.startPeriod}
            </if>
            <if test="vo.endPeriod != null">
                and c_customer_service_period_month.`period` &lt;= #{vo.endPeriod}
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.businessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.accountingTopDeptId != null">
                and c_customer_service_period_month.accounting_top_dept_id = #{vo.accountingTopDeptId}
            </if>
            <if test="vo.advisorDeptId != null">
                and c_customer_service_period_month.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.accountingStatus != null">
                and c_customer_service_period_month.accounting_status = #{vo.accountingStatus}
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and c_customer_service_period_month.customer_service_id in
                <foreach collection="customerServiceIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by c_customer_service_period_month.`period` desc, c_customer_service_period_month.customer_service_id desc
    </select>

    <select id="searchAccounting" resultType="long">
        select distinct c_customer_service_period_month.id
        from c_customer_service_period_month
        left join sys_dept on sys_dept.dept_id = c_customer_service_period_month.accounting_dept_id
        left join sys_employee on sys_employee.dept_id = sys_dept.dept_id
        where sys_dept.dept_name like concat('%', #{accountingEmployee}, '%') or sys_employee.employee_name like concat('%', #{accountingEmployee}, '%')
    </select>

    <select id="searchAdvisor" resultType="long">
        select distinct c_customer_service_period_month.id
        from c_customer_service_period_month
                 left join sys_dept on sys_dept.dept_id = c_customer_service_period_month.advisor_dept_id
                 left join sys_employee on sys_employee.dept_id = sys_dept.dept_id
        where sys_dept.dept_name like concat('%', #{advisorEmployee}, '%') or sys_employee.employee_name like concat('%', #{advisorEmployee}, '%')
    </select>

    <select id="selectAccountingInfoByIds" resultType="com.bxm.customer.domain.dto.AccountingInfoSourceDTO">
        select
               c_customer_service_period_month.id as customerServicePeriodMonthId,
               c_customer_service_period_month.accounting_dept_id as accountingDeptId,
               sys_dept.dept_name as accountingDeptName,
               sys_employee.employee_id as accountingEmployeeId,
               sys_employee.employee_name as accountingEmployeeName
        from c_customer_service_period_month
                 left join sys_dept on sys_dept.dept_id = c_customer_service_period_month.accounting_dept_id
                 left join sys_employee on sys_employee.dept_id = sys_dept.dept_id
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAdvisorInfoByIds" resultType="com.bxm.customer.domain.dto.AdvisorInfoSourceDTO">
        select
        c_customer_service_period_month.id as customerServicePeriodMonthId,
        c_customer_service_period_month.advisor_dept_id as advisorDeptId,
        sys_dept.dept_name as advisorDeptName,
        sys_employee.employee_id as advisorEmployeeId,
        sys_employee.employee_name as advisorEmployeeName
        from c_customer_service_period_month
        left join sys_dept on sys_dept.dept_id = c_customer_service_period_month.advisor_dept_id
        left join sys_employee on sys_employee.dept_id = sys_dept.dept_id
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectPeriodMonthByYearAndUserDept" resultMap="CustomerServicePeriodMonthResult">
        select
        cspm.id,
        cspm.period
        from c_customer_service_period_month cspm
        <where>
            <if test="year != null">
                and cspm.period like concat(#{year}, '%')
            </if>
            <if test="statisticTaxType != null">
                <if test="statisticTaxType == 3">
                    and cspm.id in (select distinct business_id from c_business_tag_relation where tag_id = 1 and business_type = 2)
                </if>
                <if test="statisticTaxType != 3">
                    and cspm.tax_type = #{statisticTaxType} and cspm.id not in (select distinct business_id from c_business_tag_relation where tag_id = 1 and business_type = 2)
                </if>
            </if>
            <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cspm.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                and (
                cspm.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="selectNoInAccount"  resultMap="CustomerServicePeriodMonthResult">
        select
               c_customer_service_period_month.id,
               c_customer_service_period_month.customer_service_id,
               c_customer_service_period_month.period
        from c_customer_service_period_month
        where c_customer_service_period_month.id not in (select customer_service_period_month_id from c_customer_service_in_account)
    </select>

    <select id="selectNoInAccountByCustomerService"  resultMap="CustomerServicePeriodMonthResult">
        select
            c_customer_service_period_month.id,
            c_customer_service_period_month.customer_service_id,
            c_customer_service_period_month.period
        from c_customer_service_period_month
        where c_customer_service_period_month.customer_service_id = #{customerServiceId}
          and c_customer_service_period_month.id not in (select customer_service_period_month_id from c_customer_service_in_account)
    </select>

    <select id="selectNoInAccountByCustomerServiceBatch"  resultMap="CustomerServicePeriodMonthResult">
        select
            c_customer_service_period_month.id,
            c_customer_service_period_month.customer_service_id,
            c_customer_service_period_month.period
        from c_customer_service_period_month
        where
        c_customer_service_period_month.customer_service_id in
        <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
            #{customerServiceId}
        </foreach>
        and c_customer_service_period_month.id not in (select customer_service_period_month_id from c_customer_service_in_account)
    </select>


    <select id="getCustomerPeriodByListCreditCodeAndPeriod" resultMap="CustomerServicePeriodMonthResult">
        select * from
                     c_customer_service_period_month
        where (credit_code, period) in
        <foreach collection="voList" item="vo" open="(" separator="," close=")">
            (#{vo.creditCode}, #{vo.period})
        </foreach>
    </select>
    <select id="searchByDeptIds" resultType="java.lang.Long">
        select distinct id
        from c_customer_service_period_month
        where
            advisor_dept_id in
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            or advisor_top_dept_id in
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            or accounting_dept_id in
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            or accounting_top_dept_id in
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
    </select>
    <select id="selectPeriodMonthCountByYearAndUserDept" resultType="com.bxm.customer.domain.dto.PeriodCountDTO">
        select
        cspm.period as period,
        count(cspm.id) as `count`,
        COUNT(if(cspm.settle_account_status >= 2, 1, NULL)) AS inAccountCount,
        COUNT(if(cspm.settle_account_status = 3, 1, NULL)) AS endAccountCount,
        COUNT(if(cspm.settle_account_status = 1, 1, NULL)) AS notInAccountCount,
        COUNT(if(cspm.settle_account_status &lt;= 2, 1, NULL)) AS notEndAccountCount
        from c_customer_service_period_month cspm
        <where>
            cspm.accounting_status = 1
            <if test="year != null">
                and cspm.year = #{year}
            </if>
            <if test="statisticTaxType != null">
                <if test="statisticTaxType == 3">
                    AND EXISTS (
                    SELECT 1
                    FROM c_business_tag_relation where tag_id = 1 and business_type = 2 AND business_id = cspm.id
                    )
                </if>
                <if test="statisticTaxType != 3">
                    and cspm.tax_type = #{statisticTaxType}
                    AND not exists (
                    SELECT 1
                    FROM c_business_tag_relation where tag_id = 1 and business_type = 2 AND business_id = cspm.id
                    )
                </if>
            </if>
            <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 2">
                    and (
                    cspm.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                and (
                cspm.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
        group by cspm.period
    </select>
    <select id="selectPeriodIdsBatchByCreditCodeAndPeriod" resultType="java.lang.Long">
        select id
            from c_customer_service_period_month
        where (credit_code, period) in
        <foreach collection="dataList" item="data" open="(" separator="," close=")">
            (#{data.creditCode}, #{data.period})
        </foreach>
        <if test="isSupplement != null">
            <if test="isSupplement == true">
                and settlement_status = 4
            </if>
            <if test="isSupplement == false">
                and settlement_status = 2
            </if>
        </if>
    </select>
    <select id="selectBatchByCreditCodeAndPeriod"
            resultMap="CustomerServicePeriodMonthResult">
        select id, credit_code, period, customer_service_id, settlement_status, business_dept_id, prepay_status
        from c_customer_service_period_month
        where (credit_code, period) in
        <foreach collection="dataList" item="data" open="(" separator="," close=")">
            (#{data.creditCode}, #{data.period})
        </foreach>
    </select>
    <select id="selectValidCountByDeptTypeAndPeriods"
            resultType="com.bxm.customer.domain.dto.workBench.ManagerCommonCountDTO">
        SELECT
            <if test="statisticType == 1">
                ccspm.advisor_dept_id as deptId,
            </if>
            <if test="statisticType == 2">
                ccspm.accounting_dept_id as deptId,
            </if>
        ccspm.period,COUNT(distinct ccspm.id) as periodCount FROM c_customer_service_period_month ccspm
                                                             join c_customer_service cs on ccspm.customer_service_id = cs.id
        WHERE
            cs.is_del = 0 and
            ccspm.service_type = 1 and
            ccspm.period in
        <foreach collection="periods" separator="," item="period" close=")" open="(">
            #{period}
        </foreach>
        <if test="statisticType == 1">
            AND ccspm.advisor_dept_id IS NOT NULL
        </if>
        <if test="statisticType == 2">
            AND ccspm.accounting_dept_id IS NOT NULL
        </if>
        GROUP BY
        <if test="statisticType == 1">
            ccspm.advisor_dept_id,
        </if>
        <if test="statisticType == 2">
            ccspm.accounting_dept_id,
        </if>
        ccspm.period
    </select>
    <select id="selectOperCountByDeptTypeAndTime"
            resultType="com.bxm.customer.domain.dto.workBench.ManagerCommonCountDTO">
        SELECT
            <if test="statisticType == 1">
                cs.advisor_dept_id as deptId,
            </if>
            <if test="statisticType == 2">
                cs.accounting_dept_id as deptId,
            </if>
            COUNT(distinct cs.id) as periodCount
        FROM c_customer_service cs
        JOIN sys_business_log bl ON cs.id = bl.business_id
        WHERE
            bl.business_type = 1 AND cs.is_del = 0
          <if test="operType == 1">
              and bl.oper_type like '重启服务%' and (cs.end_account_period is null or cs.end_account_period &gt;= #{period})
          </if>
          <if test="operType == 2">
              and bl.oper_type like '结束服务%' and cs.end_account_period is not null and cs.end_account_period &lt;= #{period}
          </if>
          AND bl.create_time BETWEEN #{startTime} AND #{endTime}
        <if test="statisticType == 1">
            and cs.advisor_dept_id is not null
        </if>
        <if test="statisticType == 2">
            and cs.accounting_dept_id is not null
        </if>
        GROUP BY
        <if test="statisticType == 1">
            cs.advisor_dept_id
        </if>
        <if test="statisticType == 2">
            cs.accounting_dept_id
        </if>
    </select>
    <select id="selectNewPeriodCountByDeptTypeAndPeriod"
            resultType="com.bxm.customer.domain.dto.workBench.ManagerCommonCountDTO">
        SELECT
        <if test="statisticType == 1">
            cs.advisor_dept_id as deptId,
        </if>
        <if test="statisticType == 2">
            cs.accounting_dept_id as deptId,
        </if>
        COUNT(1) as periodCount FROM c_customer_service cs
        WHERE cs.is_del = 0
        <if test="statisticType == 1">
            AND cs.advisor_dept_id IS NOT NULL
        </if>
        <if test="statisticType == 2">
            AND cs.accounting_dept_id IS NOT NULL
        </if>
        AND cs.first_account_period = #{period}

        GROUP BY
        <if test="statisticType == 1">
            cs.advisor_dept_id
        </if>
        <if test="statisticType == 2">
            cs.accounting_dept_id
        </if>
    </select>
    <select id="selectValidCountByPeriods"
            resultType="com.bxm.customer.domain.dto.workBench.ManagerCommonCountDTO">
        SELECT
        ccspm.period,COUNT(distinct ccspm.id) as periodCount FROM c_customer_service_period_month ccspm
        join c_customer_service cs on ccspm.customer_service_id = cs.id
        WHERE cs.is_del = 0
        and ccspm.service_type = 1
        and ccspm.period in
        <foreach collection="periods" separator="," item="period" close=")" open="(">
            #{period}
        </foreach>
        <if test="statisticType == 1">
            AND ccspm.advisor_dept_id IS NOT NULL
        </if>
        <if test="statisticType == 2">
            AND ccspm.accounting_dept_id IS NOT NULL
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="statisticType == 1">
                and (
                ccspm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                ccspm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (
                ccspm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                ccspm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="statisticType == 1">
                and (
                ccspm.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                ccspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="statisticType == 2">
                and (
                ccspm.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                ccspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        GROUP BY ccspm.period
    </select>
    <select id="selectOperCountByTime"
            resultType="com.bxm.customer.domain.dto.workBench.ManagerCommonCountDTO">
        SELECT
        COUNT(distinct cs.id) as periodCount
        FROM c_customer_service cs
        JOIN sys_business_log bl ON cs.id = bl.business_id
        WHERE
        bl.business_type = 1 AND cs.is_del = 0
        <if test="operType == 1">
            and bl.oper_type like '重启服务%' and (cs.end_account_period is null or cs.end_account_period &gt;= #{period})
        </if>
        <if test="operType == 2">
            and bl.oper_type like '结束服务%' and cs.end_account_period is not null and cs.end_account_period &lt;= #{period}
        </if>
        AND bl.create_time BETWEEN #{startTime} AND #{endTime}
        <if test="statisticType == 1">
            AND cs.advisor_dept_id IS NOT NULL
        </if>
        <if test="statisticType == 2">
            AND cs.accounting_dept_id IS NOT NULL
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="statisticType == 1">
                and (
                cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (
                cs.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="statisticType == 1">
                and (
                cs.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="statisticType == 2">
                and (
                cs.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
    </select>
    <select id="selectNewPeriodCountByPeriod"
            resultType="com.bxm.customer.domain.dto.workBench.ManagerCommonCountDTO">
        SELECT
        COUNT(1) as periodCount
        FROM c_customer_service cs
        WHERE cs.is_del = 0
        <if test="statisticType == 1">
            AND cs.advisor_dept_id IS NOT NULL
        </if>
        <if test="statisticType == 2">
            AND cs.accounting_dept_id IS NOT NULL
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="statisticType == 1">
                and (
                cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (
                cs.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="statisticType == 1">
                and (
                cs.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="statisticType == 2">
                and (
                cs.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        AND cs.first_account_period = #{period}
    </select>
    <select id="selectValidListByPeriod"
            resultType="com.bxm.customer.domain.dto.workBench.CustomerServicePeriodMonthMiniListDTO">
        SELECT
        ccspm.customer_service_id as customerServiceId,
        ccspm.id as customerServicePeriodMonthId,
        ccspm.customer_name as customerName,
        ccspm.business_dept_id as businessDeptId,
        cs.customer_company_name as customerCompanyName,
        ccspm.credit_code as creditCode,
        ccspm.tax_type as taxType
        FROM c_customer_service_period_month ccspm
        join c_customer_service cs on ccspm.customer_service_id = cs.id
        WHERE cs.is_del = 0
        and ccspm.service_type = 1
        and ccspm.period = #{period}
        <if test="statisticType == 1">
            AND ccspm.advisor_dept_id IS NOT NULL
        </if>
        <if test="statisticType == 2">
            AND ccspm.accounting_dept_id IS NOT NULL
        </if>
        <if test="tagIncludeFlag != null and tagIncludeFlag == 1">
            <if test="ids != null and ids.size > 0">
                and ccspm.id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="tagIncludeFlag != null and tagIncludeFlag == 0">
            <if test="ids != null and ids.size > 0">
                and ccspm.id not in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            and (ccspm.customer_name like concat('%',#{vo.customerName},'%') or cs.customer_company_name like concat('%',#{vo.customerName},'%') or cs.credit_code like concat('%',#{vo.customerName},'%'))
        </if>
        <if test="vo.taxType != null">
            and ccspm.tax_type = #{vo.taxType}
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="statisticType == 1">
                and (
                ccspm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                ccspm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (
                ccspm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                ccspm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="statisticType == 1">
                and (
                ccspm.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                ccspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="statisticType == 2">
                and (
                ccspm.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                ccspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        order by ccspm.id desc
    </select>
    <select id="selectOperListByOperTypeAndTime"
            resultType="com.bxm.customer.domain.dto.workBench.CustomerServicePeriodMonthMiniListDTO">
        SELECT
            distinct
        cs.id as customerServiceId,
        cs.customer_name as customerName,
        cs.business_dept_id as businessDeptId,
        cs.customer_company_name as customerCompanyName,
        cs.credit_code as creditCode,
        cs.tax_type as taxType
        FROM c_customer_service cs
        JOIN sys_business_log bl ON cs.id = bl.business_id
        WHERE
        bl.business_type = 1 AND cs.is_del = 0
        <if test="operType == 1">
            and bl.oper_type like '重启服务%' and (cs.end_account_period is null or cs.end_account_period &gt;= #{period})
        </if>
        <if test="operType == 2">
            and bl.oper_type like '结束服务%' and cs.end_account_period is not null and cs.end_account_period &lt;= #{period}
        </if>
        AND bl.create_time BETWEEN #{startTime} AND #{endTime}
        <if test="statisticType == 1">
            AND cs.advisor_dept_id IS NOT NULL
        </if>
        <if test="statisticType == 2">
            AND cs.accounting_dept_id IS NOT NULL
        </if>
        <if test="tagIncludeFlag != null and tagIncludeFlag == 1">
            <if test="ids != null and ids.size > 0">
                and cs.id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="tagIncludeFlag != null and tagIncludeFlag == 0">
            <if test="ids != null and ids.size > 0">
                and cs.id not in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            and (cs.customer_name like concat('%',#{vo.customerName},'%') or cs.customer_company_name like concat('%',#{vo.customerName},'%') or cs.credit_code like concat('%',#{vo.customerName},'%'))
        </if>
        <if test="vo.taxType != null">
            and cs.tax_type = #{vo.taxType}
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="statisticType == 1">
                and (
                cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (
                cs.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="statisticType == 1">
                and (
                cs.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="statisticType == 2">
                and (
                cs.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
    </select>
    <select id="selectNewPeriodListByPeriod"
            resultType="com.bxm.customer.domain.dto.workBench.CustomerServicePeriodMonthMiniListDTO">
        SELECT
        cs.id as customerServiceId,
        cs.customer_name as customerName,
        cs.business_dept_id as businessDeptId,
        cs.customer_company_name as customerCompanyName,
        cs.credit_code as creditCode,
        cs.tax_type as taxType
        FROM
        c_customer_service cs
        WHERE cs.is_del = 0
        <if test="statisticType == 1">
            AND cs.advisor_dept_id IS NOT NULL
        </if>
        <if test="statisticType == 2">
            AND cs.accounting_dept_id IS NOT NULL
        </if>
        <if test="tagIncludeFlag != null and tagIncludeFlag == 1">
            <if test="ids != null and ids.size > 0">
                and cs.id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="tagIncludeFlag != null and tagIncludeFlag == 0">
            <if test="ids != null and ids.size > 0">
                and cs.id not in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            and (cs.customer_name like concat('%',#{vo.customerName},'%') or cs.customer_company_name like concat('%',#{vo.customerName},'%') or cs.credit_code like concat('%',#{vo.customerName},'%'))
        </if>
        <if test="vo.taxType != null">
            and cs.tax_type = #{vo.taxType}
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="statisticType == 1">
                and (
                cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (
                cs.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="statisticType == 1">
                and (
                cs.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="statisticType == 2">
                and (
                cs.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cs.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        AND cs.first_account_period = #{period}
    </select>
    <select id="customerServicePeriodMonthAccountingDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
            from c_customer_service_period_month
        <where>
            c_customer_service_period_month
        </where>
    </select>
    <select id="customerServicePeriodMonthAdvisorDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">

    </select>
    <select id="accountingDeptCountWithServiceStatusInAuth"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select c_customer_service_period_month.accounting_dept_id as deptId,
               count(1) as dataCount
        from c_customer_service_period_month
        <where>
            c_customer_service_period_month.accounting_dept_id is not null
            <if test="userDept.isAdmin == null or userDept.isAdmin == 0">
                <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                    and 1 = -1
                </if>
                <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.startPeriod != null">
                and c_customer_service_period_month.`period` &gt;= #{vo.startPeriod}
            </if>
            <if test="vo.endPeriod != null">
                and c_customer_service_period_month.`period` &lt;= #{vo.endPeriod}
            </if>
        </where>
        group by c_customer_service_period_month.accounting_dept_id
    </select>
    <select id="advisorDeptCountWithServiceStatusInAuth"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select c_customer_service_period_month.advisor_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service_period_month
        <where>
            c_customer_service_period_month.advisor_dept_id is not null
            <if test="userDept.isAdmin == null or userDept.isAdmin == 0">
                <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                    and 1 = -1
                </if>
                <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.startPeriod != null">
                and c_customer_service_period_month.`period` &gt;= #{vo.startPeriod}
            </if>
            <if test="vo.endPeriod != null">
                and c_customer_service_period_month.`period` &lt;= #{vo.endPeriod}
            </if>
        </where>
        group by c_customer_service_period_month.advisor_dept_id
    </select>
    <select id="selectMinPeriodByCustomerServiceIds" resultType="java.lang.Integer">
        select
            min(period)
        from c_customer_service_period_month where customer_service_id in
        <foreach collection="customerServiceIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="listWithServiceStatusInAuthV2"
            resultType="com.bxm.customer.domain.dto.CustomerServicePeriodMonthDTO">
        select c_customer_service_period_month.* from c_customer_service_period_month
            <if test="vo.customerServiceTaxType != null">
            join c_customer_service on c_customer_service_period_month.customer_service_id = c_customer_service.id and c_customer_service.is_del = 0
            </if>
        <if test="vo.accountingCashierInAccountStatus != null and vo.accountingCashierInAccountStatus != ''"> left join c_customer_service_cashier_accounting on c_customer_service_period_month.id = c_customer_service_cashier_accounting.customer_service_period_month_id and c_customer_service_cashier_accounting.`type` = 1 and c_customer_service_cashier_accounting.is_del = 0 </if>
        <if test="vo.preAuthStatus != null and vo.preAuthStatus != ''"> left join c_customer_deliver ccd5 on c_customer_service_period_month.id = ccd5.customer_service_period_month_id and ccd5.is_del = 0 and ccd5.deliver_type = 5 </if>
        <if test="vo.medicalInsuranceStatus != null and vo.medicalInsuranceStatus != ''"> left join c_customer_deliver ccd1 on c_customer_service_period_month.id = ccd1.customer_service_period_month_id and ccd1.is_del = 0 and ccd1.deliver_type = 1 </if>
        <if test="vo.socialInsuranceStatus != null and vo.socialInsuranceStatus != ''"> left join c_customer_deliver ccd2 on c_customer_service_period_month.id = ccd2.customer_service_period_month_id and ccd2.is_del = 0 and ccd2.deliver_type = 2 </if>
        <if test="vo.taxStatus != null and vo.taxStatus != ''"> left join c_customer_deliver ccd3 on c_customer_service_period_month.id = ccd3.customer_service_period_month_id and ccd3.is_del = 0 and ccd3.deliver_type = 3 </if>
        <if test="vo.taxOperatingStatus != null and vo.taxOperatingStatus != ''"> left join c_customer_deliver ccd6 on c_customer_service_period_month.id = ccd6.customer_service_period_month_id and ccd6.is_del = 0 and ccd6.deliver_type = 6 </if>
        <if test="vo.nationalTaxStatus != null and vo.nationalTaxStatus != ''"> left join c_customer_deliver ccd4 on c_customer_service_period_month.id = ccd4.customer_service_period_month_id and ccd4.is_del = 0 and ccd4.deliver_type = 4 </if>
        <where>
            <if test="userDept.isAdmin == null or userDept.isAdmin == 0">
                <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                    and 1 = -1
                </if>
                <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.serviceType != null">
                and c_customer_service_period_month.service_type = #{vo.serviceType}
            </if>
            <if test="vo.settlementStatus != null">
                <if test="vo.settlementStatus == 4">
                    and c_customer_service_period_month.settlement_status in (4,5,6)
                </if>
                <if test="vo.settlementStatus != 4">
                    and c_customer_service_period_month.settlement_status = #{vo.settlementStatus}
                </if>
            </if>
            <if test="vo.serviceStatus != null">
                and c_customer_service_period_month.service_status = #{vo.serviceStatus}
            </if>
            <if test="tagIncludeFlag != null">
                <if test="tagIncludeFlag == 0">
                    and c_customer_service_period_month.id not in
                    <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeFlag == 1">
                    and c_customer_service_period_month.id in
                    <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="customerServiceTagIncludeFlag != null">
                <if test="customerServiceTagIncludeFlag == 0">
                    and c_customer_service_period_month.customer_service_id not in
                    <foreach collection="customerServiceIdForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="customerServiceTagIncludeFlag == 1">
                    and c_customer_service_period_month.customer_service_id in
                    <foreach collection="customerServiceIdForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="searchIds != null and searchIds.size > 0">
                and c_customer_service_period_month.id in
                <foreach collection="searchIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_customer_service_period_month.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_customer_service_period_month.credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and c_customer_service_period_month.customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and c_customer_service_period_month.service_number like concat('%', #{vo.serviceNumber}, '%')
            </if>
            <if test="vo.startPeriod != null">
                and c_customer_service_period_month.`period` &gt;= #{vo.startPeriod}
            </if>
            <if test="vo.endPeriod != null">
                and c_customer_service_period_month.`period` &lt;= #{vo.endPeriod}
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.customerServiceTaxType != null">
                and c_customer_service.tax_type = #{vo.customerServiceTaxType}
            </if>
            <if test="vo.businessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.accountingTopDeptId != null">
                and c_customer_service_period_month.accounting_top_dept_id = #{vo.accountingTopDeptId}
            </if>
            <if test="vo.advisorDeptId != null">
                and c_customer_service_period_month.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.accountingStatus != null">
                and c_customer_service_period_month.accounting_status = #{vo.accountingStatus}
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and c_customer_service_period_month.customer_service_id in
                <foreach collection="customerServiceIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.prepayStatus != null">
                and c_customer_service_period_month = #{vo.prepayStatus}
            </if>
            <if test="vo.bankPaymentResult != null and vo.bankPaymentResult != ''">
                and c_customer_service_period_month.bank_payment_result in (${vo.bankPaymentResult})
            </if>
            <if test="vo.accountingCashierInAccountStatus != null and vo.accountingCashierInAccountStatus != ''">
                and c_customer_service_cashier_accounting.deliver_status in (${vo.accountingCashierInAccountStatus})
            </if>
            <if test="vo.settleAccountStatus != null and vo.settleAccountStatus != ''">
                and c_customer_service_period_month.settle_account_status in (${vo.settleAccountStatus})
            </if>
            <if test="vo.preAuthStatus != null and vo.preAuthStatus != ''">
                and (ccd5.`status` in (${preAuthStatus})
                <if test='vo.preAuthStatus.contains("-1")'>
                    or (c_customer_service_period_month.service_type = 1 and ccd5.id is null and c_customer_service_period_month.tax_type = 2)
                </if>
                <if test='vo.preAuthStatus.contains("-2")'>
                    or (ccd5.id is null and (c_customer_service_period_month.service_type != 1 or c_customer_service_period_month.tax_type != 2))
                </if>
                )
            </if>
            <if test="vo.medicalInsuranceStatus != null and vo.medicalInsuranceStatus != ''">
                and (ccd1.`status` in (${vo.medicalInsuranceStatus})
                <if test='vo.medicalInsuranceStatus.contains("-1")'>
                    or (c_customer_service_period_month.service_type = 1 and ccd1.id is null
                    and c_customer_service_period_month.id in (select business_id from c_business_tag_relation where tag_id = 2 and business_type = 2))
                </if>
                <if test='vo.medicalInsuranceStatus.contains("-2")'>
                    or (ccd1.id is null and (c_customer_service_period_month.service_type != 1
                    or c_customer_service_period_month.id not in (select business_id from c_business_tag_relation where tag_id = 2 and business_type = 2)))
                </if>
                )
            </if>
            <if test="vo.socialInsuranceStatus != null and vo.socialInsuranceStatus != ''">
                and (ccd2.`status` in (${vo.socialInsuranceStatus})
                <if test='vo.socialInsuranceStatus.contains("-1")'>
                    or (c_customer_service_period_month.service_type = 1 and ccd2.id is null
                    and c_customer_service_period_month.id in (select business_id from c_business_tag_relation where tag_id = 3 and business_type = 2))
                </if>
                <if test='vo.socialInsuranceStatus.contains("-2")'>
                    or (ccd2.id is null and (c_customer_service_period_month.service_type != 1
                    or c_customer_service_period_month.id not in (select business_id from c_business_tag_relation where tag_id = 3 and business_type = 2)))
                </if>
                )
            </if>
            <if test="vo.taxStatus != null and vo.taxStatus != ''">
                and (ccd3.`status` in (${vo.taxStatus})
                <if test='vo.taxStatus.contains("-1")'>
                    or (c_customer_service_period_month.service_type = 1 and ccd3.id is null
                    and (
                    ((mod(c_customer_service_period_month.period, 100) = 3 or mod(c_customer_service_period_month.period, 100) = 6 or mod(c_customer_service_period_month.period, 100) = 9 or mod(c_customer_service_period_month.period, 100) = 12) and c_customer_service_period_month.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type in (1, 2)))
                    or
                    (mod(c_customer_service_period_month.period, 100) != 3 and mod(c_customer_service_period_month.period, 100) != 6 and mod(c_customer_service_period_month.period, 100) != 9 and mod(c_customer_service_period_month.period, 100) != 12 and c_customer_service_period_month.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type = 1))
                    ))
                </if>
                <if test='vo.taxStatus.contains("-2")'>
                    or (ccd3.id is null and (c_customer_service_period_month.service_type != 1 or
                    ((mod(c_customer_service_period_month.period, 100) = 3 or mod(c_customer_service_period_month.period, 100) = 6 or mod(c_customer_service_period_month.period, 100) = 9 or mod(c_customer_service_period_month.period, 100) = 12) and c_customer_service_period_month.id not in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type in (1, 2)))
                        or ((mod(c_customer_service_period_month.period, 100) != 3 and mod(c_customer_service_period_month.period, 100) != 6 and mod(c_customer_service_period_month.period, 100) != 9 and mod(c_customer_service_period_month.period, 100) != 12) and c_customer_service_period_month.id not in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type = 1)))
                    )
                </if>
                )
            </if>
            <if test="vo.taxOperatingStatus != null and vo.taxOperatingStatus != ''">
                and (ccd6.`status` in (${vo.taxOperatingStatus})
                <if test='vo.taxOperatingStatus.contains("-1")'>
                    or (c_customer_service_period_month.service_type = 1 and ccd6.id is null
                    and (
                    ((mod(c_customer_service_period_month.period, 100) = 3 or mod(c_customer_service_period_month.period, 100) = 6 or mod(c_customer_service_period_month.period, 100) = 9 or mod(c_customer_service_period_month.period, 100) = 12) and c_customer_service_period_month.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type in (1, 2)))
                    or
                    (mod(c_customer_service_period_month.period, 100) != 3 and mod(c_customer_service_period_month.period, 100) != 6 and mod(c_customer_service_period_month.period, 100) != 9 and mod(c_customer_service_period_month.period, 100) != 12 and c_customer_service_period_month.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type = 1))
                    ))
                </if>
                <if test='vo.taxOperatingStatus.contains("-2")'>
                    or (ccd6.id is null and (c_customer_service_period_month.service_type != 1 or
                    ((mod(c_customer_service_period_month.period, 100) = 3 or mod(c_customer_service_period_month.period, 100) = 6 or mod(c_customer_service_period_month.period, 100) = 9 or mod(c_customer_service_period_month.period, 100) = 12) and c_customer_service_period_month.id not in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type in (1, 2)))
                    or ((mod(c_customer_service_period_month.period, 100) != 3 and mod(c_customer_service_period_month.period, 100) != 6 and mod(c_customer_service_period_month.period, 100) != 9 and mod(c_customer_service_period_month.period, 100) != 12) and c_customer_service_period_month.id not in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type = '个人所得税' and report_type = 1)))
                    )
                </if>
                )
            </if>
            <if test="vo.nationalTaxStatus != null and vo.nationalTaxStatus != ''">
                and (ccd4.`status` in (${vo.nationalTaxStatus})
                <if test='vo.nationalTaxStatus.contains("-1")'>
                    or (c_customer_service_period_month.service_type = 1 and ccd4.id is null
                    and (
                    ((mod(c_customer_service_period_month.period, 100) = 3 or mod(c_customer_service_period_month.period, 100) = 6 or mod(c_customer_service_period_month.period, 100) = 9 or mod(c_customer_service_period_month.period, 100) = 12) and 1 = 1)
                    or
                    (mod(c_customer_service_period_month.period, 100) != 3 and mod(c_customer_service_period_month.period, 100) != 6 and mod(c_customer_service_period_month.period, 100) != 9 and mod(c_customer_service_period_month.period, 100) != 12 and c_customer_service_period_month.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type != '个人所得税' and tax_type != '个人所得税' and report_type = 1))
                    ))
                </if>
                <if test='vo.nationalTaxStatus.contains("-2")'>
                    or (ccd4.id is null and (c_customer_service_period_month.service_type != 1 or
                        ((mod(c_customer_service_period_month.period, 100) != 3 and mod(c_customer_service_period_month.period, 100) != 6 and mod(c_customer_service_period_month.period, 100) != 9 and mod(c_customer_service_period_month.period, 100) != 12) and c_customer_service_period_month.id in (select distinct customer_service_period_month_id from c_customer_service_period_month_tax_type_check where tax_type in ('个人所得税','个人所得税') and report_type = 1))
                    ))
                </if>
                )
            </if>
        </where>
        order by c_customer_service_period_month.`period` desc, c_customer_service_period_month.customer_service_id desc
    </select>

    <select id="listWithServiceStatusInAuthV3"
            resultType="com.bxm.customer.domain.dto.CustomerServicePeriodMonthDTO">
        select c_customer_service_period_month.* from c_customer_service_period_month
        <if test="vo.customerServiceTaxType != null">
            join c_customer_service on c_customer_service_period_month.customer_service_id = c_customer_service.id and c_customer_service.is_del = 0
        </if>
        <where>
            <if test="userDept.isAdmin == null or userDept.isAdmin == 0">
                <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                    and 1 = -1
                </if>
                <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.serviceType != null">
                and c_customer_service_period_month.service_type = #{vo.serviceType}
            </if>
            <if test="vo.settlementStatus != null">
                <if test="vo.settlementStatus == 4">
                    and c_customer_service_period_month.settlement_status in (4,5,6)
                </if>
                <if test="vo.settlementStatus != 4">
                    and c_customer_service_period_month.settlement_status = #{vo.settlementStatus}
                </if>
            </if>
            <if test="vo.serviceStatus != null">
                and c_customer_service_period_month.service_status = #{vo.serviceStatus}
            </if>
            <if test="tagIncludeFlag != null">
                <if test="tagIncludeFlag == 0">
                    <if test="periodMonthIdsForTag != null and periodMonthIdsForTag.size > 0">
                        and c_customer_service_period_month.id not in
                        <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagIncludeFlag == 1">
                    and c_customer_service_period_month.id in
                    <foreach collection="periodMonthIdsForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="customerServiceTagIncludeFlag != null">
                <if test="customerServiceTagIncludeFlag == 0">
                    <if test="customerServiceIdForTag != null and customerServiceIdForTag.size > 0">
                        and c_customer_service_period_month.customer_service_id not in
                        <foreach collection="customerServiceIdForTag" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="customerServiceTagIncludeFlag == 1">
                    and c_customer_service_period_month.customer_service_id in
                    <foreach collection="customerServiceIdForTag" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_customer_service_period_month.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_customer_service_period_month.credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and c_customer_service_period_month.customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and c_customer_service_period_month.service_number like concat('%', #{vo.serviceNumber}, '%')
            </if>
            <if test="vo.startPeriod != null">
                and c_customer_service_period_month.`period` &gt;= #{vo.startPeriod}
            </if>
            <if test="vo.endPeriod != null">
                and c_customer_service_period_month.`period` &lt;= #{vo.endPeriod}
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.customerServiceTaxType != null">
                and c_customer_service.tax_type = #{vo.customerServiceTaxType}
            </if>
            <if test="vo.businessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.accountingTopDeptId != null">
                and c_customer_service_period_month.accounting_top_dept_id = #{vo.accountingTopDeptId}
            </if>
            <if test="vo.businessDeptIdList != null and vo.businessDeptIdList != ''">
                and c_customer_service_period_month.business_dept_id in (${vo.businessDeptIdList})
            </if>
            <if test="vo.accountingTopDeptIdList != null and vo.accountingTopDeptIdList != ''">
                and c_customer_service_period_month.accounting_top_dept_id in (${vo.accountingTopDeptIdList})
            </if>
            <if test="vo.advisorDeptId != null">
                and c_customer_service_period_month.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.accountingStatus != null">
                and c_customer_service_period_month.accounting_status = #{vo.accountingStatus}
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and c_customer_service_period_month.customer_service_id in
                <foreach collection="customerServiceIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.prepayStatus != null">
                and c_customer_service_period_month.prepay_status = #{vo.prepayStatus}
            </if>
            <if test="vo.bankPaymentResult != null and vo.bankPaymentResult != ''">
                and c_customer_service_period_month.bank_payment_result in (${vo.bankPaymentResult})
            </if>
            <if test="vo.accountingCashierInAccountStatus != null and vo.accountingCashierInAccountStatus != ''">
                and c_customer_service_period_month.in_account_status in (${vo.accountingCashierInAccountStatus})
            </if>
            <if test="vo.settleAccountStatus != null and vo.settleAccountStatus != ''">
                and c_customer_service_period_month.settle_account_status in (${vo.settleAccountStatus})
            </if>
            <if test="vo.medicalInsuranceStatus != null and vo.medicalInsuranceStatus != ''">
                and c_customer_service_period_month.medical_deliver_status in (${vo.medicalInsuranceStatus})
            </if>
            <if test="vo.socialInsuranceStatus != null and vo.socialInsuranceStatus != ''">
                and c_customer_service_period_month.social_deliver_status in (${vo.socialInsuranceStatus})
            </if>
            <if test="vo.taxStatus != null and vo.taxStatus != ''">
                and c_customer_service_period_month.person_tax_deliver_status in (${vo.taxStatus})
            </if>
            <if test="vo.taxOperatingStatus != null and vo.taxOperatingStatus != ''">
                and c_customer_service_period_month.operation_tax_deliver_status in (${vo.taxOperatingStatus})
            </if>
            <if test="vo.nationalTaxStatus != null and vo.nationalTaxStatus != ''">
                and c_customer_service_period_month.national_tax_deliver_status in (${vo.nationalTaxStatus})
            </if>
            <if test="vo.preAuthStatus != null and vo.preAuthStatus != ''">
                and c_customer_service_period_month.pre_auth_deliver_status in (${vo.preAuthStatus})
            </if>
        </where>
        order by c_customer_service_period_month.`period` desc, c_customer_service_period_month.customer_service_id desc
    </select>
    <select id="selectBankPaymentResultCount" resultType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM
        c_customer_service_period_month spm
        JOIN
        c_customer_service cs
        ON
        spm.customer_service_id = cs.id AND cs.is_del = 0
        LEFT JOIN
        c_customer_service_bank_account bank
        ON
        spm.customer_service_id = bank.customer_service_id
        AND (bank.account_open_date IS NULL OR spm.period &gt;= DATE_FORMAT(bank.account_open_date, '%Y%m'))
        AND (bank.account_close_date IS NULL OR spm.period &lt;= DATE_FORMAT(bank.account_close_date, '%Y%m'))
        WHERE
            NOT EXISTS (
            SELECT 1
            FROM c_customer_service_cashier_accounting cca
            WHERE cca.customer_service_period_month_id = spm.id
            AND cca.is_del = 0
            AND cca.type = 2
            AND cca.bank_account_number = bank.bank_account_number
            )
        and spm.bank_payment_result = #{bankPaymentResult}
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (cs.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or cs.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="selectBankWaitCreateCount"
            resultType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM
        c_customer_service_period_month spm
        LEFT JOIN
        c_customer_service_bank_account bank
        ON
        spm.customer_service_id = bank.customer_service_id
        AND (bank.account_open_date IS NULL OR spm.period &gt;= DATE_FORMAT(bank.account_open_date, '%Y%m'))
        AND (bank.account_close_date IS NULL OR spm.period &lt;= DATE_FORMAT(bank.account_close_date, '%Y%m'))
        WHERE
            bank.id is not null and
        NOT EXISTS (
        SELECT 1
        FROM c_customer_service_cashier_accounting cca
        WHERE cca.customer_service_period_month_id = spm.id
        AND cca.is_del = 0
        AND cca.type = 2
        AND cca.bank_account_number = bank.bank_account_number
        )
        and spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="selectInAccountWaitCreateCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            c_customer_service_period_month spm
        WHERE spm.in_account_status = 0 and spm.period >= 202401 and spm.period &lt; #{nowPeriod}
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="inAccountMiniList" resultType="com.bxm.customer.domain.dto.workBench.InAccountMiniListDTO">
        select
            cs.id as customerServiceId,
            spm.id as customerServicePeriodMonthId,
            cs.customer_name as customerName,
            spm.period as period,
            spm.bank_payment_result as bankPaymentResult,
            spm.in_account_status as inAccountResult,
            spm.settle_account_status as settleAccountResult
            from c_customer_service_period_month spm
        join c_customer_service cs
        on spm.customer_service_id = cs.id and cs.is_del = 0
        <where>
            spm.accounting_status = 1
            <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                <if test="userDeptDTO.deptType == 1">
                    and (spm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 2">
                    and (spm.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                and (spm.advisor_dept_id in
                <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or spm.accounting_dept_id in
                <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="customerName != null and customerName != ''">
                and (cs.customer_name like concat('%',#{customerName},'%') or cs.credit_code = #{customerName})
            </if>
            <if test="miniListType != null">
                <if test="miniListType == 1">
                    and spm.settle_account_status = 1
                </if>
                <if test="miniListType == 2">
                    and spm.settle_account_status &lt;= 2
                </if>
                <if test="miniListType == 3">
                    and spm.settle_account_status = 1
                </if>
                <if test="miniListType == 4">
                    and spm.settle_account_status &lt;= 2
                </if>
            </if>
            <if test="period != null">
                and spm.period = #{period}
            </if>
            <if test="statisticTaxType != null">
                <if test="statisticTaxType == 3">
                    AND EXISTS (
                    SELECT 1
                    FROM c_business_tag_relation where tag_id = 1 and business_type = 2 AND business_id = spm.id
                    )
                </if>
                <if test="statisticTaxType != 3">
                    and spm.tax_type = #{statisticTaxType}
                    AND not exists (
                    SELECT 1
                    FROM c_business_tag_relation where tag_id = 1 and business_type = 2 AND business_id = spm.id
                    )
                </if>
            </if>
        </where>
        order by spm.period desc,spm.id desc
    </select>
    <select id="selectBankPatrialMisssCount" resultType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM
        c_customer_service_period_month spm
        WHERE
        spm.bank_payment_result in (1, 2)
          and spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="selectAccountingTopInfoByIds"
            resultType="com.bxm.customer.domain.dto.AccountingTopInfoSourceDTO">
        select
        c_customer_service_period_month.id as customerServicePeriodMonthId,
        sd1.dept_name as periodAccountingTopDeptName,
        sd2.dept_name as customerServiceAccountingTopDeptName
        from c_customer_service_period_month
            join c_customer_service on c_customer_service_period_month.customer_service_id = c_customer_service.id and c_customer_service.is_del = 0
        left join sys_dept sd1 on sd1.dept_id = c_customer_service_period_month.accounting_top_dept_id
        left join sys_dept sd2 on sd2.dept_id = c_customer_service.accounting_top_dept_id
        where c_customer_service_period_month.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectBatchByCustomerServiceIdAndPeriod"
            resultType="com.bxm.customer.domain.CustomerServicePeriodMonth">
        select * from c_customer_service_period_month
        where
            <if test="customerPeriodList != null and customerPeriodList.size > 0">
                (customer_service_id, period) in
                <foreach collection="customerPeriodList" item="item" open="(" close=")" separator=",">
                    (#{item.customerServiceId},#{item.maxPeriod})
                </foreach>
            </if>
            <if test="customerPeriodList == null or customerPeriodList.size == 0">
                1 = 0
            </if>
    </select>
    <select id="selectNoIncomePeriod" resultType="com.bxm.customer.domain.CustomerServicePeriodMonth">
        SELECT
            *
        FROM c_customer_service_period_month cspm JOIN c_customer_service_period_month_income cspmi ON cspm.customer_service_id = cspmi.customer_service_id  AND cspm.period = cspmi.period
        WHERE
            cspm.period = #{period}
          AND EXISTS(
            SELECT 1 FROM c_business_tag_relation WHERE business_type = 2 AND business_id = cspm.id AND tag_id IN (1, 13)
        )
          AND cspmi.all_ticket_amount + cspmi.no_ticket_income_amount != 0
    </select>
    <select id="selectNoAdvisorCount" resultType="java.lang.Long">
        SELECT COUNT(ccspm.id)
        FROM c_customer_service_period_month ccspm JOIN c_customer_service cs ON ccspm.customer_service_id = cs.id AND cs.is_del = 0
        WHERE ccspm.period >= 202401 and ccspm.advisor_dept_id IS NULL AND cs.advisor_dept_id IS NOT NULL
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            and ccspm.business_dept_id in
            <foreach collection="userDept.deptIds" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectNoAccountingCount" resultType="java.lang.Long">
        SELECT COUNT(ccspm.id)
        FROM c_customer_service_period_month ccspm JOIN c_customer_service cs ON ccspm.customer_service_id = cs.id AND cs.is_del = 0
        WHERE ccspm.period >= 202401 and ccspm.accounting_dept_id IS NULL AND cs.accounting_dept_id IS NOT NULL
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            and ccspm.accounting_top_dept_id in
            <foreach collection="userDept.deptIds" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="periodNoAccountingNoAdvisorPageList"
            resultType="com.bxm.customer.domain.dto.workBench.CustomerServicePeriodMonthSimpleDTO">
        select
            ccspm.customer_service_id as customerServiceId,
            ccspm.id as customerServicePeriodMonthId,
            ccspm.customer_name as customerName,
            ccspm.credit_code as creditCode,
            ccspm.period as period,
            ccspm.business_dept_id as businessDeptId,
            ccspm.accounting_top_dept_id as accountingTopDeptId,
            cs.service_status as serviceStatus,
            ccspm.business_top_dept_id as businessTopDeptId,
            cs.accounting_dept_id as customerServiceAccountingDeptId,
            cs.advisor_dept_id as customerServiceAdvisorDeptId
            from c_customer_service_period_month ccspm JOIN c_customer_service cs ON ccspm.customer_service_id = cs.id AND cs.is_del = 0
        <where>
            ccspm.period >= 202401
            <if test="vo.type == 1">
                and ccspm.accounting_dept_id IS NULL AND cs.accounting_dept_id IS NOT NULL
            </if>
            <if test="vo.type == 2">
                and ccspm.advisor_dept_id IS NULL AND cs.advisor_dept_id IS NOT NULL
            </if>
            <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                <if test="vo.type == 1">
                    and ccspm.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="item" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                </if>
                <if test="vo.type == 2">
                    and ccspm.business_dept_id in
                    <foreach collection="userDept.deptIds" item="item" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                ccspm.customer_name like concat('%', #{vo.keyWord}, '%')
                or ccspm.credit_code = #{vo.keyWord}
                )
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and ccspm.customer_service_id in
                <foreach collection="batchSearchCustomerServiceIds" separator="," item="customerServiceId" close=")" open="(">
                    #{customerServiceId}
                </foreach>
            </if>
        </where>
        order by ccspm.id desc
    </select>
    <select id="selectBankWaitCreateList"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierWaitCreateStatisticDTO">
        SELECT
        spm.id as customerServicePeriodMonthId,
        bank.id as bankId,
        bank.receipt_status as receiptStatus,
        bank.bank_direct as bankDirect
        FROM
        c_customer_service_period_month spm
        LEFT JOIN
        c_customer_service_bank_account bank
        ON
        spm.customer_service_id = bank.customer_service_id
        AND (bank.account_open_date IS NULL OR spm.period &gt;= DATE_FORMAT(bank.account_open_date, '%Y%m'))
        AND (bank.account_close_date IS NULL OR spm.period &lt;= DATE_FORMAT(bank.account_close_date, '%Y%m'))
        WHERE
        bank.id is not null and
        NOT EXISTS (
        SELECT 1
        FROM c_customer_service_cashier_accounting cca
        WHERE cca.customer_service_period_month_id = spm.id
        AND cca.is_del = 0
        AND cca.type = 2
        AND cca.bank_account_number = bank.bank_account_number
        )
        and spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <insert id="saveNewPeriod">
        insert into c_customer_service_period_month
        select null,id,${nowPeriod},'',now(),'',now(),business_dept_id, business_top_dept_id,advisor_dept_id,advisor_top_dept_id,accounting_dept_id,accounting_top_dept_id,
               if(service_status = 3, 3, 1), customer_name,credit_code, tax_number, tax_type, service_number, 1,1, null, if(service_status = 3, 2, 1),2,null,1,null,null,null,null,null,null,null,null,null,#{year},null,null,null,null,null
        from c_customer_service where is_del = 0 and (end_account_period is null or end_account_period >= ${nowPeriod})
    </insert>
</mapper>