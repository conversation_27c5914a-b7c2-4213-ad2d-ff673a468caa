<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CTagMapper">
    
    <resultMap type="com.bxm.customer.domain.CTag" id="CTagResult">
        <result property="id"    column="id"    />
        <result property="tagName"    column="tag_name"    />
        <result property="hasParam"    column="has_param"    />
        <result property="tagType"    column="tag_type"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCTagVo">
        select id, tag_name, has_param, tag_type, is_del, create_by, create_time, update_by, update_time from c_tag
    </sql>

    <select id="selectCTagList" parameterType="com.bxm.customer.domain.CTag" resultMap="CTagResult">
        <include refid="selectCTagVo"/>
        <where>  
            <if test="tagName != null  and tagName != ''"> and tag_name like concat('%', #{tagName}, '%')</if>
            <if test="hasParam != null "> and has_param = #{hasParam}</if>
            <if test="tagType != null "> and tag_type = #{tagType}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCTagById" parameterType="Long" resultMap="CTagResult">
        <include refid="selectCTagVo"/>
        where id = #{id}
    </select>
    <select id="selectTagManagerPageList" resultType="com.bxm.customer.domain.dto.tag.TagManagerDTO">
        select
            id as id,
            tag_name as tagName,
            tag_type as tagType,
            is_customize as isCustomize,
            status as status
            from c_tag
        <where>
            is_del = 0
            <if test="deptType = 1">
                and tag_type = 2 and top_dept_id = #{topDeptId}
            </if>
            <if test="deptType = 2">
                and (tag_type = 1 or (tag_type = 2 and top_dept_id = #{topDeptId}))
            </if>
            <if test="tagNames != null and tagNames.size > 0">
                and replace(tag_name, '%s', '') in
                <foreach item="tagName" collection="tagNames" separator="," open="(" close=")">
                    #{tagName}
                </foreach>
            </if>
            <if test="isCustomize != null">
                and is_customize = #{isCustomize}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by create_time desc, id desc
    </select>
    <select id="selectTagManagerListByIds" resultType="com.bxm.customer.domain.dto.tag.TagManagerDTO">
        select
        id as id,
        tag_name as tagName,
        tag_type as tagType,
        is_customize as isCustomize,
        status as status
        from c_tag
        <where>
            id in
            <foreach item="id" collection="ids" separator="," open="(" close=")">
                #{id}
            </foreach>
        </where>
        order by create_time desc, id desc
    </select>

    <insert id="insertCTag" parameterType="com.bxm.customer.domain.CTag" useGeneratedKeys="true" keyProperty="id">
        insert into c_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name,</if>
            <if test="hasParam != null">has_param,</if>
            <if test="tagType != null">tag_type,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">#{tagName},</if>
            <if test="hasParam != null">#{hasParam},</if>
            <if test="tagType != null">#{tagType},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCTag" parameterType="com.bxm.customer.domain.CTag">
        update c_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name = #{tagName},</if>
            <if test="hasParam != null">has_param = #{hasParam},</if>
            <if test="tagType != null">tag_type = #{tagType},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCTagById" parameterType="Long">
        delete from c_tag where id = #{id}
    </delete>

    <delete id="deleteCTagByIds" parameterType="String">
        delete from c_tag where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>