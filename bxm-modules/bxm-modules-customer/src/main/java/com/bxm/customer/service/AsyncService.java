package com.bxm.customer.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverResult;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverStatus;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.enums.businessTask.BusinessTaskItemType;
import com.bxm.common.core.enums.businessTask.BusinessTaskStatus;
import com.bxm.common.core.enums.businessTask.BusinessTaskType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodDTO;
import com.bxm.customer.api.domain.vo.RemoteSendQualityCheckingTaskVO;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.OpenApiSyncStatementDetailExportDTO;
import com.bxm.customer.domain.dto.PreAuthInfoDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderCustomerUploadDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderPeriodUploadDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderUploadCheckResultDTO;
import com.bxm.customer.domain.dto.tag.TagV2DTO;
import com.bxm.customer.domain.vo.*;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.mapper.*;
import com.bxm.customer.properties.BusinessGroupProperties;
import com.bxm.customer.properties.SpecialTagProperties;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.*;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.bxm.thirdpart.api.domain.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AsyncService {

    private static final int THREAD_POOL_SIZE = 20;

    private static final String DEAL_SUCCESS = "处理成功";

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteThirdpartService remoteThirdpartService;

    @Autowired
    @Lazy
    private ICustomerDeliverService customerDeliverService;

    @Autowired
    private ICustomerTaxTypeCheckService customerTaxTypeCheckService;

    @Autowired
    private ICustomerServicePeriodMonthTaxTypeCheckService customerServicePeriodMonthTaxTypeCheckService;

    @Autowired
    private IOpenApiSyncCustomerService openApiSyncCustomerService;

    @Autowired
    private DownloadRecordMapper downloadRecordMapper;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private SettlementOrderDataMapper settlementOrderDataMapper;

    @Autowired
    private SettlementOrderDataTempMapper settlementOrderDataTempMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BusinessTaskMapper businessTaskMapper;

    @Autowired
    private CustomerServiceInAccountMapper customerServiceInAccountMapper;

    @Autowired
    private ICustomerServicePeriodMonthIncomeService customerServicePeriodMonthIncomeService;

    @Autowired
    @Lazy
    private ICustomerServiceInAccountService customerServiceInAccountService;

    @Autowired
    private IXqySetCustomerRecordService xqySetCustomerRecordService;

    @Autowired
    private CustomerServiceCashierAccountingMapper customerServiceCashierAccountingMapper;

    @Autowired
    @Lazy
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @Autowired
    private FileService fileService;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private BusinessGroupProperties businessGroupProperties;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    @Autowired
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    @Autowired
    private CuOssService cuOssService;

    @Autowired
    private OpenApiSyncRecordMapper openApiSyncRecordMapper;

    @Autowired
    private IOpenApiSyncItemService openApiSyncItemService;

    @Lazy
    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Async
    public void asyncCompanyWechatSendMessage(Long deptId, String content, String sendEmployeeName, Long sendUserId) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException(false);
        if (Objects.isNull(sysDept)) {
            return;
        }
        if (sysDept.getLevel() == 4 || sysDept.getLevel() == 2 || sysDept.getLevel() == 3) {
            List<SysEmployee> employees = remoteEmployeeService.getEmployeeListByDeptId(deptId).getDataThrowException(false);
            if (ObjectUtils.isEmpty(employees)) {
                return;
            }
            if (sysDept.getLevel() == 2 || sysDept.getLevel() == 3) {
                employees = employees.stream().filter(SysEmployee::getIsLeader).collect(Collectors.toList());
                if (ObjectUtils.isEmpty(employees)) {
                    return;
                }
            }
            employees = employees.stream().filter(employee -> !Objects.equals(sendUserId, employee.getUserId())).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(employees)) {
                return;
            }
            remoteUserService.sendMessage(employees.stream().map(employee -> RemoteUserMessageVO.builder()
                    .sendEmployeeName(sendEmployeeName)
                    .receiveUserId(employee.getUserId()).receiveEmployeeId(employee.getEmployeeId()).receiveEmployeeName(employee.getEmployeeName())
                    .receiveDeptId(deptId).receiveDeptName(sysDept.getDeptName()).content(content).messageType(2)
                    .build()).collect(Collectors.toList()));
        }
    }

    @Async
    public void asyncSendMessage(Long receiveUserId, Long receiveEmployeeId, String receiveEmployeeName, Long receiveDeptId, String receiveDeptName, String content, String sendEmployeeName) {
        remoteUserService.sendMessage(Collections.singletonList(RemoteUserMessageVO.builder()
                .sendEmployeeName(sendEmployeeName)
                .receiveUserId(receiveUserId).receiveEmployeeId(receiveEmployeeId).receiveEmployeeName(receiveEmployeeName)
                .receiveDeptId(receiveDeptId).receiveDeptName(receiveDeptName).content(content).messageType(2)
                .build()));
    }

    public static void main(String[] args) {
        List<TagDTO> tagDTOS = Lists.newArrayList(
                TagDTO.builder().id(1L).fullTagName("医保").build(),
                TagDTO.builder().id(2L).fullTagName("社保").build()
        );
        Map<String, Object> params = new HashMap<>();
        params.put("taxNumber", "xxxxxx");
        params.put("customerName", "xxxxxxx");
        params.put("customerServiceId", 1L);
        params.put("tags", tagDTOS);
        System.out.println(JSONObject.toJSONString(params));
    }

    @Async
    public void asyncSendMessage(Long receiveUserId, Long receiveEmployeeId, String receiveEmployeeName, Long receiveDeptId, String receiveDeptName, String content, String sendEmployeeName, Integer messageType) {
        remoteUserService.sendMessage(Collections.singletonList(RemoteUserMessageVO.builder()
                .sendEmployeeName(sendEmployeeName)
                .receiveUserId(receiveUserId).receiveEmployeeId(receiveEmployeeId).receiveEmployeeName(receiveEmployeeName)
                .receiveDeptId(receiveDeptId).receiveDeptName(receiveDeptName).content(content).messageType(messageType)
                .build()));
    }

    public void asyncOpenApiReport(Map<Long, OpenApiSyncCustomer> syncCustomerMap, Map<Long, List<OpenApiSyncItem>> syncItemMap,
                                   Map<Long, List<OpenApiSyncStatementDetail>> detailMap, Map<Long, CustomerDeliver> deliverMap,
                                   String operName, String reportPeriod, String deliverType, Long deptId) {
        List<Integer> deliverTypes = Lists.newArrayList();
        if (StringUtils.isEmpty(deliverType)) {
            deliverTypes.addAll(Arrays.stream(DeliverType.values()).map(DeliverType::getCode).collect(Collectors.toList()));
        } else {
            List<String> deliverTypeList = Arrays.stream(deliverType.split(",")).collect(Collectors.toList());
            if (deliverTypeList.contains("1")) {
                deliverTypes.add(DeliverType.MEDICAL_INSURANCE.getCode());
            }
            if (deliverTypeList.contains("2")) {
                deliverTypes.add(DeliverType.SOCIAL_INSURANCE.getCode());
            }
            if (deliverTypeList.contains("3")) {
                deliverTypes.add(DeliverType.TAX.getCode());
                deliverTypes.add(DeliverType.TAX_OPERATING_INCOME.getCode());
            }
            if (deliverTypeList.contains("4")) {
                deliverTypes.add(DeliverType.NATIONAL_TAX.getCode());
            }
        }
        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        syncCustomerMap.forEach((syncCustomerId, syncCustomer) -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                List<OpenApiSyncItem> itemList = syncItemMap.get(syncCustomerId);
                boolean isSuccess = true;
                if (deliverTypes.contains(DeliverType.NATIONAL_TAX.getCode())) {
                    if (!Objects.isNull(syncCustomer.getNationalTaxDeliverId()) && StringUtils.isEmpty(syncCustomer.getNationalTaxResult()) &&
                            itemList.stream().anyMatch(item -> (Constants.NATIONAL_TAX_ITEM_CATEGORIES.contains(item.getItemCategoryName()) ||
                                    (!Constants.PERSON_TAX_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && !Constants.SOCIAL_SECURITY_ITEM_CATEGORIES.contains(item.getItemCategoryName()))) && Objects.equals(item.getIsReport(), "是"))) {
                        try {
                            dealNationTaxDeliver(deliverMap, syncCustomer, itemList.stream().filter(item -> (Constants.NATIONAL_TAX_ITEM_CATEGORIES.contains(item.getItemCategoryName()) ||
                                    (!Constants.PERSON_TAX_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && !Constants.SOCIAL_SECURITY_ITEM_CATEGORIES.contains(item.getItemCategoryName()))) && Objects.equals(item.getIsReport(), "是")).collect(Collectors.toList()), operName, deptId);
                            syncCustomer.setNationalTaxResult(DEAL_SUCCESS);
                        } catch (Exception e) {
                            String message = StringUtils.isEmpty(e.getMessage()) ? "未知" : e.getMessage().length() > 500 ? e.getMessage().substring(0, 500) : e.getMessage();
                            syncCustomer.setNationalTaxResult(message);
                            isSuccess = false;
                        }
                    }
                    if (itemList.stream().anyMatch(item -> Constants.SETTLE_ACCOUNTS.contains(item.getItemCategoryName()) && Objects.equals(item.getIsReport(), "是"))) {
                        try {
                            deaSettleAccountsDeliver(syncCustomer, itemList.stream().filter(item -> Constants.SETTLE_ACCOUNTS.contains(item.getItemCategoryName()) && Objects.equals(item.getIsReport(), "是")).collect(Collectors.toList()), operName, deptId);
                        } catch (Exception e) {
                            log.error("汇算处理失败:{}", e.getMessage());
                        }
                    }
                }
                if (deliverTypes.contains(DeliverType.MEDICAL_INSURANCE.getCode()) && !Objects.isNull(syncCustomer.getMedicalSecurityDeliverId()) && StringUtils.isEmpty(syncCustomer.getMedicalSecurityResult()) &&
                        itemList.stream().anyMatch(item -> Constants.MEDICAL_SECURITY_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && Constants.MEDICAL_SECURITY_ITEMS.contains(item.getItemName()) && Objects.equals(item.getIsReport(), "是"))) {
                    try {
                        List<OpenApiSyncStatementDetail> statementDetails = detailMap.getOrDefault(syncCustomer.getId(), Lists.newArrayList());
                        dealOtherDeliver(deliverMap, syncCustomer, operName, syncCustomer.getMedicalSecurityDeliverId(), itemList.stream().filter(item -> Constants.MEDICAL_SECURITY_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && Constants.MEDICAL_SECURITY_ITEMS.contains(item.getItemName()) && Objects.equals(item.getIsReport(), "是")).collect(Collectors.toList()), statementDetails, deptId);
                        syncCustomer.setMedicalSecurityResult(DEAL_SUCCESS);
                    } catch (Exception e) {
                        String message = StringUtils.isEmpty(e.getMessage()) ? "未知" : e.getMessage().length() > 500 ? e.getMessage().substring(0, 500) : e.getMessage();
                        syncCustomer.setMedicalSecurityResult(message);
                        isSuccess = false;
                    }
                }
                if (deliverTypes.contains(DeliverType.SOCIAL_INSURANCE.getCode()) && !Objects.isNull(syncCustomer.getSocialSecurityDeliverId()) && StringUtils.isEmpty(syncCustomer.getSocialSecurityResult()) &&
                        itemList.stream().anyMatch(item -> Constants.SOCIAL_SECURITY_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && !Constants.MEDICAL_SECURITY_ITEMS.contains(item.getItemName()) && Objects.equals(item.getIsReport(), "是"))) {
                    try {
                        List<OpenApiSyncStatementDetail> statementDetails = detailMap.getOrDefault(syncCustomer.getId(), Lists.newArrayList());
                        dealOtherDeliver(deliverMap, syncCustomer, operName, syncCustomer.getSocialSecurityDeliverId(), itemList.stream().filter(item -> Constants.SOCIAL_SECURITY_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && !Constants.MEDICAL_SECURITY_ITEMS.contains(item.getItemName()) && Objects.equals(item.getIsReport(), "是")).collect(Collectors.toList()), statementDetails, deptId);
                        syncCustomer.setSocialSecurityResult(DEAL_SUCCESS);
                    } catch (Exception e) {
                        String message = StringUtils.isEmpty(e.getMessage()) ? "未知" : e.getMessage().length() > 500 ? e.getMessage().substring(0, 500) : e.getMessage();
                        syncCustomer.setSocialSecurityResult(message);
                        isSuccess = false;
                    }
                }
                if (deliverTypes.contains(DeliverType.TAX.getCode()) && !Objects.isNull(syncCustomer.getPersonTaxDeliverId()) && StringUtils.isEmpty(syncCustomer.getPersonTaxResult()) &&
                        itemList.stream().anyMatch(item -> Constants.PERSON_TAX_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && Constants.PERSON_TAX_ITEMS.contains(item.getItemName()) && Objects.equals(item.getIsReport(), "是"))) {
                    try {
                        dealOtherDeliver(deliverMap, syncCustomer, operName, syncCustomer.getPersonTaxDeliverId(), itemList.stream().filter(item -> Constants.PERSON_TAX_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && Constants.PERSON_TAX_ITEMS.contains(item.getItemName()) && Objects.equals(item.getIsReport(), "是")).collect(Collectors.toList()), null, deptId);
                        syncCustomer.setPersonTaxResult(DEAL_SUCCESS);
                    } catch (Exception e) {
                        String message = StringUtils.isEmpty(e.getMessage()) ? "未知" : e.getMessage().length() > 500 ? e.getMessage().substring(0, 500) : e.getMessage();
                        syncCustomer.setPersonTaxResult(message);
                        isSuccess = false;
                    }
                }
                if (deliverTypes.contains(DeliverType.TAX_OPERATING_INCOME.getCode()) && !Objects.isNull(syncCustomer.getTaxOperatingDeliverId()) && StringUtils.isEmpty(syncCustomer.getTaxOperatingResult()) &&
                        itemList.stream().anyMatch(item -> Constants.OPERATING_TAX_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && Constants.OPERATING_TAX_ITEMS.contains(item.getItemName()) && Objects.equals(item.getIsReport(), "是"))) {
                    try {
                        dealOtherDeliver(deliverMap, syncCustomer, operName, syncCustomer.getTaxOperatingDeliverId(), itemList.stream().filter(item -> Constants.OPERATING_TAX_ITEM_CATEGORIES.contains(item.getItemCategoryName()) && Constants.OPERATING_TAX_ITEMS.contains(item.getItemName()) && Objects.equals(item.getIsReport(), "是")).collect(Collectors.toList()), null, deptId);
                        syncCustomer.setTaxOperatingResult(DEAL_SUCCESS);
                    } catch (Exception e) {
                        String message = StringUtils.isEmpty(e.getMessage()) ? "未知" : e.getMessage().length() > 500 ? e.getMessage().substring(0, 500) : e.getMessage();
                        syncCustomer.setTaxOperatingResult(message);
                        isSuccess = false;
                    }
                }
                syncCustomer.setIsSuccess(isSuccess);
                openApiSyncCustomerService.updateById(syncCustomer);

            }, executorService);
            futures.add(future);
        });

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        executorService.shutdown();
    }

    private void deaSettleAccountsDeliver(OpenApiSyncCustomer syncCustomer, List<OpenApiSyncItem> settleAccountsItems, String operName, Long deptId) {
        CustomerDeliver settleAccountsDeliver = customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                .eq(CustomerDeliver::getIsDel, false).eq(CustomerDeliver::getCustomerServiceId, syncCustomer.getCustomerServiceId())
                .eq(CustomerDeliver::getDeliverType, DeliverType.SETTLE_ACCOUNTS.getCode())
                .eq(CustomerDeliver::getPeriod, DateUtils.getLastYear12Period()));
        if (Objects.isNull(settleAccountsDeliver)) {
            return;
        }
        BigDecimal currentPeriodAmount = settleAccountsItems.stream().filter(row -> !Objects.equals(row.getItemName(), "滞纳金") && !StringUtils.isEmpty(row.getActualPayTaxAmount()) && !StringUtils.isEmpty(row.getTaxPeriodEnd()) && DateUtils.yearMonthToPeriod(row.getTaxPeriodEnd().substring(0, 7)) >= settleAccountsDeliver.getPeriod())
                .map(row -> new BigDecimal(row.getActualPayTaxAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal supplementAmount = settleAccountsItems.stream().filter(row -> !Objects.equals(row.getItemName(), "滞纳金") && !StringUtils.isEmpty(row.getActualPayTaxAmount()) && !StringUtils.isEmpty(row.getTaxPeriodEnd()) && DateUtils.yearMonthToPeriod(row.getTaxPeriodEnd().substring(0, 7)) < settleAccountsDeliver.getPeriod())
                .map(row -> new BigDecimal(row.getActualPayTaxAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal overdueAmount = settleAccountsItems.stream().filter(row -> Objects.equals(row.getItemName(), "滞纳金") && !StringUtils.isEmpty(row.getActualPayTaxAmount()))
                .map(row -> new BigDecimal(row.getActualPayTaxAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        StringBuilder reportRemark = new StringBuilder();
        for (OpenApiSyncItem item : settleAccountsItems) {
            reportRemark.append(item.getItemCategoryName()).append("-").append(item.getItemName()).append("：").append(item.getActualPayTaxAmount()).append("，");
        }
        if (!StringUtils.isEmpty(reportRemark)) {
            reportRemark.deleteCharAt(reportRemark.length() - 1);
        }
        if (Objects.equals(DeliverStatus.STATUS_0.getCode(), settleAccountsDeliver.getStatus()) || Objects.equals(DeliverStatus.STATUS_1.getCode(), settleAccountsDeliver.getStatus())) {
            CustomerDeliverReportVO vo = new CustomerDeliverReportVO();
            vo.setId(settleAccountsDeliver.getId());
            vo.setReportStatus(1);
            vo.setCurrentPeriodAmount(currentPeriodAmount);
            vo.setOverdueAmount(overdueAmount);
            vo.setSupplementAmount(supplementAmount);
            vo.setSource(1);
            vo.setOperName(operName);
            vo.setReportRemark(reportRemark.toString());
            vo.setSaveType(1);
            vo.setOperType("鑫启易申报数据同步并保存");
            vo.setLeaderDeptId(deptId);
            customerDeliverService.report(vo);
        } else {
            if (Objects.equals(DeliverStatus.STATUS_3.getCode(), settleAccountsDeliver.getStatus()) || Objects.equals(DeliverStatus.STATUS_4.getCode(), settleAccountsDeliver.getStatus())) {
                List<OpenApiSyncItem> items = settleAccountsItems.stream().filter(item -> (!Objects.equals(item.getReportType(), "次") || (Objects.equals(item.getReportType(), "次") && !StringUtils.isEmpty(item.getActualPayTaxAmount()) && new BigDecimal(item.getActualPayTaxAmount()).compareTo(BigDecimal.ZERO) > 0))  && !StringUtils.isEmpty(item.getTaxPeriodEnd()) && DateUtils.yearMonthToPeriod(item.getTaxPeriodEnd().substring(0, 7)) >= settleAccountsDeliver.getPeriod() && !Objects.equals(item.getItemName(), "滞纳金")).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(items) && items.stream().allMatch(item -> Objects.equals(item.getIsPaid(), "是"))) {
                    CustomerDeliverReportVO vo = new CustomerDeliverReportVO();
                    vo.setId(settleAccountsDeliver.getId());
                    vo.setReportStatus(1);
                    vo.setCurrentPeriodAmount(currentPeriodAmount);
                    vo.setReportRemark(reportRemark.toString());
                    vo.setOverdueAmount(overdueAmount);
                    vo.setSupplementAmount(BigDecimal.ZERO);
                    vo.setSource(1);
                    vo.setOperName(operName);
                    customerDeliverService.xqyDeduction(vo);
                }
            }
        }
    }

    private void dealNationTaxDeliver(Map<Long, CustomerDeliver> deliverMap, OpenApiSyncCustomer syncCustomer, List<OpenApiSyncItem> nationalTaxItems, String operName, Long deptId) {
        CustomerDeliver nationalTaxDeliver = deliverMap.get(syncCustomer.getNationalTaxDeliverId());
        BigDecimal currentPeriodAmount = nationalTaxItems.stream().filter(row -> !Objects.equals(row.getItemName(), "滞纳金") && !StringUtils.isEmpty(row.getActualPayTaxAmount()) && !StringUtils.isEmpty(row.getTaxPeriodEnd()) && DateUtils.yearMonthToPeriod(row.getTaxPeriodEnd().substring(0, 7)) >= nationalTaxDeliver.getPeriod())
                .map(row -> new BigDecimal(row.getActualPayTaxAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal supplementAmount = nationalTaxItems.stream().filter(row -> !Objects.equals(row.getItemName(), "滞纳金") && !StringUtils.isEmpty(row.getActualPayTaxAmount()) && !StringUtils.isEmpty(row.getTaxPeriodEnd()) && DateUtils.yearMonthToPeriod(row.getTaxPeriodEnd().substring(0, 7)) < nationalTaxDeliver.getPeriod())
                .map(row -> new BigDecimal(row.getActualPayTaxAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal overdueAmount = nationalTaxItems.stream().filter(row -> Objects.equals(row.getItemName(), "滞纳金") && !StringUtils.isEmpty(row.getActualPayTaxAmount()))
                .map(row -> new BigDecimal(row.getActualPayTaxAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        int nowYear = LocalDate.now().getYear();
        StringBuilder reportRemark = new StringBuilder();
        BigDecimal preAmount = BigDecimal.ZERO;
        for (OpenApiSyncItem item : nationalTaxItems) {
            String taxPeriodEnd = item.getTaxPeriodEnd();
            if (StringUtils.isEmpty(taxPeriodEnd)) {
                reportRemark.append(item.getItemCategoryName()).append("-").append(item.getItemName()).append("：").append(item.getActualPayTaxAmount()).append("，");
            } else {
                int year = Integer.parseInt(taxPeriodEnd.substring(0, 4));
                if (year < nowYear - 1) {
                    try {
                        preAmount = preAmount.add(new BigDecimal(item.getActualPayTaxAmount()));
                    } catch (Exception e) {
                        log.info("金额格式错误，itemId:{}", item.getId());
                    }
                } else {
                    reportRemark.append(item.getItemCategoryName()).append("-").append(item.getItemName()).append("：").append(item.getActualPayTaxAmount()).append("，");
                }
            }
        }
        if (!StringUtils.isEmpty(reportRemark)) {
            reportRemark.deleteCharAt(reportRemark.length() - 1);
        }
        if (preAmount.compareTo(BigDecimal.ZERO) > 0) {
            reportRemark.append("，历年总计：").append(preAmount.stripTrailingZeros().toPlainString());
        }
        if (Objects.equals(DeliverStatus.STATUS_0.getCode(), nationalTaxDeliver.getStatus()) || Objects.equals(DeliverStatus.STATUS_1.getCode(), nationalTaxDeliver.getStatus())) {
            CustomerDeliverReportVO vo = new CustomerDeliverReportVO();
            vo.setId(syncCustomer.getNationalTaxDeliverId());
            vo.setReportStatus(1);
            vo.setCurrentPeriodAmount(currentPeriodAmount);
            vo.setOverdueAmount(overdueAmount);
            vo.setSupplementAmount(supplementAmount);
            vo.setSource(1);
            vo.setOperName(operName);
            vo.setReportRemark(reportRemark.toString());
            vo.setSaveType(1);
            vo.setOperType("保存交付");
            vo.setLeaderDeptId(deptId);
            customerDeliverService.report(vo);
        } else {
//            // 申报获取的金额和申报金额不同，将交付单打上“重新确认”标记，并交付单状态变更为“待确认”
//            // 申报获取的金额和申报金额相同，且交付单状态是“待扣款”，且除“次”以外的“是否申报”全部为“是”，且除“次”以外的“是否扣款”全部为“是”时，将交付单状态变更为“已扣款”
//            if (currentPeriodAmount.compareTo(nationalTaxDeliver.getCurrentPeriodAmount()) != 0 ||
//                    supplementAmount.compareTo(nationalTaxDeliver.getSupplementAmount()) != 0 ||
//                    overdueAmount.compareTo(nationalTaxDeliver.getOverdueAmount()) != 0) {
//                CustomerDeliverReportVO vo = new CustomerDeliverReportVO();
//                vo.setId(syncCustomer.getNationalTaxDeliverId());
//                vo.setReportStatus(1);
//                vo.setCurrentPeriodAmount(currentPeriodAmount);
//                vo.setOverdueAmount(overdueAmount);
//                vo.setSupplementAmount(supplementAmount);
//                vo.setReportRemark(reportRemark.toString());
//                vo.setSource(1);
//                vo.setOperName(operName);
//                customerDeliverService.xqyRepeatConfirm(vo);
//            } else {
//
//            }
            if (Objects.equals(DeliverStatus.STATUS_3.getCode(), nationalTaxDeliver.getStatus()) || Objects.equals(DeliverStatus.STATUS_4.getCode(), nationalTaxDeliver.getStatus())) {
                List<OpenApiSyncItem> items = nationalTaxItems.stream().filter(item -> (!Objects.equals(item.getReportType(), "次") || (Objects.equals(item.getReportType(), "次") && !StringUtils.isEmpty(item.getActualPayTaxAmount()) && new BigDecimal(item.getActualPayTaxAmount()).compareTo(BigDecimal.ZERO) > 0)) && !StringUtils.isEmpty(item.getTaxPeriodEnd()) && DateUtils.yearMonthToPeriod(item.getTaxPeriodEnd().substring(0, 7)) >= nationalTaxDeliver.getPeriod() && !Objects.equals(item.getItemName(), "滞纳金")).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(items) && items.stream().allMatch(item -> Objects.equals(item.getIsPaid(), "是"))) {
                    CustomerDeliverReportVO vo = new CustomerDeliverReportVO();
                    vo.setId(syncCustomer.getNationalTaxDeliverId());
                    vo.setReportStatus(1);
                    vo.setCurrentPeriodAmount(currentPeriodAmount);
                    vo.setReportRemark(reportRemark.toString());
                    vo.setOverdueAmount(overdueAmount);
                    vo.setSupplementAmount(BigDecimal.ZERO);
                    vo.setSource(1);
                    vo.setOperName(operName);
                    customerDeliverService.xqyDeduction(vo);
                }
            }
        }
    }

    private void dealOtherDeliver(Map<Long, CustomerDeliver> deliverMap, OpenApiSyncCustomer syncCustomer, String operName, Long deliverId, List<OpenApiSyncItem> items, List<OpenApiSyncStatementDetail> details, Long deptId) {
        CustomerDeliver customerDeliver = deliverMap.get(deliverId);
        BigDecimal currentPeriodAmount = items.stream().filter(row -> !Objects.equals(row.getItemName(), "滞纳金") && !StringUtils.isEmpty(row.getActualPayTaxAmount()) && !StringUtils.isEmpty(row.getTaxPeriodEnd()) && DateUtils.yearMonthToPeriod(row.getTaxPeriodEnd().substring(0, 7)) >= customerDeliver.getPeriod())
                .map(row -> new BigDecimal(row.getActualPayTaxAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal supplementAmount = items.stream().filter(row -> !Objects.equals(row.getItemName(), "滞纳金") && !StringUtils.isEmpty(row.getActualPayTaxAmount()) && !StringUtils.isEmpty(row.getTaxPeriodEnd()) && DateUtils.yearMonthToPeriod(row.getTaxPeriodEnd().substring(0, 7)) < customerDeliver.getPeriod())
                .map(row -> new BigDecimal(row.getActualPayTaxAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal overdueAmount = Objects.equals(customerDeliver.getDeliverType(), DeliverType.TAX.getCode()) || Objects.equals(customerDeliver.getDeliverType(), DeliverType.TAX_OPERATING_INCOME.getCode()) ? BigDecimal.ZERO :
                items.stream().filter(row -> Objects.equals(row.getItemName(), "滞纳金") && !StringUtils.isEmpty(row.getActualPayTaxAmount()))
                        .map(row -> new BigDecimal(row.getActualPayTaxAmount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        StringBuilder reportRemark = new StringBuilder();
        for (OpenApiSyncItem item : items) {
            reportRemark.append(item.getItemCategoryName()).append("-").append(item.getItemName()).append("：").append(item.getActualPayTaxAmount()).append("，");
        }
        if (!StringUtils.isEmpty(reportRemark)) {
            reportRemark.deleteCharAt(reportRemark.length() - 1);
        }
        if (Objects.equals(DeliverStatus.STATUS_0.getCode(), customerDeliver.getStatus()) || Objects.equals(DeliverStatus.STATUS_1.getCode(), customerDeliver.getStatus())) {
            CustomerDeliverReportVO vo = new CustomerDeliverReportVO();
            vo.setId(deliverId);
            vo.setReportStatus(1);
            vo.setCurrentPeriodAmount(currentPeriodAmount);
            vo.setOverdueAmount(overdueAmount);
            vo.setSupplementAmount(supplementAmount);
            vo.setSource(1);
            vo.setOperName(operName);
            vo.setReportRemark(reportRemark.toString());
            vo.setSaveType(1);
            vo.setOperType("保存交付");
            if (!ObjectUtils.isEmpty(details)) {
                List<OpenApiSyncStatementDetailExportDTO> export = details.stream().map(row -> {
                    OpenApiSyncStatementDetailExportDTO dto = new OpenApiSyncStatementDetailExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    calculateTotalAmount(dto);
                    return dto;
                }).collect(Collectors.toList());
                appendTotalExport(export);
                String fileName = syncCustomer.getCustomerName() + "-" + customerDeliver.getPeriod() + "-社保对账单-" + System.currentTimeMillis();
                ExcelUtil<OpenApiSyncStatementDetailExportDTO> util = new ExcelUtil<>(OpenApiSyncStatementDetailExportDTO.class);
                String fileUrl = util.exportExcelAndUpload(export, fileName);
                vo.setStatementDetailFile(CommonFileVO.builder().fileUrl(fileUrl).fileName(fileName + ".xlsx").build());
                vo.setReportFiles(Collections.singletonList(vo.getStatementDetailFile()));
            }
            vo.setLeaderDeptId(deptId);
            customerDeliverService.report(vo);
        } else {
//            // 申报获取的金额和申报金额不同，将交付单打上“重新确认”标记，并交付单状态变更为“待确认”
//            // 申报获取的金额和申报金额相同，且交付单状态是“待扣款”，且除“次”以外的“是否申报”全部为“是”，且除“次”以外的“是否扣款”全部为“是”时，将交付单状态变更为“已扣款”
//            if (currentPeriodAmount.compareTo(customerDeliver.getCurrentPeriodAmount()) != 0 ||
//                    supplementAmount.compareTo(customerDeliver.getSupplementAmount()) != 0 ||
//                    overdueAmount.compareTo(customerDeliver.getOverdueAmount()) != 0) {
//                CustomerDeliverReportVO vo = new CustomerDeliverReportVO();
//                vo.setId(deliverId);
//                vo.setReportStatus(1);
//                vo.setCurrentPeriodAmount(currentPeriodAmount);
//                vo.setOverdueAmount(overdueAmount);
//                vo.setSupplementAmount(BigDecimal.ZERO);
//                vo.setSource(1);
//                vo.setReportRemark(reportRemark.toString());
//                vo.setOperName(operName);
//                customerDeliverService.xqyRepeatConfirm(vo);
//            } else {
//
//            }
            if (!ObjectUtils.isEmpty(details)) {
                List<OpenApiSyncStatementDetailExportDTO> export = details.stream().map(row -> {
                    OpenApiSyncStatementDetailExportDTO dto = new OpenApiSyncStatementDetailExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    calculateTotalAmount(dto);
                    return dto;
                }).collect(Collectors.toList());
                appendTotalExport(export);
                String fileName = syncCustomer.getCustomerName() + "-" + customerDeliver.getPeriod() + "-社保对账单-" + System.currentTimeMillis();
                ExcelUtil<OpenApiSyncStatementDetailExportDTO> util = new ExcelUtil<>(OpenApiSyncStatementDetailExportDTO.class);
                String fileUrl = util.exportExcelAndUpload(export, fileName);
                customerDeliverService.removeAndSaveNewStatementDetailFile(deliverId, operName, CommonFileVO.builder().fileUrl(fileUrl).fileName(fileName + ".xlsx").build(), deptId);
            }
            if (Objects.equals(DeliverStatus.STATUS_3.getCode(), customerDeliver.getStatus()) || Objects.equals(DeliverStatus.STATUS_4.getCode(), customerDeliver.getStatus())) {
                List<OpenApiSyncItem> itemsList = items.stream().filter(item -> (!Objects.equals(item.getReportType(), "次") || (Objects.equals(item.getReportType(), "次") && !StringUtils.isEmpty(item.getActualPayTaxAmount()) && new BigDecimal(item.getActualPayTaxAmount()).compareTo(BigDecimal.ZERO) > 0))  && !StringUtils.isEmpty(item.getTaxPeriodEnd()) && DateUtils.yearMonthToPeriod(item.getTaxPeriodEnd().substring(0, 7)) >= customerDeliver.getPeriod() && !Objects.equals(item.getItemName(), "滞纳金")).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(itemsList) && itemsList.stream().allMatch(item -> Objects.equals(item.getIsPaid(), "是"))) {
                    CustomerDeliverReportVO vo = new CustomerDeliverReportVO();
                    vo.setId(deliverId);
                    vo.setReportStatus(1);
                    vo.setCurrentPeriodAmount(currentPeriodAmount);
                    vo.setOverdueAmount(overdueAmount);
                    vo.setSupplementAmount(BigDecimal.ZERO);
                    vo.setReportRemark(reportRemark.toString());
                    vo.setSource(1);
                    vo.setOperName(operName);
                    customerDeliverService.xqyDeduction(vo);
                }
            }
        }
    }

    private void appendTotalExport(List<OpenApiSyncStatementDetailExportDTO> export) {
        BigDecimal pensionCompanyBearTotal = BigDecimal.ZERO;
        BigDecimal unemploymentCompanyBearTotal = BigDecimal.ZERO;
        BigDecimal injuryCompanyBearTotal = BigDecimal.ZERO;
        BigDecimal medicalCompanyBearTotal = BigDecimal.ZERO;
        BigDecimal maternityCompanyBearTotal = BigDecimal.ZERO;
        BigDecimal pensionPersonalBearTotal = BigDecimal.ZERO;
        BigDecimal medicalPersonalBearTotal = BigDecimal.ZERO;
        BigDecimal unemploymentPersonalBearTotal = BigDecimal.ZERO;
        for (OpenApiSyncStatementDetailExportDTO dto : export) {
            pensionCompanyBearTotal = pensionCompanyBearTotal.add(StringUtils.isEmpty(dto.getPensionCompanyBear()) ? BigDecimal.ZERO : new BigDecimal(dto.getPensionCompanyBear()));
            unemploymentCompanyBearTotal = unemploymentCompanyBearTotal.add(StringUtils.isEmpty(dto.getUnemploymentCompanyBear()) ? BigDecimal.ZERO : new BigDecimal(dto.getUnemploymentCompanyBear()));
            injuryCompanyBearTotal = injuryCompanyBearTotal.add(StringUtils.isEmpty(dto.getInjuryCompanyBear()) ? BigDecimal.ZERO : new BigDecimal(dto.getInjuryCompanyBear()));
            medicalCompanyBearTotal = medicalCompanyBearTotal.add(StringUtils.isEmpty(dto.getMedicalCompanyBear()) ? BigDecimal.ZERO : new BigDecimal(dto.getMedicalCompanyBear()));
            maternityCompanyBearTotal = maternityCompanyBearTotal.add(StringUtils.isEmpty(dto.getMaternityCompanyBear()) ? BigDecimal.ZERO : new BigDecimal(dto.getMaternityCompanyBear()));
            pensionPersonalBearTotal = pensionPersonalBearTotal.add(StringUtils.isEmpty(dto.getPensionPersonalBear()) ? BigDecimal.ZERO : new BigDecimal(dto.getPensionPersonalBear()));
            medicalPersonalBearTotal = medicalPersonalBearTotal.add(StringUtils.isEmpty(dto.getMedicalPersonalBear()) ? BigDecimal.ZERO : new BigDecimal(dto.getMedicalPersonalBear()));
            unemploymentPersonalBearTotal = unemploymentPersonalBearTotal.add(StringUtils.isEmpty(dto.getUnemploymentPersonalBear()) ? BigDecimal.ZERO : new BigDecimal(dto.getUnemploymentPersonalBear()));
        }
        OpenApiSyncStatementDetailExportDTO totalExport = new OpenApiSyncStatementDetailExportDTO(
                "合计",
                "——",
                pensionCompanyBearTotal.stripTrailingZeros().toPlainString(),
                unemploymentCompanyBearTotal.stripTrailingZeros().toPlainString(),
                injuryCompanyBearTotal.stripTrailingZeros().toPlainString(),
                medicalCompanyBearTotal.stripTrailingZeros().toPlainString(),
                maternityCompanyBearTotal.stripTrailingZeros().toPlainString(),
                pensionPersonalBearTotal.stripTrailingZeros().toPlainString(),
                medicalPersonalBearTotal.stripTrailingZeros().toPlainString(),
                unemploymentPersonalBearTotal.stripTrailingZeros().toPlainString(),
                null,
                null,
                null
        );
        calculateTotalAmount(totalExport);
        export.add(totalExport);
    }

    private void calculateTotalAmount(OpenApiSyncStatementDetailExportDTO dto) {
        dto.setTotalPersonalBear(addStringValue(dto.getPensionPersonalBear(), dto.getMedicalPersonalBear(), dto.getUnemploymentPersonalBear()));
        dto.setTotalCompanyBear(addStringValue(dto.getInjuryCompanyBear(), dto.getMedicalCompanyBear(), dto.getPensionCompanyBear(), dto.getUnemploymentCompanyBear(), dto.getMaternityCompanyBear()));
        dto.setTotalBear(addStringValue(dto.getTotalPersonalBear(), dto.getTotalCompanyBear()));
    }

    private String addStringValue(String... values) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (String value : values) {
            totalAmount = totalAmount.add(StringUtils.isEmpty(value) ? BigDecimal.ZERO : new BigDecimal(value));
        }
        return totalAmount.stripTrailingZeros().toPlainString();
    }

    public void asyncOpenApiTaxCheck(Map<Long, OpenApiSyncCustomer> syncCustomerMap, Map<Long, List<OpenApiSyncItem>> syncItemMap, String operName, List<Long> customerServiceIds, List<Long> customerServicePeriodMonthIds, Map<Long, List<Long>> customerServicePeriodMonthMap, Long deptId) {
        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        syncCustomerMap.forEach((syncCustomerId, syncCustomer) -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                List<OpenApiSyncItem> itemList = syncItemMap.get(syncCustomerId);
                boolean isSuccess = true;
                try {
                    if (!Objects.isNull(syncCustomer.getCustomerServiceId())) {
                        customerTaxTypeCheckService.removeAndCreateByXqy(syncCustomer.getCustomerServiceId(), itemList, operName, deptId);
                    } else {
                        syncCustomer.setTaxCheckError("服务id为空");
                        isSuccess = false;
                    }
                } catch (Exception e) {
                    syncCustomer.setTaxCheckError(e.getMessage());
                    isSuccess = false;
                }
                syncCustomer.setIsSuccess(isSuccess);
                openApiSyncCustomerService.updateById(syncCustomer);

            }, executorService);
            futures.add(future);
        });

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        executorService.shutdown();

        // 同步最新的税种到账期
        customerServicePeriodMonthTaxTypeCheckService.deleteAndSaveNewPeriodTaxCheckByCustomerServiceIds(customerServiceIds, DateUtils.getPrePeriod(), DateUtils.getNowPeriod(), customerServicePeriodMonthIds);

        syncCustomerMap.forEach((syncCustomerId, syncCustomer) -> {
            boolean hasMedicalTag = false;
            boolean hasSocialTag = false;
            List<OpenApiSyncItem> itemList = syncItemMap.get(syncCustomerId);
            if (!ObjectUtils.isEmpty(itemList)) {
                hasMedicalTag = itemList.stream().anyMatch(item -> {
                    if (!StringUtils.isEmpty(item.getItemName())) {
                        return item.getItemName().contains("生育保险") || item.getItemName().contains("医疗保险");
                    } else {
                        return item.getItemCategoryName().contains("生育保险") || item.getItemCategoryName().contains("医疗保险");
                    }
                });
                hasSocialTag = itemList.stream().anyMatch(item -> {
                    if (!StringUtils.isEmpty(item.getItemName())) {
                        return !item.getItemName().contains("生育保险") && !item.getItemName().contains("医疗保险") && (item.getItemName().contains("保险") || item.getItemName().contains("社保"));
                    } else {
                        return !item.getItemCategoryName().contains("生育保险") && !item.getItemCategoryName().contains("医疗保险") && (item.getItemCategoryName().contains("保险") || item.getItemCategoryName().contains("社保"));
                    }
                });
            }
            Long customerServiceId = syncCustomer.getCustomerServiceId();
            List<Long> periodIds = customerServicePeriodMonthMap.get(customerServiceId);
            customerServiceService.addMedicalAndSocialTagByIds(hasMedicalTag, hasSocialTag, customerServiceId, periodIds, operName, deptId);
        });
        customerServicePeriodMonthMapper.updateDeliverStatusByCustomerServiceIds(customerServiceIds);
    }

    public void asyncOpenApiSupplement(List<OpenApiSupplementRecord> waitDealRecords, String operName, Long deptId) {
        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        Map<Long, List<OpenApiSupplementRecord>> supplementMap = waitDealRecords.stream().collect(Collectors.groupingBy(OpenApiSupplementRecord::getDeliverId));
        supplementMap.forEach((deliverId, supplementList) -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> customerDeliverService.supplementReportFilesForXqy(deliverId, supplementList, operName, deptId), executorService);
            futures.add(future);
        });

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        executorService.shutdown();
    }

    @Async
    public void uploadExport(ExcelUtil util, List list, String title, Long downloadRecordId) {
        if (Objects.isNull(downloadRecordId)) {
            return;
        }
        DownloadRecord downloadRecord = downloadRecordMapper.selectById(downloadRecordId);
        if (Objects.isNull(downloadRecord)) {
            return;
        }
        try {
            String downloadUrl = util.exportExcelAndUpload(list, title);
            downloadRecord.setDownloadUrl(downloadUrl);
            downloadRecord.setStatus(1);
            downloadRecord.setDataCount((long) list.size());
            downloadRecordMapper.updateById(downloadRecord);
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            log.error("recordId:{}, 导出上传失败，错误原因:{}", downloadRecordId, errorMsg);
            downloadRecord.setStatus(2);
            downloadRecord.setErrorReason(!StringUtils.isEmpty(errorMsg) && errorMsg.length() > 500 ? errorMsg.substring(0, 500) : errorMsg);
            downloadRecordMapper.updateById(downloadRecord);
        }
    }

    @Async
    public void uploadExport(List<CommonFileVO> files, Map<String, List<?>> dataMap, Map<String, Class<?>> sheetClassMap, String fileName, Long downloadRecordId) {
        if (Objects.isNull(downloadRecordId)) {
            return;
        }
        DownloadRecord downloadRecord = downloadRecordMapper.selectById(downloadRecordId);
        if (Objects.isNull(downloadRecord)) {
            return;
        }
        try {
            String downloadUrl = cuOssService.downloadFilesAndGenerateZiByFilesAndUpload(files, dataMap, sheetClassMap, fileName);
            downloadRecord.setDownloadUrl(downloadUrl);
            downloadRecord.setStatus(1);
            downloadRecordMapper.updateById(downloadRecord);
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            log.error("recordId:{}, 导出上传失败，错误原因:{}", downloadRecordId, errorMsg);
            downloadRecord.setStatus(2);
            downloadRecord.setErrorReason(!StringUtils.isEmpty(errorMsg) && errorMsg.length() > 500 ? errorMsg.substring(0, 500) : errorMsg);
            downloadRecordMapper.updateById(downloadRecord);
        }
    }

    @Async
    public void uploadExport(File tempFile, Long dataCount, Long downloadRecordId) {
        if (Objects.isNull(downloadRecordId)) {
            return;
        }
        DownloadRecord downloadRecord = downloadRecordMapper.selectById(downloadRecordId);
        if (Objects.isNull(downloadRecord)) {
            return;
        }
        try {
            String downloadUrl = exportExcelAndUpload(tempFile);
            downloadRecord.setDownloadUrl(downloadUrl);
            downloadRecord.setStatus(1);
            downloadRecord.setDataCount(dataCount);
            downloadRecordMapper.updateById(downloadRecord);
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            log.error("recordId:{}, 导出上传失败，错误原因:{}", downloadRecordId, errorMsg);
            downloadRecord.setStatus(2);
            downloadRecord.setErrorReason(!StringUtils.isEmpty(errorMsg) && errorMsg.length() > 500 ? errorMsg.substring(0, 500) : errorMsg);
            downloadRecordMapper.updateById(downloadRecord);
        }
    }

    public String exportExcelAndUpload(File tempFile) {
        try {
            // 上传到OSS
            String ossUrl = uploadToOSS(tempFile);

            return ossUrl;

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 上传成功后，删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
        return "";
    }

    public String uploadToOSS(File file) {
        // 初始化OSS客户端
        String endpoint = "oss-cn-hangzhou-internal.aliyuncs.com";
        String accessKeyId = "LTAI5t9TQPHCxG8vU9XXjfWa";
        String accessKeySecret = "******************************";
        String bucketName = "bxm410";

        // 生成OSS客户端
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 上传文件
            String objectName = "export/" + file.getName(); // 根据需求生成文件路径
            ossClient.putObject(bucketName, objectName, new FileInputStream(file));

            // 返回文件的OSS URL
            return objectName;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            // 关闭OSS客户端
            ossClient.shutdown();
        }
    }

    public void asyncOpenApiCommonReport(OpenApiSyncCustomer syncCustomer, CustomerDeliver customerDeliver, CommonReportVO commonReportVO) {
        boolean isSuccess = true;
        try {
            Integer reportStatus = StringUtils.isEmpty(commonReportVO.getResult()) ? 1 : Integer.parseInt(commonReportVO.getResult());
            CustomerDeliverReportVO vo = new CustomerDeliverReportVO();
            vo.setId(customerDeliver.getId());
            vo.setReportStatus(reportStatus);
            vo.setCurrentPeriodAmount(StringUtils.isEmpty(commonReportVO.getReportAmount()) ? BigDecimal.ZERO : new BigDecimal(commonReportVO.getReportAmount()));
            vo.setOverdueAmount(StringUtils.isEmpty(commonReportVO.getOverdueAmount()) ? BigDecimal.ZERO : new BigDecimal(commonReportVO.getOverdueAmount()));
            vo.setSupplementAmount(StringUtils.isEmpty(commonReportVO.getSupplementAmount()) ? BigDecimal.ZERO : new BigDecimal(commonReportVO.getSupplementAmount()));
            vo.setSource(commonReportVO.getSource());
            vo.setOperName(commonReportVO.getOperateName());
            vo.setReportRemark(commonReportVO.getRemark());
            vo.setReportFiles(ObjectUtils.isEmpty(commonReportVO.getFiles()) ? Lists.newArrayList() :
                    commonReportVO.getFiles().stream().map(f -> CommonFileVO.builder().fileName(f.getFileName()).fileUrl(f.getFileId()).officalFilename(f.getOfficalFilename()).build()).collect(Collectors.toList()));
            vo.setSaveType(1);
            vo.setOperType("保存交付");
            vo.setLeaderDeptId(commonReportVO.getDeptId());
            customerDeliverService.report(vo);
            if (!Objects.isNull(syncCustomer.getMedicalSecurityDeliverId())) {
                syncCustomer.setMedicalSecurityResult(DEAL_SUCCESS);
            } else if (!Objects.isNull(syncCustomer.getSocialSecurityDeliverId())) {
                syncCustomer.setSocialSecurityResult(DEAL_SUCCESS);
            } else if (!Objects.isNull(syncCustomer.getPersonTaxDeliverId())) {
                syncCustomer.setPersonTaxResult(DEAL_SUCCESS);
            } else if (!Objects.isNull(syncCustomer.getNationalTaxDeliverId())) {
                syncCustomer.setNationalTaxResult(DEAL_SUCCESS);
            } else {
                syncCustomer.setTaxOperatingResult(DEAL_SUCCESS);
            }
        } catch (Exception e) {
            if (!Objects.isNull(syncCustomer.getMedicalSecurityDeliverId())) {
                syncCustomer.setMedicalSecurityResult(e.getMessage());
            } else if (!Objects.isNull(syncCustomer.getSocialSecurityDeliverId())) {
                syncCustomer.setSocialSecurityResult(e.getMessage());
            } else if (!Objects.isNull(syncCustomer.getPersonTaxDeliverId())) {
                syncCustomer.setPersonTaxResult(e.getMessage());
            } else if (!Objects.isNull(syncCustomer.getNationalTaxDeliverId())) {
                syncCustomer.setNationalTaxResult(e.getMessage());
            } else {
                syncCustomer.setTaxOperatingResult(e.getMessage());
            }
            isSuccess = false;
        }
        syncCustomer.setIsSuccess(isSuccess);
        openApiSyncCustomerService.updateById(syncCustomer);
    }

    public void asyncOpenApiCommonSupplement(List<OpenApiSupplementRecord> waitDealRecords, String operateName, String sourceName, Integer fileType, Long deptId) {
        Long deliverId = waitDealRecords.get(0).getDeliverId();
        customerDeliverService.supplementReportFilesForCommon(deliverId, waitDealRecords, operateName, sourceName, fileType, deliverId);
    }

    public void asyncOpenApiCommonDeduction(List<OpenApiDeductionRecord> waitDealRecords, String operateName, String sourceName, String remark, Long deptId) {
        Long deliverId = waitDealRecords.get(0).getDeliverId();
        Integer result = Integer.parseInt(waitDealRecords.get(0).getResult());
        List<CommonFileVO> files = waitDealRecords.stream().filter(r -> !StringUtils.isEmpty(r.getFileId())).map(r -> CommonFileVO.builder().fileName(r.getFileName()).officalFilename(r.getOfficalFilename()).fileUrl(r.getFileId()).build()).collect(Collectors.toList());
        customerDeliverService.deductionForCommon(deliverId, result, files, operateName, sourceName, remark, deptId);
    }

    @Async
    public void checkPeriodDatas(List<SettlementOrderPeriodUploadDTO> periodDataList, String uuid, Integer isSupplement, String batchNo, Long settlementOrderId, Long businessDeptId, Integer settlementType) {
        if (ObjectUtils.isEmpty(periodDataList)) {
            SettlementOrderUploadCheckResultDTO checkResultDTO = new SettlementOrderUploadCheckResultDTO(uuid, 0L);
            checkResultDTO.setCheckDataCount(0L);
            checkResultDTO.setSuccessDataCount(0L);
            checkResultDTO.setErrorDataCount(0L);
            checkResultDTO.setHasErrorData(false);
            checkResultDTO.setIsComplete(true);
            redisService.setCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_RESULT + uuid, checkResultDTO, 60 * 60L, TimeUnit.SECONDS);
        } else {
            List<CustomerServicePeriodMonth> periodMonthList = customerServicePeriodMonthMapper.selectBatchByCreditCodeAndPeriod(periodDataList);
            Map<String, CustomerServicePeriodMonth> periodMonthMap = periodMonthList.stream().collect(Collectors.toMap(row -> row.getCreditCode() + "-" + row.getPeriod(), Function.identity(), (o1, o2) -> o1));
            List<Long> existsBusinessIds;
            List<Long> existsTaskPeriodIds = null;
            if (!StringUtils.isEmpty(batchNo)) {
                if (Objects.equals(settlementType, SettlementType.TASK_PERIOD.getCode())) {
                    existsBusinessIds = settlementOrderDataTempMapper.selectList(new LambdaQueryWrapper<SettlementOrderDataTemp>()
                            .eq(SettlementOrderDataTemp::getSettlementOrderBatchNo, batchNo)
                            .select(SettlementOrderDataTemp::getBusinessId)).stream().map(SettlementOrderDataTemp::getBusinessId).collect(Collectors.toList());

                    //不重复派发任务
                    existsTaskPeriodIds = businessTaskMapper.selectList(
                            new LambdaQueryWrapper<BusinessTask>()
                                    .eq(BusinessTask::getIsDel, false)
                                    .eq(BusinessTask::getType, BusinessTaskType.PERIOD.getCode())
                                    .eq(BusinessTask::getItemType, BusinessTaskItemType.BANK_PAYMENT.getCode())
                                    .notIn(BusinessTask::getStatus, BusinessTaskStatus.RE)
                                    .select(BusinessTask::getBizId)).stream().map(BusinessTask::getBizId).distinct().collect(Collectors.toList()
                    );
                } else {
                    existsBusinessIds = settlementOrderDataTempMapper.selectList(new LambdaQueryWrapper<SettlementOrderDataTemp>()
                            .eq(SettlementOrderDataTemp::getSettlementOrderBatchNo, batchNo)
                            .eq(SettlementOrderDataTemp::getBusinessDeptId, businessDeptId).select(SettlementOrderDataTemp::getBusinessId)).stream().map(SettlementOrderDataTemp::getBusinessId).collect(Collectors.toList());
                }
            } else {
                existsBusinessIds = settlementOrderDataMapper.selectList(new LambdaQueryWrapper<SettlementOrderData>()
                        .eq(SettlementOrderData::getSettlementOrderId, settlementOrderId)
                        .select(SettlementOrderData::getBusinessId)).stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList());
            }
            for (SettlementOrderPeriodUploadDTO periodDTO : periodDataList) {
                redisService.incrementWithTimeToLive(CacheConstants.SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_DATA_COUNT + uuid, 60 * 60L, TimeUnit.SECONDS);
                if (StringUtils.isEmpty(periodDTO.getCreditCode()) || StringUtils.isEmpty(periodDTO.getPeriod())) {
                    periodDTO.setCheckError("数据异常");
                    continue;
                }
                if (periodDataList.stream().filter(row -> Objects.equals(row.getCreditCode(), periodDTO.getCreditCode()) && Objects.equals(row.getPeriod(), periodDTO.getPeriod())).count() > 1L) {
                    periodDTO.setCheckError("表内数据重复");
                    continue;
                }
                String key = periodDTO.getCreditCode() + "-" + periodDTO.getPeriod();
                if (!periodMonthMap.containsKey(key)) {
                    periodDTO.setCheckError("账期不存在");
                } else {
                    CustomerServicePeriodMonth customerServicePeriodMonth = periodMonthMap.get(key);
                    periodDTO.setCustomerServiceId(customerServicePeriodMonth.getCustomerServiceId());
                    periodDTO.setCustomerServicePeriodMonthId(customerServicePeriodMonth.getId());
                    if (!Objects.equals(settlementType, SettlementType.TASK_PERIOD.getCode())) {
                        if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT.getCode())) {
                            if (isSupplement == 1 && !Objects.equals(customerServicePeriodMonth.getSettlementStatus(), BusinessSettlementStatus.SETTLED.getCode())) {
                                periodDTO.setCheckError("账期未结算");
                                continue;
                            }
                            if (isSupplement == 0 && !Objects.equals(customerServicePeriodMonth.getSettlementStatus(), BusinessSettlementStatus.WAIT_SETTLEMENT.getCode())) {
                                periodDTO.setCheckError("账期已结算");
                                continue;
                            }
                        }
                        if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
                            if (!Objects.equals(customerServicePeriodMonth.getPrepayStatus(), PeriodPrepayStatus.UNPREPAID.getCode())) {
                                periodDTO.setCheckError("账期已预收");
                                continue;
                            }
                        }
                    }
                    if (!Objects.equals(settlementType, SettlementType.TASK_PERIOD.getCode()) && !Objects.equals(customerServicePeriodMonth.getBusinessDeptId(), businessDeptId)) {
                        periodDTO.setCheckError("业务公司不同");
                        continue;
                    }
                    if (existsBusinessIds.contains(customerServicePeriodMonth.getId())) {
                        periodDTO.setCheckError("数据已存在");
                        continue;
                    }

                    if (Objects.equals(settlementType, SettlementType.TASK_PERIOD.getCode())) {
                        //这是账期任务

                        if (existsTaskPeriodIds != null) {
                            if (existsTaskPeriodIds.contains(customerServicePeriodMonth.getId())) {
                                periodDTO.setCheckError("任务已存在");
                                continue;
                            }
                        }

                        //过滤银行流水时间为空的数据
                        CustomerServiceInAccount customerServiceInAccount = customerServiceInAccountMapper.selectByPeriod(customerServicePeriodMonth.getId());
                        if (customerServiceInAccount != null && customerServiceInAccount.getBankPaymentInputTime() != null) {
                            periodDTO.setCheckError("银行流水录入时间有数据");
                            continue;
                        }
                    }
                }
            }
            SettlementOrderUploadCheckResultDTO checkResultDTO = new SettlementOrderUploadCheckResultDTO(uuid, (long) periodDataList.size());
            checkResultDTO.setCheckDataCount((long) periodDataList.size());
            checkResultDTO.setSuccessDataCount(periodDataList.stream().filter(row -> StringUtils.isEmpty(row.getCheckError())).count());
            checkResultDTO.setErrorDataCount(periodDataList.stream().filter(row -> !StringUtils.isEmpty(row.getCheckError())).count());
            checkResultDTO.setHasErrorData(periodDataList.stream().anyMatch(row -> !StringUtils.isEmpty(row.getCheckError())));
            checkResultDTO.setIsComplete(true);
            redisService.setCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_RESULT + uuid, checkResultDTO, 60 * 60L, TimeUnit.SECONDS);
            redisService.setLargeCacheList(CacheConstants.SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_RESULT_LIST + uuid, periodDataList, 2000, 60 * 60L, TimeUnit.SECONDS);
        }
    }

    @Async
    public void checkCustomerServiceDatas(List<SettlementOrderCustomerUploadDTO> customerDataList, String uuid, String batchNo, Long settlementOrderId, Long businessDeptId) {
        if (ObjectUtils.isEmpty(customerDataList)) {
            SettlementOrderUploadCheckResultDTO checkResultDTO = new SettlementOrderUploadCheckResultDTO(uuid, 0L);
            checkResultDTO.setCheckDataCount(0L);
            checkResultDTO.setSuccessDataCount(0L);
            checkResultDTO.setErrorDataCount(0L);
            checkResultDTO.setHasErrorData(false);
            checkResultDTO.setIsComplete(true);
            redisService.setCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_RESULT + uuid, checkResultDTO, 60 * 60L, TimeUnit.SECONDS);
        } else {
            List<CCustomerService> customerServices = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>().eq(CCustomerService::getIsDel, false)
                    .in(CCustomerService::getCreditCode, customerDataList.stream().map(SettlementOrderCustomerUploadDTO::getCreditCode).collect(Collectors.toList()))
                    .select(CCustomerService::getId, CCustomerService::getCreditCode, CCustomerService::getBusinessDeptId, CCustomerService::getSettlementStatus));
            Map<String, CCustomerService> customerServiceMap = customerServices.stream().collect(Collectors.toMap(CCustomerService::getCreditCode, Function.identity(), (o1, o2) -> o1));
            List<Long> existsBusinessIds;
            if (!StringUtils.isEmpty(batchNo)) {
                existsBusinessIds = settlementOrderDataTempMapper.selectList(new LambdaQueryWrapper<SettlementOrderDataTemp>()
                        .eq(SettlementOrderDataTemp::getSettlementOrderBatchNo, batchNo)
                        .eq(SettlementOrderDataTemp::getBusinessDeptId, businessDeptId).select(SettlementOrderDataTemp::getBusinessId)).stream().map(SettlementOrderDataTemp::getBusinessId).collect(Collectors.toList());
            } else {
                existsBusinessIds = settlementOrderDataMapper.selectList(new LambdaQueryWrapper<SettlementOrderData>()
                        .eq(SettlementOrderData::getSettlementOrderId, settlementOrderId)
                        .select(SettlementOrderData::getBusinessId)).stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList());
            }
            for (SettlementOrderCustomerUploadDTO customerDTO : customerDataList) {
                redisService.incrementWithTimeToLive(CacheConstants.SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_DATA_COUNT + uuid, 60 * 60L, TimeUnit.SECONDS);
                if (StringUtils.isEmpty(customerDTO.getCreditCode())) {
                    customerDTO.setCheckError("数据异常");
                    continue;
                }
                if (customerDataList.stream().filter(row -> Objects.equals(row.getCreditCode(), customerDTO.getCreditCode())).count() > 1L) {
                    customerDTO.setCheckError("表内数据重复");
                    continue;
                }
                if (!customerServiceMap.containsKey(customerDTO.getCreditCode())) {
                    customerDTO.setCheckError("服务不存在");
                } else {
                    CCustomerService customerService = customerServiceMap.get(customerDTO.getCreditCode());
                    customerDTO.setCustomerServiceId(customerService.getId());
                    if (!Objects.equals(customerService.getSettlementStatus(), BusinessSettlementStatus.WAIT_SETTLEMENT.getCode())) {
                        customerDTO.setCheckError("服务已结算");
                        continue;
                    }
                    if (!Objects.equals(customerService.getBusinessDeptId(), businessDeptId)) {
                        customerDTO.setCheckError("业务公司不同");
                        continue;
                    }
                    if (existsBusinessIds.contains(customerService.getId())) {
                        customerDTO.setCheckError("数据已存在");
                    }
                }
            }
            SettlementOrderUploadCheckResultDTO checkResultDTO = new SettlementOrderUploadCheckResultDTO(uuid, (long) customerDataList.size());
            checkResultDTO.setCheckDataCount((long) customerDataList.size());
            checkResultDTO.setSuccessDataCount(customerDataList.stream().filter(row -> StringUtils.isEmpty(row.getCheckError())).count());
            checkResultDTO.setErrorDataCount(customerDataList.stream().filter(row -> !StringUtils.isEmpty(row.getCheckError())).count());
            checkResultDTO.setHasErrorData(customerDataList.stream().anyMatch(row -> !StringUtils.isEmpty(row.getCheckError())));
            checkResultDTO.setIsComplete(true);
            redisService.setCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_RESULT + uuid, checkResultDTO, 60 * 60L, TimeUnit.SECONDS);
            redisService.setLargeCacheList(CacheConstants.SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_RESULT_LIST + uuid, customerDataList, 2000, 60 * 60L, TimeUnit.SECONDS);
        }
    }

    public void asyncPreAuthAuth(OpenApiSyncCustomer syncCustomer, OpenApiSyncVatData vatData, String operateName, Long deptId) {
        PreAuthInfoDTO preAuthInfoDTO = new PreAuthInfoDTO();
        BeanUtils.copyProperties(vatData, preAuthInfoDTO);
        CustomerDeliverAuthVO vo = CustomerDeliverAuthVO.builder()
                .id(syncCustomer.getPreAuthDeliverId())
                .authStatus(1)
                .preAuthInfoDTO(preAuthInfoDTO)
                .authRemark(StringUtils.isEmpty(vatData.getCurrentRemark()) || Objects.equals("null", vatData.getCurrentRemark()) ? "" : vatData.getCurrentRemark())
                .mattersNotes(StringUtils.isEmpty(vatData.getPermanentRemark()) || Objects.equals("null", vatData.getPermanentRemark()) ? "" : vatData.getPermanentRemark())
                .authFiles(Lists.newArrayList())
                .operName(operateName)
                .source(OpenApiAppRelations.XQY.getId())
                .preAuthRemind(syncCustomer.getPreAuthRemind())
                .deptId(deptId)
                .build();
        customerDeliverService.xqyAuth(vo);
    }

    @Async
    public void asyncIncomeNotice(OpenApiData openApiData) {
        String params = JSONObject.parseObject(openApiData.getDataJson()).getString("params");
        if (StringUtils.isEmpty(params)) {
            return;
        }
        CCustomerService customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getTaxNumber, openApiData.getTaxNumber())
                .eq(CCustomerService::getServiceStatus, ServiceStatus.SERVICE.getCode())
                .last("limit 1"));
        if (Objects.isNull(customerService)) {
            customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                    .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getTaxNumber, openApiData.getTaxNumber())
                    .eq(CCustomerService::getServiceStatus, ServiceStatus.END.getCode())
                    .last("limit 1"));
        }
        if (Objects.isNull(customerService)) {
            return;
        }
        Map<Integer, CustomerServicePeriodMonthIncome> customerServicePeriodMonthIncomeMap = customerServicePeriodMonthIncomeService.list(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>()
                .eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerService.getId())).stream().collect(Collectors.toMap(CustomerServicePeriodMonthIncome::getPeriod, Function.identity(), (v1, v2) -> v1));
        List<CommonIncomeVO> incomes = JSONArray.parseArray(params, CommonIncomeVO.class);
        Integer nowPeriod = DateUtils.getNowPeriod();
        boolean needUpdateIncome = false;
        for (CommonIncomeVO incomeVO : incomes) {
            Integer period = DateUtils.yearMonthToPeriod(incomeVO.getPeriod());
            CustomerServicePeriodMonthIncome income = customerServicePeriodMonthIncomeMap.get(period);
            if (Objects.isNull(income)) {
                if (period <= nowPeriod) {
                    if (!Objects.isNull(customerService.getEndAccountPeriod()) && customerService.getEndAccountPeriod() < period) {
                        log.info("收入不存在且服务已结束，不新增处理。。。");
                    } else {
                        income = new CustomerServicePeriodMonthIncome();
                        income.setCustomerServiceId(customerService.getId());
                        income.setPeriod(period);
                        income.setAllTicketTaxAmount(StringUtils.isEmpty(incomeVO.getAllKpse()) ? BigDecimal.ZERO : new BigDecimal(incomeVO.getAllKpse()));
                        income.setAllTicketAmount(StringUtils.isEmpty(incomeVO.getAllKpJe()) ? BigDecimal.ZERO : new BigDecimal(incomeVO.getAllKpJe()));
                        income.setTotalInvoiceAmount(StringUtils.isEmpty(incomeVO.getAllJxJe()) ? BigDecimal.ZERO : new BigDecimal(incomeVO.getAllJxJe()));
                        income.setTotalInvoiceTaxAmount(StringUtils.isEmpty(incomeVO.getAllJxSe()) ? BigDecimal.ZERO : new BigDecimal(incomeVO.getAllJxSe()));
                        income.setTicketTime(LocalDateTime.now());
                        income.setRpaTime(LocalDateTime.now());
                        income.setInputInvoiceCount(Objects.isNull(incomeVO.getAllJxNum()) ? null : incomeVO.getAllJxNum().toString());
                        income.setOutputInvoiceCount(Objects.isNull(incomeVO.getAllKpNum()) ? null : incomeVO.getAllKpNum().toString());
                        customerServicePeriodMonthIncomeService.addPeriodMonthIncomeInner(income, openApiData.getCreateBy(), openApiData.getDeptId());
                        needUpdateIncome = true;
                    }
                } else {
                    log.info("收入不存在且是未来账期，不新增处理。。。");
                }
            } else {
                CustomerServicePeriodMonthIncome update = new CustomerServicePeriodMonthIncome();
                update.setCustomerServiceId(customerService.getId());
                update.setPeriod(period);
                update.setId(income.getId());
                update.setAllTicketTaxAmount(StringUtils.isEmpty(incomeVO.getAllKpse()) ? BigDecimal.ZERO : new BigDecimal(incomeVO.getAllKpse()));
                update.setAllTicketAmount(StringUtils.isEmpty(incomeVO.getAllKpJe()) ? BigDecimal.ZERO : new BigDecimal(incomeVO.getAllKpJe()));
                update.setTotalInvoiceAmount(StringUtils.isEmpty(incomeVO.getAllJxJe()) ? BigDecimal.ZERO : new BigDecimal(incomeVO.getAllJxJe()));
                update.setTotalInvoiceTaxAmount(StringUtils.isEmpty(incomeVO.getAllJxSe()) ? BigDecimal.ZERO : new BigDecimal(incomeVO.getAllJxSe()));
                update.setTicketTime(LocalDateTime.now());
                update.setRpaTime(LocalDateTime.now());
                update.setInputInvoiceCount(Objects.isNull(incomeVO.getAllJxNum()) ? null : incomeVO.getAllJxNum().toString());
                update.setOutputInvoiceCount(Objects.isNull(incomeVO.getAllKpNum()) ? null : incomeVO.getAllKpNum().toString());
                customerServicePeriodMonthIncomeService.modifyPeriodMonthIncomeInner(update, openApiData.getCreateBy(), openApiData.getDeptId());
                needUpdateIncome = true;
            }
        }
        // 更新收入
        if (needUpdateIncome) {
            customerServicePeriodMonthIncomeService.updateCustomerServiceIncome(customerService.getId());
        }
    }

    public void asyncPreAuthRemind(OpenApiSyncCustomer syncCustomer, String operateName, Long deptId) {
        if (Objects.isNull(syncCustomer.getPreAuthDeliverId())) {
            return;
        }
        customerDeliverService.preAuthRemind(syncCustomer, operateName, deptId);
    }

    @Async
    public void asyncInAccountNotice(OpenApiData openApiData) {
        CCustomerService customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getTaxNumber, openApiData.getTaxNumber())
                .eq(CCustomerService::getServiceStatus, ServiceStatus.SERVICE.getCode())
                .last("limit 1"));
        if (Objects.isNull(customerService)) {
            return;
        }
        CustomerServiceInAccount customerServiceInAccount = customerServiceInAccountMapper.selectOne(new LambdaQueryWrapper<CustomerServiceInAccount>()
                .eq(CustomerServiceInAccount::getCustomerServiceId, customerService.getId())
                .eq(CustomerServiceInAccount::getIsDel, false)
                .eq(CustomerServiceInAccount::getPeriod, openApiData.getPeriod()).last("limit 1"));
        if (Objects.isNull(customerServiceInAccount)) {
            return;
        }
        CommonInAccountVO commonInAccountVO = JSONObject.parseObject(openApiData.getDataJson(), CommonInAccountVO.class);
        if (StringUtils.isEmpty(commonInAccountVO.getEmployees()) && StringUtils.isEmpty(commonInAccountVO.getTotalTax()) && StringUtils.isEmpty(commonInAccountVO.getNetProfit()) &&
                StringUtils.isEmpty(commonInAccountVO.getOperatingCosts()) && StringUtils.isEmpty(commonInAccountVO.getOperatingRevenue())) {
            // 都为空，不处理
            return;
        }
        customerServiceInAccountService.updateByCommonNotice(openApiData, commonInAccountVO, customerServiceInAccount);
    }

    @Async
    public void asyncInAccountNoticeV2(OpenApiData openApiData) {
        CCustomerService customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getId, openApiData.getCustomerServiceId()));
        if (Objects.isNull(customerService)) {
            return;
        }
        CustomerServiceInAccount customerServiceInAccount = customerServiceInAccountMapper.selectOne(new LambdaQueryWrapper<CustomerServiceInAccount>()
                .eq(CustomerServiceInAccount::getCustomerServiceId, customerService.getId())
                .eq(CustomerServiceInAccount::getIsDel, false)
                .eq(CustomerServiceInAccount::getPeriod, openApiData.getPeriod()).last("limit 1"));
        if (Objects.isNull(customerServiceInAccount)) {
            return;
        }
        CommonInAccountVO commonInAccountVO = JSONObject.parseObject(openApiData.getDataJson(), CommonInAccountVO.class);
//        if (StringUtils.isEmpty(commonInAccountVO.getEmployees()) && StringUtils.isEmpty(commonInAccountVO.getTotalTax()) && StringUtils.isEmpty(commonInAccountVO.getNetProfit()) &&
//                StringUtils.isEmpty(commonInAccountVO.getOperatingCosts()) && StringUtils.isEmpty(commonInAccountVO.getOperatingRevenue())) {
//            // 都为空，不处理
//            return;
//        }
        customerServiceInAccountService.updateByCommonNoticeV2(openApiData, commonInAccountVO, customerServiceInAccount);
    }

    @Async
    public void asyncInAccountNoticeV3(OpenApiData openApiData) {
        CCustomerService customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getId, openApiData.getCustomerServiceId()));
        if (Objects.isNull(customerService)) {
            return;
        }
        CustomerServiceCashierAccounting customerServiceCashierAccounting = customerServiceCashierAccountingMapper.selectOne(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getCustomerServiceId, customerService.getId())
                .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getPeriod, openApiData.getPeriod()).last("limit 1"));
        if (Objects.isNull(customerServiceCashierAccounting)) {
            return;
        }
        CommonInAccountVO commonInAccountVO = JSONObject.parseObject(openApiData.getDataJson(), CommonInAccountVO.class);
        customerServiceCashierAccountingService.updateByCommonNotice(openApiData, commonInAccountVO, customerServiceCashierAccounting);
    }

    @Async
    public void asyncSupplementAccountingFiles(OpenApiData openApiData) {
        CCustomerService customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getTaxNumber, openApiData.getTaxNumber())
                .eq(CCustomerService::getServiceStatus, ServiceStatus.SERVICE.getCode())
                .last("limit 1"));
        if (Objects.isNull(customerService)) {
            customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                    .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getTaxNumber, openApiData.getTaxNumber())
                    .eq(CCustomerService::getServiceStatus, ServiceStatus.END.getCode())
                    .last("limit 1"));
        }
        if (Objects.isNull(customerService)) {
            return;
        }
        CustomerServiceCashierAccounting customerServiceCashierAccounting = customerServiceCashierAccountingMapper.selectOne(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getCustomerServiceId, customerService.getId())
                .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getPeriod, openApiData.getPeriod()).last("limit 1"));
        if (Objects.isNull(customerServiceCashierAccounting)) {
            return;
        }
        CommonInAccountVO commonInAccountVO = JSONObject.parseObject(openApiData.getDataJson(), CommonInAccountVO.class);
        customerServiceCashierAccountingService.supplementFilesByCommonNotice(openApiData, commonInAccountVO, customerServiceCashierAccounting);
    }

    @Async
    public void asyncXqySetCustomerStatus(List<CCustomerService> xqySetCustomerStatusList) {
        List<XqySetCustomerRecord> records = Lists.newArrayList();
        xqySetCustomerStatusList.forEach(customerService -> {
            XqySetCustomerRecord record = new XqySetCustomerRecord();
            record.setCustomerServiceId(customerService.getId());
            record.setTaxNumber(customerService.getTaxNumber());
            record.setSetType(XqySetCustomerType.SET_CUSTOMER_STATUS.getCode());
            XqySetCustomerStatusVO vo = XqySetCustomerStatusVO.builder().taxNumber(customerService.getTaxNumber()).agentStatus(4).build();
            record.setRequestParams(JSONObject.toJSONString(vo));
            R<Boolean> result = remoteThirdpartService.xqySetCustomerStatus(vo, SecurityConstants.INNER);
            Result<Boolean> res = new Result<>();
            res.setCode(result.getCode());
            res.setMsg(result.getMsg());
            res.setData(result.getDataThrowException(false));
            record.setResult(JSONObject.toJSONString(res));
            records.add(record);
        });

        if (!ObjectUtils.isEmpty(records)) {
            xqySetCustomerRecordService.saveBatch(records);
        }
    }

    @Async
    public void asyncXqySetCustomerServiceUser(List<CCustomerService> xqySetCustomerServiceUserList, Map<Long, List<SysEmployee>> employeeMap,
                                               Map<Long, List<SysEmployee>> accountingTopEmployeeMap, Map<Long, String> deptMap) {
        List<XqySetCustomerRecord> records = Lists.newArrayList();
        xqySetCustomerServiceUserList.forEach(customerService -> {
            List<SysEmployee> employees = employeeMap.get(customerService.getAccountingDeptId());
            if (!ObjectUtils.isEmpty(employees)) {
                if (employees.size() == 1) {
                    SysEmployee employee = employees.get(0);
                    XqySetCustomerRecord record = new XqySetCustomerRecord();
                    record.setCustomerServiceId(customerService.getId());
                    record.setTaxNumber(customerService.getTaxNumber());
                    record.setSetType(XqySetCustomerType.SET_CUSTOMER_SERVICE_USER.getCode());
                    XqySetCustomerServiceUser vo = XqySetCustomerServiceUser.builder().taxNumber(customerService.getTaxNumber()).jobFunctionName("客服会计")
                            .operationType(1).userName(employee.getEmployeeName()).build();
                    record.setRequestParams(JSONObject.toJSONString(vo));
                    R<Boolean> result = remoteThirdpartService.xqySetCustomerServiceUser(vo, SecurityConstants.INNER);
                    record.setResult(JSONObject.toJSONString(result));
                    records.add(record);
                } else {
                    List<SysEmployee> topEmployees = accountingTopEmployeeMap.getOrDefault(customerService.getAccountingTopDeptId(), Lists.newArrayList());
                    topEmployees = topEmployees.stream().filter(e -> e.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList()).contains("accountingManager")).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(topEmployees)) {
                        topEmployees.forEach(e -> asyncSendMessage(e.getUserId(), e.getEmployeeId(), e.getEmployeeName(), e.getDeptId(), deptMap.getOrDefault(e.getDeptId(), ""), String.format("%s 会计小组下有多个会计，鑫启易会计同步失败。", customerService.getCustomerName()), "系统"));
                    }
                }
            }
        });
        if (!ObjectUtils.isEmpty(records)) {
            xqySetCustomerRecordService.saveBatch(records);
        }
    }

    @Async
    @Transactional
    public void asyncTaxCheckNotice(OpenApiData openApiData) {
        String params = JSONObject.parseObject(openApiData.getDataJson()).getString("params");
        if (StringUtils.isEmpty(params)) {
            return;
        }
        List<CCustomerService> customerServiceList = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getTaxNumber, openApiData.getTaxNumber()));
        if (ObjectUtils.isEmpty(customerServiceList)) {
            return;
        }
        CCustomerService customerService;
        List<CCustomerService> servicingCustomerList = customerServiceList.stream().filter(e -> Objects.equals(e.getServiceStatus(), ServiceStatus.SERVICE.getCode())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(servicingCustomerList)) {
            customerService = customerServiceList.get(0);
        } else {
            customerService = servicingCustomerList.get(0);
        }
        List<CustomerServicePeriodMonth> periodList = customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                        .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerService.getId())
                        .in(CustomerServicePeriodMonth::getPeriod, DateUtils.getNowPeriod(), DateUtils.getPrePeriod()));
        List<OpenApiSyncItem> itemList = JSONArray.parseArray(params, OpenApiSyncItem.class);
        boolean hasMedicalTag = false;
        boolean hasSocialTag = false;
        if (!ObjectUtils.isEmpty(itemList)) {
            hasMedicalTag = itemList.stream().anyMatch(item -> item.getItemCategoryName().contains("生育保险") || item.getItemCategoryName().contains("医疗保险"));
            hasSocialTag = itemList.stream().anyMatch(item -> !item.getItemCategoryName().contains("生育保险") && !item.getItemCategoryName().contains("医疗保险") && (item.getItemCategoryName().contains("保险") || item.getItemCategoryName().contains("社保")));
        }
        if (!ObjectUtils.isEmpty(itemList)) {
            try {
                OpenApiSyncRecord openApiSyncRecord = new OpenApiSyncRecord().setSourceType(2).setType(1);
                openApiSyncRecordMapper.insert(openApiSyncRecord);
                OpenApiSyncCustomer openApiSyncCustomer = new OpenApiSyncCustomer().setSycRecordId(openApiSyncRecord.getId())
                        .setCustomerName(customerService.getCustomerName()).setTaxNumber(customerService.getTaxNumber()).setCustomerServiceId(customerService.getId())
                        .setIsSuccess(true);
                openApiSyncCustomerService.save(openApiSyncCustomer);
                itemList.forEach(item -> {
                    item.setCustomerName(customerService.getCustomerName());
                    item.setTaxNumber(customerService.getTaxNumber());
                    item.setSycRecordId(openApiSyncRecord.getId());
                    item.setSyncCustomerId(openApiSyncCustomer.getId());
                    item.setIsReport("否");
                    item.setIsPaid("否");
                    item.setReportPeriod(DateUtils.periodToYeaMonth(DateUtils.getNowPeriod()));
                    setPeriodStartAndEnd(item);
                });
                openApiSyncItemService.saveBatch(itemList);
                Long userId = null;
                if (!StringUtils.isEmpty(openApiData.getCreateBy())) {
                    SysUser user = remoteUserService.getUserByNickName(openApiData.getCreateBy(), openApiData.getDeptId(), SecurityConstants.INNER).getDataThrowException();
                    if (!Objects.isNull(user)) {
                        userId = user.getUserId();
                    }
                }
                ReportDeductionGetVO vo = ReportDeductionGetVO.builder()
                        .creditCode(customerService.getCreditCode())
                        .taxNumber(customerService.getTaxNumber())
                        .customerName(customerService.getCustomerName())
                        .operator(openApiData.getCreateBy())
                        .period(DateUtils.getNowPeriod())
                        .customerServiceId(customerService.getId())
                        .userId(userId)
                        .syncRecordId(openApiSyncRecord.getId())
                        .build();
                ReportDeductionGetDTO reportDeductionResult = remoteThirdpartService.getReportDeductionList(vo, SecurityConstants.INNER).getDataThrowException(false);
                if (!Objects.isNull(reportDeductionResult)) {
                    openApiSyncItemService.dealReportDeductionList(reportDeductionResult, openApiSyncRecord.getId(), customerService.getId());
                }
            } catch (Exception e) {
                log.error("写入open_api_sync_item表失败:{}", e.getMessage());
            }
        }
        customerTaxTypeCheckService.removeAndCreateByYsb(customerService.getId(), itemList, openApiData.getCreateBy(), openApiData.getDeptId());
        // 同步最新的税种到账期
        customerServicePeriodMonthTaxTypeCheckService.deleteAndSaveNewPeriodTaxCheckByCustomerServiceIds(Collections.singletonList(customerService.getId()), DateUtils.getPrePeriod(), DateUtils.getNowPeriod(), ObjectUtils.isEmpty(periodList) ? Lists.newArrayList() : periodList.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList()));
        customerServiceService.addMedicalAndSocialTag(hasMedicalTag, hasSocialTag, customerService.getId(), periodList, openApiData.getCreateBy(), openApiData.getDeptId());
        customerServicePeriodMonthMapper.updateDeliverStatusByCustomerServiceId(customerService.getId());
    }

    private void setPeriodStartAndEnd(OpenApiSyncItem item) {
        int period = DateUtils.getPrePeriod();
        int month = period % 100;
        int year = period / 100;
        LocalDate date = LocalDate.of(year, month, 1);
        if (Objects.equals("月", item.getReportType())) {
            item.setTaxPeriodStart(date.toString());
            item.setTaxPeriodEnd(date.plusMonths(1).minusDays(1).toString());
        } else if (Objects.equals("季", item.getReportType())) {
            if (month == 3) {
                item.setTaxPeriodStart(year + "-01-01");
                item.setTaxPeriodEnd(year + "-03-31");
            } else if (month == 6) {
                item.setTaxPeriodStart(year + "-04-01");
                item.setTaxPeriodEnd(year + "-06-30");
            } else if (month == 9) {
                item.setTaxPeriodStart(year + "-07-01");
                item.setTaxPeriodEnd(year + "-09-30");
            } else if (month == 12) {
                item.setTaxPeriodStart(year + "-10-01");
                item.setTaxPeriodEnd(year + "-12-31");
            }
        } else if (Objects.equals("半年", item.getReportType())) {
            if (month == 6) {
                item.setTaxPeriodStart(year + "-01-01");
                item.setTaxPeriodEnd(year + "-06-30");
            } else if (month == 12) {
                item.setTaxPeriodStart(year + "-07-01");
                item.setTaxPeriodEnd(year + "-12-31");
            }
        } else if (Objects.equals("年", item.getReportType())) {
            if (month == 12) {
                item.setTaxPeriodStart(year + "-01-01");
                item.setTaxPeriodEnd(year + "-12-31");
            }
        }
        if (!StringUtils.isEmpty(item.getTaxPeriodEnd())) {
            item.setTaxPeriodEndMonth(item.getTaxPeriodEnd().substring(0, 7));
        }
    }

    public void test() {
        String taxNumber = "91350982MAC78TH14T";
        List<CCustomerService> customerServiceList = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getTaxNumber, taxNumber));
        if (ObjectUtils.isEmpty(customerServiceList)) {
            return;
        }
        CCustomerService customerService;
        List<CCustomerService> servicingCustomerList = customerServiceList.stream().filter(e -> Objects.equals(e.getServiceStatus(), ServiceStatus.SERVICE.getCode())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(servicingCustomerList)) {
            customerService = customerServiceList.get(0);
        } else {
            customerService = servicingCustomerList.get(0);
        }

        List<OpenApiSyncItem> itemList = Lists.newArrayList(
                new OpenApiSyncItem().setItemCategoryName("地方教育附加")
                        .setItemName("增值税地方教育附加")
                        .setActualPayTaxAmount("0")
                        .setReportType("月")
        );
        if (!ObjectUtils.isEmpty(itemList)) {
            try {
                OpenApiSyncRecord openApiSyncRecord = new OpenApiSyncRecord().setSourceType(2).setType(1);
                openApiSyncRecordMapper.insert(openApiSyncRecord);
                OpenApiSyncCustomer openApiSyncCustomer = new OpenApiSyncCustomer().setSycRecordId(openApiSyncRecord.getId())
                        .setCustomerName(customerService.getCustomerName()).setTaxNumber(customerService.getTaxNumber()).setCustomerServiceId(customerService.getId())
                        .setIsSuccess(true);
                openApiSyncCustomerService.save(openApiSyncCustomer);
                itemList.forEach(item -> {
                    item.setCustomerName(customerService.getCustomerName());
                    item.setTaxNumber(customerService.getTaxNumber());
                    item.setSycRecordId(openApiSyncRecord.getId());
                    item.setSyncCustomerId(openApiSyncCustomer.getId());
                    item.setIsReport("否");
                    item.setIsPaid("否");
                    item.setReportPeriod(DateUtils.periodToYeaMonth(DateUtils.getNowPeriod()));
                });
                openApiSyncItemService.saveBatch(itemList);

                ReportDeductionGetVO vo = ReportDeductionGetVO.builder()
                        .creditCode(customerService.getCreditCode())
                        .taxNumber(customerService.getTaxNumber())
                        .customerName(customerService.getCustomerName())
                        .operator("系统")
                        .period(DateUtils.getNowPeriod())
                        .customerServiceId(customerService.getId())
                        .userId(null)
                        .syncRecordId(openApiSyncRecord.getId())
                        .build();
                ReportDeductionGetDTO reportDeductionResult = remoteThirdpartService.getReportDeductionList(vo, SecurityConstants.INNER).getDataThrowException(false);
                if (!Objects.isNull(reportDeductionResult)) {
                    openApiSyncItemService.dealReportDeductionList(reportDeductionResult, openApiSyncRecord.getId(), customerService.getId());
                }
            } catch (Exception e) {
                log.error("写入open_api_sync_item表失败:{}", e.getMessage());
            }
        }
    }

    @Transactional
    @Async
    public void asyncInsuranceCheckNotice(OpenApiData openApiData) {
        String params = JSONObject.parseObject(openApiData.getDataJson()).getString("params");
        if (StringUtils.isEmpty(params)) {
            return;
        }
        List<CCustomerService> customerServiceList = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getTaxNumber, openApiData.getTaxNumber()));
        if (ObjectUtils.isEmpty(customerServiceList)) {
            return;
        }
        CCustomerService customerService;
        List<CCustomerService> servicingCustomerList = customerServiceList.stream().filter(e -> Objects.equals(e.getServiceStatus(), ServiceStatus.SERVICE.getCode())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(servicingCustomerList)) {
            customerService = customerServiceList.get(0);
        } else {
            customerService = servicingCustomerList.get(0);
        }
        List<CustomerServicePeriodMonth> periodList = customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerService.getId())
                .in(CustomerServicePeriodMonth::getPeriod, DateUtils.getNowPeriod(), DateUtils.getPrePeriod()));
        List<OpenApiSyncItem> itemList = JSONArray.parseArray(params, OpenApiSyncItem.class);
        boolean hasMedicalTag = false;
        boolean hasSocialTag = false;
        if (!ObjectUtils.isEmpty(itemList)) {
            hasMedicalTag = itemList.stream().anyMatch(item -> item.getItemCategoryName().contains("生育保险") || item.getItemCategoryName().contains("医疗保险"));
            hasSocialTag = itemList.stream().anyMatch(item -> !item.getItemCategoryName().contains("生育保险") && !item.getItemCategoryName().contains("医疗保险") && (item.getItemCategoryName().contains("保险") || item.getItemCategoryName().contains("社保")));
        }
        customerTaxTypeCheckService.insuranceCheck(customerService.getId(), itemList, openApiData.getCreateBy(), openApiData.getDeptId());
        customerServiceService.addMedicalAndSocialTag(hasMedicalTag, hasSocialTag, customerService.getId(), periodList, openApiData.getCreateBy(), openApiData.getDeptId());
        customerServicePeriodMonthMapper.updateDeliverStatusByCustomerServiceId(customerService.getId());
    }

    @Transactional
    @Async
    public void asyncInvoiceUpdate(OpenApiData openApiData, String sourceName) {
        if (StringUtils.isEmpty(openApiData.getDataJson())) {
            return;
        }
        List<CCustomerService> customerServiceList = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getTaxNumber, openApiData.getTaxNumber()));
        if (ObjectUtils.isEmpty(customerServiceList)) {
            return;
        }
        CCustomerService customerService;
        List<CCustomerService> servicingCustomerList = customerServiceList.stream().filter(e -> Objects.equals(e.getServiceStatus(), ServiceStatus.SERVICE.getCode())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(servicingCustomerList)) {
            customerService = customerServiceList.get(0);
        } else {
            customerService = servicingCustomerList.get(0);
        }
        CommonInvoiceUpdateVO vo = JSONObject.parseObject(openApiData.getDataJson(), CommonInvoiceUpdateVO.class);
        Integer period = openApiData.getPeriod();
        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerService.getId())
                .eq(CustomerServicePeriodMonth::getPeriod, period).last("limit 1"));
        if (Objects.isNull(vo)) {
            if (period < DateUtils.getNowPeriod()) {
                CustomerServiceCashierAccounting customerServiceCashierAccounting = customerServiceCashierAccountingService.getOne(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                        .eq(CustomerServiceCashierAccounting::getIsDel, false).eq(CustomerServiceCashierAccounting::getCustomerServiceId, customerService.getId())
                        .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                        .eq(CustomerServiceCashierAccounting::getPeriod, period), false);
                if (Objects.isNull(customerServiceCashierAccounting)) {
                    return;
                }
                if (!Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()).contains(customerServiceCashierAccounting.getDeliverStatus())) {
                    return;
                }
                List<CBusinessTagRelation> relations = businessTagRelationService.selectByBusinessIdAndBusinessType(customerServicePeriodMonth.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD.getCode());
                if (!ObjectUtils.isEmpty(relations) && relations.stream().anyMatch(e -> Objects.equals(e.getTagId(), specialTagProperties.getWupiaoshouru()))) {
                    //仅入账交付单是“待交付”或“交付待提交”的时候，交付状态改“已交付”，结果是“无账务”。
                    //
                    //操作记录：
                    //
                    //oper_type：交付
                    //
                    //oper_content：
                    //
                    //结果
                    //
                    //前置状态
                    //
                    //后置状态
                    //
                    //备注：无发票交付
                    customerServiceCashierAccountingService.updateByInvoiceUpdate(customerServiceCashierAccounting, openApiData.getCreateBy(), AccountingCashierDeliverResult.NORMAL, AccountingCashierDeliverStatus.WAIT_SUBMIT, sourceName, openApiData.getDeptId());
                } else {
                    //仅入账交付单是“待交付”或“交付待提交”的时候，交付状态改“交付待提交”，结果是“正常”。
                    //
                    //操作记录：
                    //
                    //oper_type：交付
                    //
                    //oper_content：
                    //
                    //结果
                    //
                    //前置状态
                    //
                    //后置状态
                    //
                    //备注：无发票交付
                    customerServiceCashierAccountingService.updateByInvoiceUpdate(customerServiceCashierAccounting, openApiData.getCreateBy(), AccountingCashierDeliverResult.NO_ACCOUNT, AccountingCashierDeliverStatus.WAIT_SUBMIT, sourceName, openApiData.getDeptId());
                }
            }
        } else {
            Long userId = null;
            if (!StringUtils.isEmpty(openApiData.getCreateBy())) {
                SysUser user = remoteUserService.getUserByNickName(openApiData.getCreateBy(), openApiData.getDeptId(), SecurityConstants.INNER).getDataThrowException();
                if (!Objects.isNull(user)) {
                    userId = user.getUserId();
                }
            }
            CustomerServicePeriodMonthIncome income = customerServicePeriodMonthIncomeService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>()
                    .eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerService.getId())
                    .eq(CustomerServicePeriodMonthIncome::getPeriod, period));
            if (Objects.isNull(income)) {
                if (period <= DateUtils.getNowPeriod()) {
                    income = new CustomerServicePeriodMonthIncome();
                    income.setCustomerServiceId(customerService.getId());
                    income.setPeriod(period);
                    income.setInputFile(StringUtils.isEmpty(vo.getIncomeFileLink()) ? null : JSONObject.toJSONString(CommonFileVO.builder().fileName(vo.getIncomeFileName()).fileUrl(vo.getIncomeFileLink()).build()));
                    income.setOutputFile(StringUtils.isEmpty(vo.getOutputFileLink()) ? null : JSONObject.toJSONString(CommonFileVO.builder().fileName(vo.getOutputFileName()).fileUrl(vo.getOutputFileLink()).build()));
                    customerServicePeriodMonthIncomeService.addPeriodMonthIncomeInputOutput(income, userId, openApiData.getCreateBy(), sourceName);
                }
            } else {
                CustomerServicePeriodMonthIncome update = new CustomerServicePeriodMonthIncome();
                update.setId(income.getId());
                update.setInputFile(StringUtils.isEmpty(vo.getIncomeFileLink()) ? null : JSONObject.toJSONString(CommonFileVO.builder().fileName(vo.getIncomeFileName()).fileUrl(vo.getIncomeFileLink()).build()));
                update.setOutputFile(StringUtils.isEmpty(vo.getOutputFileLink()) ? null : JSONObject.toJSONString(CommonFileVO.builder().fileName(vo.getOutputFileName()).fileUrl(vo.getOutputFileLink()).build()));
                customerServicePeriodMonthIncomeService.modifyPeriodMonthIncomeInputOutput(update, userId, openApiData.getCreateBy(), sourceName);
            }
            // 通知医社保
            Long validTime = 60 * 60 * 48L;
            String inputFileFullUrl = StringUtils.isEmpty(vo.getIncomeFileLink()) ? "" : fileService.getFullFileUrlValidTime(vo.getIncomeFileLink(), validTime);
            String outputFileFullUrl = StringUtils.isEmpty(vo.getOutputFileLink()) ? "" : fileService.getFullFileUrlValidTime(vo.getOutputFileLink(), validTime);

            Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(Collections.singletonList(customerService.getId()), TagBusinessType.CUSTOMER_SERVICE);
            if (!Objects.isNull(customerService.getBusinessDeptId())) {
                Long businessDeptId = customerService.getBusinessDeptId();
                String groupId = businessGroupProperties.getMap().get(businessDeptId);
                if (!StringUtils.isEmpty(groupId)) {
                    GroupMap groupMap = GroupMap.getByGroupId(groupId);
                    if (!Objects.isNull(groupMap)) {
                        String platType = customerServiceTagMap.getOrDefault(customerService.getId(), Lists.newArrayList()).stream().map(TagDTO::getId).collect(Collectors.toList())
                                .contains(specialTagProperties.getYq()) ? "亿企赢" : "易捷账";
                        remoteThirdpartService.incomeOutput(IncomeOutputVO.builder()
                                .creditCode(customerService.getCreditCode())
                                .groupId(groupId)
                                .groupName(groupMap.getGroupName())
                                .operator(openApiData.getCreateBy())
                                .platType(platType)
                                .period(openApiData.getPeriod())
                                .taxNumber(customerService.getTaxNumber())
                                .customerName(customerService.getCustomerName())
                                .customerServiceId(customerService.getId())
                                .outputFileLink(outputFileFullUrl)
                                .incomeFileLink(inputFileFullUrl)
                                .customerServicePeriodMonthId(Objects.isNull(customerServicePeriodMonth) ? null : customerServicePeriodMonth.getId())
                                .userId(userId)
                                .build(), SecurityConstants.INNER);
                    }
                }
            }
        }
    }

    @Transactional
    @Async
    public void asyncInAccountUpdate(CommonNoticeVO commonNoticeVO) {
        if (Objects.isNull(commonNoticeVO.getIncomeResult()) || Objects.isNull(commonNoticeVO.getOutputResult())) {
            openApiNoticeRecordService.updateSysDealFailByUuid(commonNoticeVO.getUuid(), "参数错误，incomeResult或outputResult为空");
            return;
        }
        if (StringUtils.isEmpty(commonNoticeVO.getIncomeFileLink()) && StringUtils.isEmpty(commonNoticeVO.getOutputFileLink())) {
            openApiNoticeRecordService.updateSysDealFailByUuid(commonNoticeVO.getUuid(), "参数错误，incomeFileLink和outputFileLink都为空");
            return;
        }
        CustomerServiceCashierAccounting customerServiceCashierAccounting = customerServiceCashierAccountingService.getOne(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false).eq(CustomerServiceCashierAccounting::getCustomerServiceId, commonNoticeVO.getCustomerServiceId())
                .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                .eq(CustomerServiceCashierAccounting::getPeriod, commonNoticeVO.getPeriod()), false);
        if (Objects.isNull(customerServiceCashierAccounting)) {
            openApiNoticeRecordService.updateSysDealFailByUuid(commonNoticeVO.getUuid(), "入账交付单不存在");
            return;
        }
        if (!Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()).contains(customerServiceCashierAccounting.getDeliverStatus())) {
            openApiNoticeRecordService.updateSysDealFailByUuid(commonNoticeVO.getUuid(), "交付单状态不为待交付或待提交，交付单状态为：" + AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
            return;
        }
        openApiNoticeRecordService.updateSysDealSuccessByUuid(commonNoticeVO.getUuid());
        boolean isSuccess;
        //进项文件有，进项true+销项文件有，销项true。正常
        //进项文件有，进项true+销项文件无。正常
        //进项文件无+销项文件有，销项true。正常
        //
        //其他异常
        isSuccess = (!StringUtils.isEmpty(commonNoticeVO.getIncomeFileLink()) && commonNoticeVO.getIncomeResult() && !StringUtils.isEmpty(commonNoticeVO.getOutputFileLink()) && commonNoticeVO.getOutputResult())
                || (StringUtils.isEmpty(commonNoticeVO.getIncomeFileLink()) && !StringUtils.isEmpty(commonNoticeVO.getOutputFileLink()) && commonNoticeVO.getOutputResult())
                || (StringUtils.isEmpty(commonNoticeVO.getOutputFileLink()) && !StringUtils.isEmpty(commonNoticeVO.getIncomeFileLink()) && commonNoticeVO.getIncomeResult());
        if (isSuccess) {
            // 交付待提交处理(正常交付)
            customerServiceCashierAccountingService.updateWaitSubmitDeal(customerServiceCashierAccounting, commonNoticeVO, AccountingCashierDeliverResult.NORMAL);
        } else {
            // 交付待提交处理(异常交付)
            customerServiceCashierAccountingService.updateWaitSubmitDeal(customerServiceCashierAccounting, commonNoticeVO, AccountingCashierDeliverResult.EXCEPTION);
        }
    }

    public void asyncAnuualReportComplete(OpenApiSyncCustomer syncCustomer, String operateName) {

    }

    @Async
    public void sendCheckingTask(QualityCheckingResult checkingResult, QualityCheckingRecord qualityCheckingRecord, OperateUserInfoDTO operateUserInfo) {
        Map<Long, QualityCheckingVO> qualityCheckingResultVOMap = getQualityCheckingResultVOMap(Collections.singletonList(checkingResult.getCustomerServiceId()));
        QualityCheckingVO qualityCheckingVO = qualityCheckingResultVOMap.get(checkingResult.getCustomerServiceId());
        remoteThirdpartService.qualityChecking(QualityCheckingVO.builder()
                .groupName(qualityCheckingVO.getGroupName())
                .groupId(qualityCheckingVO.getGroupId())
                .platType(qualityCheckingVO.getPlatType())
                .taxNumber(qualityCheckingVO.getTaxNumber())
                .customerName(qualityCheckingVO.getCustomerName())
                .creditCode(qualityCheckingVO.getCreditCode())
                .operator(operateUserInfo.getOperName())
                .period(checkingResult.getPeriod())
                .qualityType(checkingResult.getQualityCheckingItemId().intValue())
                .qualityCheckingResultId(checkingResult.getId())
                .qualityCheckingRecordId(qualityCheckingRecord.getId())
                .customerServiceId(checkingResult.getCustomerServiceId())
                .customerServicePeriodMonthId(checkingResult.getCustomerServicePeriodMonthId())
                .userId(operateUserInfo.getUserId())
                .deptId(operateUserInfo.getDeptId())
                .batchNo(qualityCheckingRecord.getBatchNo())
                .build(), SecurityConstants.INNER);
    }

    @Async
    public void sendCheckingTask(QualityCheckingRecord qualityCheckingRecord, String operName, Long userId, Long deptId) {
        Map<Long, QualityCheckingVO> qualityCheckingResultVOMap = getQualityCheckingResultVOMap(Collections.singletonList(qualityCheckingRecord.getCustomerServiceId()));
        QualityCheckingVO qualityCheckingVO = qualityCheckingResultVOMap.get(qualityCheckingRecord.getCustomerServiceId());
        remoteThirdpartService.qualityChecking(QualityCheckingVO.builder()
                .groupName(qualityCheckingVO.getGroupName())
                .groupId(qualityCheckingVO.getGroupId())
                .platType(qualityCheckingVO.getPlatType())
                .taxNumber(qualityCheckingVO.getTaxNumber())
                .customerName(qualityCheckingVO.getCustomerName())
                .creditCode(qualityCheckingVO.getCreditCode())
                .operator(operName)
                .period(qualityCheckingRecord.getPeriod())
                .qualityType(qualityCheckingRecord.getQualityCheckingItemId().intValue())
                .qualityCheckingResultId(qualityCheckingRecord.getQualityCheckingResultId())
                .qualityCheckingRecordId(qualityCheckingRecord.getId())
                .customerServiceId(qualityCheckingRecord.getCustomerServiceId())
                .customerServicePeriodMonthId(qualityCheckingRecord.getCustomerServicePeriodMonthId())
                .userId(userId)
                .deptId(deptId)
                .batchNo(qualityCheckingRecord.getBatchNo())
                .build(), SecurityConstants.INNER);
    }

    @Async
    public void sendCheckingTaskV2(Long customerServiceId, Long customerServicePeriodMonthId, Integer period, String operName, Long userId, Long deptId, String batchNo) {
        Map<Long, QualityCheckingVO> qualityCheckingResultVOMap = getQualityCheckingResultVOMap(Collections.singletonList(customerServiceId));
        QualityCheckingVO qualityCheckingVO = qualityCheckingResultVOMap.get(customerServiceId);
        remoteThirdpartService.qualityChecking(QualityCheckingVO.builder()
                .groupName(qualityCheckingVO.getGroupName())
                .groupId(qualityCheckingVO.getGroupId())
                .platType(qualityCheckingVO.getPlatType())
                .taxNumber(qualityCheckingVO.getTaxNumber())
                .customerName(qualityCheckingVO.getCustomerName())
                .creditCode(qualityCheckingVO.getCreditCode())
                .operator(operName)
                .period(period)
                .customerServiceId(customerServiceId)
                .customerServicePeriodMonthId(customerServicePeriodMonthId)
                .userId(userId)
                .deptId(deptId)
                .batchNo(batchNo)
                .build(), SecurityConstants.INNER);
    }

    private Map<Long, QualityCheckingVO> getQualityCheckingResultVOMap(List<Long> customerServiceIds) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return Collections.emptyMap();
        }
        Map<Long, QualityCheckingVO> result = new HashMap<>();
        Map<Long, CCustomerService> customerServiceMap = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>().eq(CCustomerService::getIsDel, false)
                .in(CCustomerService::getId, customerServiceIds)).stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
        Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServiceIds, TagBusinessType.CUSTOMER_SERVICE);
        for (Long customerServiceId : customerServiceIds) {
            CCustomerService customerService = customerServiceMap.get(customerServiceId);
            if (Objects.isNull(customerService) || Objects.isNull(customerService.getBusinessDeptId())) {
                result.put(customerServiceId, QualityCheckingVO.builder().groupId("").groupName("").platType("")
                        .customerName(Objects.isNull(customerService) ? "" : customerService.getCustomerName())
                        .creditCode(Objects.isNull(customerService) ? "" : customerService.getCreditCode())
                        .taxNumber(Objects.isNull(customerService) ? "" : customerService.getTaxNumber()).build());
            } else {
                Long businessDeptId = customerService.getBusinessDeptId();
                String groupId = businessGroupProperties.getMap().get(businessDeptId);
                String groupName = "";
                String platType = "";
                if (!StringUtils.isEmpty(groupId)) {
                    GroupMap groupMap = GroupMap.getByGroupId(groupId);
                    if (!Objects.isNull(groupMap)) {
                        groupName = groupMap.getGroupName();
                        platType = customerServiceTagMap.getOrDefault(customerService.getId(), Lists.newArrayList()).stream().map(TagDTO::getId).collect(Collectors.toList())
                                .contains(specialTagProperties.getYq()) ? "亿企赢" : "易捷账";
                    }
                }
                result.put(customerServiceId, QualityCheckingVO.builder().groupId(groupId).groupName(groupName).platType(platType)
                        .customerName(customerService.getCustomerName())
                        .creditCode(customerService.getCreditCode())
                        .taxNumber(customerService.getTaxNumber()).build());
            }
        }
        return result;
    }

    @Async
    public void sendBatchCheckingTask(List<QualityCheckingResult> resultList, Map<Long, QualityCheckingRecord> resultRecordMap, OperateUserInfoDTO operateUserInfo) {
        List<Long> customerServiceIds = resultList.stream().map(QualityCheckingResult::getCustomerServiceId).distinct().collect(Collectors.toList());
        Map<Long, QualityCheckingVO> qualityCheckingResultVOMap = getQualityCheckingResultVOMap(customerServiceIds);
        ExecutorService executorService = Executors.newFixedThreadPool(5); // 创建一个线程池
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        resultList.forEach(row -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                QualityCheckingVO qualityCheckingVO = qualityCheckingResultVOMap.get(row.getCustomerServiceId());
                QualityCheckingRecord qualityCheckingRecord = resultRecordMap.get(row.getId());
                remoteThirdpartService.qualityChecking(QualityCheckingVO.builder()
                        .groupName(qualityCheckingVO.getGroupName())
                        .groupId(qualityCheckingVO.getGroupId())
                        .platType(qualityCheckingVO.getPlatType())
                        .taxNumber(qualityCheckingVO.getTaxNumber())
                        .customerName(qualityCheckingVO.getCustomerName())
                        .creditCode(qualityCheckingVO.getCreditCode())
                        .operator(operateUserInfo.getOperName())
                        .period(row.getPeriod())
                        .qualityType(row.getQualityCheckingItemId().intValue())
                        .qualityCheckingResultId(row.getId())
                        .qualityCheckingRecordId(qualityCheckingRecord.getId())
                        .customerServiceId(row.getCustomerServiceId())
                        .customerServicePeriodMonthId(row.getCustomerServicePeriodMonthId())
                        .userId(operateUserInfo.getUserId())
                        .deptId(operateUserInfo.getDeptId())
                        .batchNo(qualityCheckingRecord.getBatchNo())
                        .build(), SecurityConstants.INNER);
            }, executorService);

            futures.add(future);
        });
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executorService.shutdown();
    }

    @Async
    public void sendBatchCheckingTask(RemoteSendQualityCheckingTaskVO vo, Long deptId, Long userId, String operName) {
        List<Long> customerServiceIds = vo.getPeriodList().stream().map(RemoteCustomerPeriodDTO::getCustomerServiceId).distinct().collect(Collectors.toList());
        Map<Long, QualityCheckingVO> qualityCheckingResultVOMap = getQualityCheckingResultVOMap(customerServiceIds);
        ExecutorService executorService = Executors.newFixedThreadPool(5); // 创建一个线程池
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        vo.getPeriodList().forEach(row -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                QualityCheckingVO qualityCheckingVO = qualityCheckingResultVOMap.get(row.getCustomerServiceId());
                remoteThirdpartService.qualityChecking(QualityCheckingVO.builder()
                        .groupName(qualityCheckingVO.getGroupName())
                        .groupId(qualityCheckingVO.getGroupId())
                        .platType(qualityCheckingVO.getPlatType())
                        .taxNumber(qualityCheckingVO.getTaxNumber())
                        .customerName(qualityCheckingVO.getCustomerName())
                        .creditCode(qualityCheckingVO.getCreditCode())
                        .operator(operName)
                        .period(row.getPeriod())
                        .customerServiceId(row.getCustomerServiceId())
                        .customerServicePeriodMonthId(row.getId())
                        .userId(userId)
                        .deptId(deptId)
                        .batchNo(vo.getBatchNo())
                        .build(), SecurityConstants.INNER);
            }, executorService);

            futures.add(future);
        });
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executorService.shutdown();
    }

    @Async
    public void asyncSendYsbNotice(Long advisorDeptId, Long id, List<TagDTO> tags) {
        List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(advisorDeptId).getDataThrowException(false);
        if (!ObjectUtils.isEmpty(employeeList)) {
            employeeList.forEach(employee ->
                    sendYsbNotice(employee.getUserId(), id, tags)
            );
        }
    }

    @Async
    public void asyncSendYsbNoticeV2(Long advisorDeptId, Long id, List<TagV2DTO> tags) {
        List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(advisorDeptId).getDataThrowException(false);
        if (!ObjectUtils.isEmpty(employeeList)) {
            employeeList.forEach(employee ->
                    sendYsbNoticeV2(employee.getUserId(), id, tags)
            );
        }
    }

    @Async
    public void asyncSendYsbNotice(Long customerServiceId, List<TagDTO> tags) {
        CCustomerService customerService = customerServiceMapper.selectById(customerServiceId);
        if (Objects.isNull(customerService) || customerService.getIsDel() || Objects.isNull(customerService.getAdvisorDeptId())) {
            return;
        }
        List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAdvisorDeptId()).getDataThrowException(false);
        if (!ObjectUtils.isEmpty(employeeList)) {
            employeeList.forEach(employee ->
                    sendYsbNotice(employee.getUserId(), customerServiceId, tags)
            );
        }
    }

    @Async
    public void asyncSendYsbNotice(Long customerServiceId) {
        CCustomerService customerService = customerServiceMapper.selectById(customerServiceId);
        if (Objects.isNull(customerService) || customerService.getIsDel() || Objects.isNull(customerService.getAdvisorDeptId())) {
            return;
        }
        List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAdvisorDeptId()).getDataThrowException(false);
        if (!ObjectUtils.isEmpty(employeeList)) {
            Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(Collections.singletonList(customerServiceId), TagBusinessType.CUSTOMER_SERVICE);
            List<TagDTO> tags = tagMap.getOrDefault(customerServiceId, Lists.newArrayList());
            List<TagDTO> finalTags = tags.stream().map(tag -> TagDTO.builder().id(tag.getId()).fullTagName(tag.getFullTagName()).build()).collect(Collectors.toList());
            employeeList.forEach(employee ->
                    sendYsbNotice(employee.getUserId(), customerServiceId, finalTags)
            );
        }
    }

    @Async
    public void sendYsbNotice(Long toUserId, Long customerServiceId, List<TagDTO> tags) {
        YsbNoticeVO ysbNoticeVO = new YsbNoticeVO();
        ysbNoticeVO.setNoticeType("标签");
        ysbNoticeVO.setToUserId(toUserId);
        Map<String, Object> data = new HashMap<>();
        data.put("userId", toUserId);
        data.put("customerServiceId", customerServiceId);
        data.put("tags", tags);
        ysbNoticeVO.setData(data);
        remoteThirdpartService.ysbNotice(ysbNoticeVO, SecurityConstants.INNER);
    }

    @Async
    public void sendYsbNoticeV2(Long toUserId, Long customerServiceId, List<TagV2DTO> tags) {
        YsbNoticeVO ysbNoticeVO = new YsbNoticeVO();
        ysbNoticeVO.setNoticeType("标签");
        ysbNoticeVO.setToUserId(toUserId);
        Map<String, Object> data = new HashMap<>();
        data.put("userId", toUserId);
        data.put("customerServiceId", customerServiceId);
        data.put("tags", tags);
        ysbNoticeVO.setData(data);
        remoteThirdpartService.ysbNotice(ysbNoticeVO, SecurityConstants.INNER);
    }

    @Async
    public void sendYsbNotice(Long toUserId, String toUserName, Long sendUserId, String sendUserName, String content, String type, Long workOrderId) {
        YsbNoticeVO ysbNoticeVO = new YsbNoticeVO();
        ysbNoticeVO.setNoticeType("工单");
        ysbNoticeVO.setToUserId(toUserId);
        Map<String, Object> data = new HashMap<>();
        data.put("workOrderId", workOrderId);
        data.put("type", type);
        data.put("content", content);
        data.put("fromUserId", sendUserId);
        data.put("fromUserName", sendUserName);
        data.put("toUserId", toUserId);
        data.put("toUserName", toUserName);
        ysbNoticeVO.setData(data);
        remoteThirdpartService.ysbNotice(ysbNoticeVO, SecurityConstants.INNER);
    }

    @Async
    public void asyncInAccountAutoSettlement(OpenApiData openApiData) {
        CCustomerService customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getId, openApiData.getCustomerServiceId()));
        if (Objects.isNull(customerService)) {
            return;
        }
        CustomerServiceCashierAccounting customerServiceCashierAccounting = customerServiceCashierAccountingMapper.selectOne(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getCustomerServiceId, customerService.getId())
                .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getPeriod, openApiData.getPeriod()).last("limit 1"));
        if (Objects.isNull(customerServiceCashierAccounting)) {
            return;
        }
        InAccountAutoSettlementVO autoSettlementVO = JSONObject.parseObject(openApiData.getDataJson(), InAccountAutoSettlementVO.class);
        customerServiceCashierAccountingService.autoSettlement(openApiData, autoSettlementVO, customerServiceCashierAccounting);
    }
}
