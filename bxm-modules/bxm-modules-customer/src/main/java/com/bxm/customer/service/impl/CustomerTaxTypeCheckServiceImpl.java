package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.customer.domain.CustomerTaxTypeCheck;
import com.bxm.customer.domain.OpenApiSyncItem;
import com.bxm.customer.domain.vo.CustomerServiceTaxTypeCheckVO;
import com.bxm.customer.mapper.CustomerTaxTypeCheckMapper;
import com.bxm.customer.service.ICustomerTaxTypeCheckService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysUser;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户服务税种核定Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Service
@Slf4j
public class CustomerTaxTypeCheckServiceImpl extends ServiceImpl<CustomerTaxTypeCheckMapper, CustomerTaxTypeCheck> implements ICustomerTaxTypeCheckService
{
    private static final Map<String, Integer> REPORT_TYPE_MAP;

    static {
        Map<String, Integer> map = new HashMap<>();
        map.put("月", 1);
        map.put("季", 2);
        map.put("年", 3);
        map.put("次", 4);
        map.put("半年", 5);
        REPORT_TYPE_MAP = Collections.unmodifiableMap(map);
    }

    @Autowired
    private CustomerTaxTypeCheckMapper customerTaxTypeCheckMapper;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询客户服务税种核定
     * 
     * @param id 客户服务税种核定主键
     * @return 客户服务税种核定
     */
    @Override
    public CustomerTaxTypeCheck selectCustomerTaxTypeCheckById(Long id)
    {
        return customerTaxTypeCheckMapper.selectCustomerTaxTypeCheckById(id);
    }

    /**
     * 查询客户服务税种核定列表
     * 
     * @param customerTaxTypeCheck 客户服务税种核定
     * @return 客户服务税种核定
     */
    @Override
    public List<CustomerTaxTypeCheck> selectCustomerTaxTypeCheckList(CustomerTaxTypeCheck customerTaxTypeCheck)
    {
        return customerTaxTypeCheckMapper.selectCustomerTaxTypeCheckList(customerTaxTypeCheck);
    }

    /**
     * 新增客户服务税种核定
     * 
     * @param customerTaxTypeCheck 客户服务税种核定
     * @return 结果
     */
    @Override
    public int insertCustomerTaxTypeCheck(CustomerTaxTypeCheck customerTaxTypeCheck)
    {
        customerTaxTypeCheck.setCreateTime(DateUtils.getNowDate());
        return customerTaxTypeCheckMapper.insertCustomerTaxTypeCheck(customerTaxTypeCheck);
    }

    /**
     * 修改客户服务税种核定
     * 
     * @param customerTaxTypeCheck 客户服务税种核定
     * @return 结果
     */
    @Override
    public int updateCustomerTaxTypeCheck(CustomerTaxTypeCheck customerTaxTypeCheck)
    {
        customerTaxTypeCheck.setUpdateTime(DateUtils.getNowDate());
        return customerTaxTypeCheckMapper.updateCustomerTaxTypeCheck(customerTaxTypeCheck);
    }

    /**
     * 批量删除客户服务税种核定
     * 
     * @param ids 需要删除的客户服务税种核定主键
     * @return 结果
     */
    @Override
    public int deleteCustomerTaxTypeCheckByIds(Long[] ids)
    {
        return customerTaxTypeCheckMapper.deleteCustomerTaxTypeCheckByIds(ids);
    }

    /**
     * 删除客户服务税种核定信息
     * 
     * @param id 客户服务税种核定主键
     * @return 结果
     */
    @Override
    public int deleteCustomerTaxTypeCheckById(Long id)
    {
        return customerTaxTypeCheckMapper.deleteCustomerTaxTypeCheckById(id);
    }

    @Override
    public List<CustomerServiceTaxTypeCheckVO> selectCustomerTaxTypeCheckByCustomerServiceId(Long customerServiceId, Integer taxType) {
        if (Objects.isNull(customerServiceId)) {
            return Collections.emptyList();
        }
        List<CustomerTaxTypeCheck> customerTaxTypeCheckList = selectByCustomerServiceId(customerServiceId);
        if (ObjectUtils.isEmpty(customerTaxTypeCheckList)) {
            if (Objects.equals(taxType, 2)) {
                customerTaxTypeCheckList = selectByCustomerServiceId(-1L);
            } else {
                customerTaxTypeCheckList = selectByCustomerServiceId(0L);
            }
        }
        if (ObjectUtils.isEmpty(customerTaxTypeCheckList)) {
            return Collections.emptyList();
        }
        return customerTaxTypeCheckList.stream().map(row -> CustomerServiceTaxTypeCheckVO.builder()
                .id(row.getId())
                .taxTypeName(row.getTaxType())
                .reportType(row.getReportType())
                .customerServiceId(row.getCustomerServiceId())
                .taxPeriodStart(StringUtils.isEmpty(row.getTaxPeriodStart()) ? null : row.getTaxPeriodStart())
                .taxPeriodEnd(StringUtils.isEmpty(row.getTaxPeriodEnd()) ? null : (row.getTaxPeriodEnd().startsWith("9999") ? "永久" : row.getTaxPeriodEnd()))
                .build()).collect(Collectors.toList());
    }

    @Override
    public List<CustomerTaxTypeCheck> selectByCustomerServiceId(Long customerServiceId) {
        if (Objects.isNull(customerServiceId)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(new LambdaQueryWrapper<CustomerTaxTypeCheck>().eq(CustomerTaxTypeCheck::getCustomerServiceId, customerServiceId)
                .orderByAsc(CustomerTaxTypeCheck::getReportType).orderByDesc(CustomerTaxTypeCheck::getId));
    }

    @Override
    @Transactional
    public void removeAndCreateByXqy(Long customerServiceId, List<OpenApiSyncItem> itemList, String operName, Long deptId) {
        if (!ObjectUtils.isEmpty(itemList)) {
            Map<Integer, List<String>> map = new HashMap<>();
            List<CustomerTaxTypeCheck> newList = Lists.newArrayList();
            LocalDateTime now = LocalDateTime.now();
            for (OpenApiSyncItem row : itemList) {
                if (!StringUtils.isEmpty(row.getTaxPeriodEnd())) {
                    try {
                        LocalDateTime end = LocalDateTime.parse(row.getTaxPeriodEnd(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        if (end.isBefore(now)) {
                            continue;
                        }
                    } catch (Exception e) {
                        log.error("时间格式转换异常,:{}", row.getTaxPeriodEnd());
                    }
                }
                String taxType;
                if (Objects.equals(row.getItemCategoryName(), "增值税")) {
                    taxType = "增值税";
                } else if (Objects.equals(row.getItemCategoryName(), "个人所得税")) {
                    if (!StringUtils.isEmpty(row.getItemName())) {
                        if (row.getItemName().contains("工资薪金")) {
                            taxType = "个人所得税";
                        } else {
                            taxType = "个人所得税-" + row.getItemName();
                        }
                    } else {
                        taxType = "个人所得税";
                    }
                } else {
                    if (StringUtils.isEmpty(row.getItemName())) {
                        taxType = row.getItemCategoryName();
                    } else {
                        taxType = row.getItemCategoryName() + "-" + row.getItemName();
                    }
                }
                if (!Objects.equals("社保", row.getItemCategoryName())) {
                    Integer reportType = REPORT_TYPE_MAP.getOrDefault(row.getReportType(), 1);
                    if (!map.containsKey(reportType)) {
                        map.put(reportType, Lists.newArrayList(taxType));
                        newList.add(new CustomerTaxTypeCheck().setCustomerServiceId(customerServiceId)
                                .setTaxType(taxType)
                                .setReportType(reportType)
                                .setTaxPeriodStart(StringUtils.isEmpty(row.getTaxPeriodStart()) ? null : row.getTaxPeriodStart().substring(0, 10))
                                .setTaxPeriodEnd(StringUtils.isEmpty(row.getTaxPeriodEnd()) ? null : row.getTaxPeriodEnd().substring(0, 10)));
                    } else {
                        List<String> taxTypes = map.get(reportType);
                        if (!taxTypes.contains(taxType)) {
                            taxTypes.add(taxType);
                            map.put(reportType, taxTypes);
                            newList.add(new CustomerTaxTypeCheck().setCustomerServiceId(customerServiceId)
                                    .setTaxType(taxType)
                                    .setReportType(reportType)
                                    .setTaxPeriodStart(StringUtils.isEmpty(row.getTaxPeriodStart()) ? null : row.getTaxPeriodStart().substring(0, 10))
                                    .setTaxPeriodEnd(StringUtils.isEmpty(row.getTaxPeriodEnd()) ? null : row.getTaxPeriodEnd().substring(0, 10)));
                        }
                    }
                }
            }
            if (!ObjectUtils.isEmpty(newList)) {
                List<CustomerTaxTypeCheck> old = selectByCustomerServiceId(customerServiceId);
                if (!ObjectUtils.isEmpty(old)) {
                    removeByIds(old.stream().map(CustomerTaxTypeCheck::getId).collect(Collectors.toList()));
                }
                saveBatch(newList);
            }
            StringBuilder operContent = new StringBuilder("更新来源：01\r\n");
            for (OpenApiSyncItem item : itemList) {
                String taxName = "";
                if (StringUtils.isEmpty(item.getItemCategoryName()) && !StringUtils.isEmpty(item.getItemName())) {
                    taxName = item.getItemName();
                } else if (!StringUtils.isEmpty(item.getItemCategoryName()) && StringUtils.isEmpty(item.getItemName())) {
                    taxName = item.getItemCategoryName();
                } else if (!StringUtils.isEmpty(item.getItemCategoryName()) && !StringUtils.isEmpty(item.getItemName())) {
                    taxName = item.getItemCategoryName() + "-" + item.getItemName();
                }
                if (StringUtils.isEmpty(taxName)) {
                    continue;
                }
                operContent.append(String.format("%s，%s ~ %s，申报周期：%s",
                        taxName,
                        StringUtils.isEmpty(item.getTaxPeriodStart()) ? "" : item.getTaxPeriodStart().substring(0, 10),
                        StringUtils.isEmpty(item.getTaxPeriodEnd()) ? "" : (Objects.equals("9999-12-31 00:00:00", item.getTaxPeriodEnd()) ? "永久" : item.getTaxPeriodEnd().substring(0, 10)),
                        StringUtils.isEmpty(item.getReportType()) ? "" : item.getReportType()));
                operContent.append("\r\n");
            }
//            String operContent = String.format("月报税种为：%s，季报税种为：%s，次报税种为：%s，年报税种为：%s，半年报税种为：%s，无需申报税种为：%s，",
//                    newList.stream().filter(taxType -> taxType.getReportType() == 1).map(CustomerTaxTypeCheck::getTaxType).collect(Collectors.joining(",")),
//                    newList.stream().filter(taxType -> taxType.getReportType() == 2).map(CustomerTaxTypeCheck::getTaxType).collect(Collectors.joining(",")),
//                    newList.stream().filter(taxType -> taxType.getReportType() == 3).map(CustomerTaxTypeCheck::getTaxType).collect(Collectors.joining(",")),
//                    newList.stream().filter(taxType -> taxType.getReportType() == 4).map(CustomerTaxTypeCheck::getTaxType).collect(Collectors.joining(",")),
//                    newList.stream().filter(taxType -> taxType.getReportType() == 5).map(CustomerTaxTypeCheck::getTaxType).collect(Collectors.joining(",")),
//                    newList.stream().filter(taxType -> taxType.getReportType() == 6).map(CustomerTaxTypeCheck::getTaxType).collect(Collectors.joining(","))
//            );
            Long userId = null;
            if (!StringUtils.isEmpty(operName)) {
                SysUser user = remoteUserService.getUserByNickName(operName, deptId, SecurityConstants.INNER).getDataThrowException();
                if (!Objects.isNull(user)) {
                    userId = user.getUserId();
                }
            }
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceId)
                        .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                        .setDeptId(null)
                        .setOperType("税种核定")
                        .setOperContent(operContent.toString())
                        .setOperName(operName)
                        .setOperUserId(userId)
                        .setCreateBy(Constants.XQY_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    @Transactional
    public void removeAndCreateByYsb(Long customerServiceId, List<OpenApiSyncItem> itemList, String operName, Long deptId) {
        if (!ObjectUtils.isEmpty(itemList)) {
            Map<Integer, List<String>> map = new HashMap<>();
            List<CustomerTaxTypeCheck> newList = Lists.newArrayList();
//            LocalDateTime now = LocalDateTime.now();
            for (OpenApiSyncItem row : itemList) {
//                if (!StringUtils.isEmpty(row.getTaxPeriodEnd())) {
//                    try {
//                        LocalDateTime end = LocalDateTime.parse(row.getTaxPeriodEnd() + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//                        if (end.isBefore(now)) {
//                            continue;
//                        }
//                    } catch (Exception e) {
//                        log.error("时间格式转换异常,:{}", row.getTaxPeriodEnd());
//                    }
//                }
                String taxType = getTaxTypeNameByItemCategoryNameAndItemName(row.getItemCategoryName(), row.getItemName());
                if (!Objects.equals("社保", row.getItemCategoryName())) {
                    Integer reportType = REPORT_TYPE_MAP.getOrDefault(row.getReportType(), 1);
                    if (!map.containsKey(reportType)) {
                        map.put(reportType, Lists.newArrayList(taxType));
                        newList.add(new CustomerTaxTypeCheck().setCustomerServiceId(customerServiceId)
                                .setTaxType(taxType)
                                .setReportType(reportType)
                                .setTaxPeriodStart(StringUtils.isEmpty(row.getTaxPeriodStart()) ? null : row.getTaxPeriodStart().substring(0, 10))
                                .setTaxPeriodEnd(StringUtils.isEmpty(row.getTaxPeriodEnd()) ? null : row.getTaxPeriodStart().substring(0, 10)));
                    } else {
                        List<String> taxTypes = map.get(reportType);
                        if (!taxTypes.contains(taxType)) {
                            taxTypes.add(taxType);
                            map.put(reportType, taxTypes);
                            newList.add(new CustomerTaxTypeCheck().setCustomerServiceId(customerServiceId)
                                    .setTaxType(taxType)
                                    .setReportType(reportType)
                                    .setTaxPeriodStart(StringUtils.isEmpty(row.getTaxPeriodStart()) ? null : row.getTaxPeriodStart().substring(0, 10))
                                    .setTaxPeriodEnd(StringUtils.isEmpty(row.getTaxPeriodEnd()) ? null : row.getTaxPeriodStart().substring(0, 10)));
                        }
                    }
                }
            }
            if (!ObjectUtils.isEmpty(newList)) {
                Map<Integer, List<CustomerTaxTypeCheck>> taxMap = newList.stream().collect(Collectors.groupingBy(CustomerTaxTypeCheck::getReportType));
                List<CustomerTaxTypeCheck> finalNewList = Lists.newArrayList();
                for (Map.Entry<Integer, List<CustomerTaxTypeCheck>> entry : taxMap.entrySet()) {
                    List<CustomerTaxTypeCheck> checkList = entry.getValue();
                    if (checkList.stream().filter(row -> Objects.equals(row.getTaxType(), "个人所得税")).count() >= 2) {
                        // 如果个人所得税大于等于2个,移除所有“个人所得税”的税种，然后添加一个“个人所得税”，一个“个人所得税-其他”,然后全部添加到finalNewList中
                        CustomerTaxTypeCheck taxTypeCheck = checkList.stream().filter(row -> Objects.equals(row.getTaxType(), "个人所得税")).findFirst().get();
                        String taxPeriodStart = taxTypeCheck.getTaxPeriodStart();
                        String taxPeriodEnd = taxTypeCheck.getTaxPeriodEnd();
                        checkList.removeIf(row -> Objects.equals(row.getTaxType(), "个人所得税"));
                        checkList.add(new CustomerTaxTypeCheck().setCustomerServiceId(customerServiceId)
                                .setTaxType("个人所得税")
                                .setReportType(entry.getKey())
                                .setTaxPeriodStart(StringUtils.isEmpty(taxPeriodStart) ? null : taxPeriodStart.substring(0, 10))
                                .setTaxPeriodEnd(StringUtils.isEmpty(taxPeriodEnd) ? null : taxPeriodEnd.substring(0, 10)));
                        checkList.add(new CustomerTaxTypeCheck().setCustomerServiceId(customerServiceId)
                                .setTaxType("个人所得税-其他")
                                .setReportType(entry.getKey())
                                .setTaxPeriodStart(StringUtils.isEmpty(taxPeriodStart) ? null : taxPeriodStart.substring(0, 10))
                                .setTaxPeriodEnd(StringUtils.isEmpty(taxPeriodEnd) ? null : taxPeriodEnd.substring(0, 10)));
                        finalNewList.addAll(checkList);
                    } else {
                        finalNewList.addAll(checkList);
                    }
                }
                List<CustomerTaxTypeCheck> old = selectByCustomerServiceId(customerServiceId);
                if (!ObjectUtils.isEmpty(old)) {
                    removeByIds(old.stream().map(CustomerTaxTypeCheck::getId).collect(Collectors.toList()));
                }
                saveBatch(finalNewList);
            }
            StringBuilder operContent = new StringBuilder("更新来源：02\r\n");
            for (OpenApiSyncItem item : itemList) {
                String taxName = "";
                if (StringUtils.isEmpty(item.getItemCategoryName()) && !StringUtils.isEmpty(item.getItemName())) {
                    taxName = item.getItemName();
                } else if (!StringUtils.isEmpty(item.getItemCategoryName()) && StringUtils.isEmpty(item.getItemName())) {
                    taxName = item.getItemCategoryName();
                } else if (!StringUtils.isEmpty(item.getItemCategoryName()) && !StringUtils.isEmpty(item.getItemName())) {
                    taxName = item.getItemCategoryName() + "-" + item.getItemName();
                }
                if (StringUtils.isEmpty(taxName)) {
                    continue;
                }
                operContent.append(String.format("%s，%s ~ %s，申报周期：%s",
                        taxName,
                        StringUtils.isEmpty(item.getTaxPeriodStart()) ? "" : item.getTaxPeriodStart(),
                        StringUtils.isEmpty(item.getTaxPeriodEnd()) ? "" : (Objects.equals("9999-12-31", item.getTaxPeriodEnd()) ? "永久" : item.getTaxPeriodEnd()),
                        StringUtils.isEmpty(item.getReportType()) ? "" : item.getReportType()));
                operContent.append("\r\n");
            }
            Long userId = null;
            if (!StringUtils.isEmpty(operName)) {
                SysUser user = remoteUserService.getUserByNickName(operName, deptId, SecurityConstants.INNER).getDataThrowException();
                if (!Objects.isNull(user)) {
                    userId = user.getUserId();
                }
            }
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceId)
                        .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                        .setDeptId(null)
                        .setOperType("税种核定")
                        .setOperContent(operContent.toString())
                        .setOperName(operName)
                        .setOperUserId(userId)
                        .setCreateBy(Constants.YSB_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    private String getTaxTypeNameByItemCategoryNameAndItemName(String itemCategoryName, String itemName) {
        String taxType;
        if (Objects.equals(itemCategoryName, "增值税")) {
            taxType = "增值税";
        } else if (Objects.equals(itemCategoryName, "个人所得税")) {
            if (!StringUtils.isEmpty(itemName)) {
                if (itemName.contains("工资薪金")) {
                    taxType = "个人所得税";
                } else {
                    taxType = "个人所得税-" + itemName;
                }
            } else {
                taxType = "个人所得税";
            }
        } else {
            if (StringUtils.isEmpty(itemName)) {
                taxType = itemCategoryName;
            } else {
                taxType = itemCategoryName + "-" + itemName;
            }
        }
        return taxType;
    }

    @Override
    @Transactional
    public void insuranceCheck(Long customerServiceId, List<OpenApiSyncItem> itemList, String operName, Long deptId) {
        if (!ObjectUtils.isEmpty(itemList)) {
            StringBuilder operContent = new StringBuilder("更新来源：02\r\n");
            for (OpenApiSyncItem item : itemList) {
                String taxName = "";
                if (StringUtils.isEmpty(item.getItemCategoryName()) && !StringUtils.isEmpty(item.getItemName())) {
                    taxName = item.getItemName();
                } else if (!StringUtils.isEmpty(item.getItemCategoryName()) && StringUtils.isEmpty(item.getItemName())) {
                    taxName = item.getItemCategoryName();
                } else if (!StringUtils.isEmpty(item.getItemCategoryName()) && !StringUtils.isEmpty(item.getItemName())) {
                    taxName = item.getItemCategoryName() + "-" + item.getItemName();
                }
                if (StringUtils.isEmpty(taxName)) {
                    continue;
                }
                operContent.append(String.format("%s，%s ~ %s，申报周期：%s",
                        taxName,
                        StringUtils.isEmpty(item.getTaxPeriodStart()) ? "" : item.getTaxPeriodStart(),
                        StringUtils.isEmpty(item.getTaxPeriodEnd()) ? "" : (Objects.equals("9999-12-31", item.getTaxPeriodEnd()) ? "永久" : item.getTaxPeriodEnd()),
                        StringUtils.isEmpty(item.getReportType()) ? "" : item.getReportType()));
                operContent.append("\r\n");
            }
            Long userId = null;
            if (!StringUtils.isEmpty(operName)) {
                SysUser user = remoteUserService.getUserByNickName(operName, deptId, SecurityConstants.INNER).getDataThrowException();
                if (!Objects.isNull(user)) {
                    userId = user.getUserId();
                }
            }
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceId)
                        .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                        .setDeptId(null)
                        .setOperType("险种更新")
                        .setOperContent(operContent.toString())
                        .setOperName(operName)
                        .setOperUserId(userId)
                        .setCreateBy(Constants.YSB_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    public Map<Long, List<CustomerTaxTypeCheck>> getBatchByCustomerServiceId(List<Long> customerServiceIds) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return Collections.emptyMap();
        }
        customerServiceIds.add(0L);
        customerServiceIds.add(-1L);
        return list(new LambdaQueryWrapper<CustomerTaxTypeCheck>().in(CustomerTaxTypeCheck::getCustomerServiceId, customerServiceIds))
                .stream().collect(Collectors.groupingBy(CustomerTaxTypeCheck::getCustomerServiceId));
    }
}
