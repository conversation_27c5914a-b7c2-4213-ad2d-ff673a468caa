package com.bxm.customer.domain.dto.tag;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TagManagerDTO {

    @ApiModelProperty("标签id")
    @Excel(name = "标签id")
    private Long id;

    @ApiModelProperty("标签名称")
    @Excel(name = "标签名称")
    private String tagName;

    @ApiModelProperty("标签类型，1-系统标签，2-集团标签")
    private Integer tagType;

    @ApiModelProperty("标签类型")
    @Excel(name = "标签类型")
    private String tagTypeName;

    @ApiModelProperty("是否自定义标签")
    private Boolean isCustomize;

    @ApiModelProperty("是否自定义标签")
    @Excel(name = "是否自定义标签")
    private String isCustomizeName;

    @ApiModelProperty("标签状态，true-启用，false-禁用")
    private Boolean status;

    @ApiModelProperty("标签状态")
    @Excel(name = "标签状态")
    private String statusName;

    @ApiModelProperty("错误原因")
    @Excel(name = "错误原因")
    private String errorMsg;
}
