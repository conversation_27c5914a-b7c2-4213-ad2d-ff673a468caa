package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.domain.CTag;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.tag.CreateTagResultDTO;
import com.bxm.customer.domain.dto.tag.TagManagerDTO;
import com.bxm.customer.domain.dto.tag.TagV2DTO;
import com.bxm.customer.domain.vo.tag.CreateCustomizeTagVO;
import com.bxm.customer.domain.vo.tag.CreateTagVO;
import com.bxm.customer.domain.vo.tag.EditTagBatchVO;
import com.bxm.customer.domain.vo.tag.EditTagSingleVO;
import com.bxm.customer.service.TagService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@RequestMapping("/tag")
@Api(tags = "标签相关")
@RestController
public class TagController {

    @Autowired
    private TagService tagService;

    @Autowired
    private RedisService redisService;

    @GetMapping("/tagManagerList")
    @ApiOperation("标签管理列表")
    public Result<IPage<TagManagerDTO>> tagManagerList(@RequestParam(value = "tagName", required = false) @ApiParam("标签名") String tagName,
                                                       @RequestParam(value = "isCustomize", required = false) @ApiParam("是否自定义，0-否，1-是") Integer isCustomize,
                                                       @RequestParam(value = "status", required = false) @ApiParam("标签状态，0-禁用，1-启用") Integer status,
                                                       @RequestParam("pageNum") @ApiParam("页码") Integer pageNum,
                                                       @RequestParam("pageSize") @ApiParam("页大小") Integer pageSize,
                                                       @RequestHeader("deptId") Long deptId) {
        return Result.ok(tagService.tagManagerList(tagName, isCustomize, status, pageNum, pageSize, deptId));
    }

    @PostMapping("/deleteTag")
    @ApiOperation("删除标签，单个删除传id，批量删除传ids")
    public Result<TCommonOperateDTO<CTag>> deleteTag(@RequestBody CommonIdVO vo,
                                                     @RequestHeader("deptId") Long deptId) {
        return Result.ok(tagService.deleteTag(vo, deptId));
    }

    @PostMapping("/changeTagStatus")
    @ApiOperation("修改标签状态，单个修改传id和targetStatus，批量修改传ids和targetStatus，targetStatus为修改目标状态，0代表修改为禁用，1-代表修改为启用")
    public Result<TCommonOperateDTO<CTag>> changeTagStatus(@RequestBody CommonIdVO vo,
                                                                    @RequestHeader("deptId") Long deptId) {
        return Result.ok(tagService.changeTagStatus(vo, deptId));
    }

    @PostMapping("/convertTag")
    @ApiOperation("转换标签，单个转换传id，批量转换传ids")
    public Result<TCommonOperateDTO<CTag>> convertTag(@RequestBody CommonIdVO vo,
                                                               @RequestHeader("deptId") Long deptId) {
        return Result.ok(tagService.convertTag(vo, deptId));
    }

    @PostMapping("/downloadOperateErrorRecord/{batchNo}")
    @ApiOperation("统一的下载操作异常的记录表格（同步导出）")
    public void downloadOperateErrorRecord(HttpServletResponse response, @PathVariable("batchNo") String batchNo) {
        List<TagManagerDTO> errorDTOList = redisService.getLargeCacheList(CacheConstants.TAG_OPERATE_ERROR_RECORD + batchNo, 500);
        errorDTOList = ObjectUtils.isEmpty(errorDTOList) ? Lists.newArrayList() : errorDTOList;
        ExcelUtil<TagManagerDTO> util = new ExcelUtil<>(TagManagerDTO.class);
        util.exportExcel(response, errorDTOList, "标签操作异常记录");
    }

    @PostMapping("/createTag")
    @ApiOperation("创建标签")
    public Result<CreateTagResultDTO> createTag(@RequestBody CreateTagVO vo,
                                                 @RequestHeader("deptId") Long deptId) {
        return Result.ok(tagService.createTag(vo, deptId));
    }

    @PostMapping("/createCustomizeTag")
    @ApiOperation("创建自定义标签")
    public Result<TagV2DTO> createCustomizeTag(@RequestBody @Valid CreateCustomizeTagVO vo,
                                            @RequestHeader("deptId") Long deptId) {
        return Result.ok(tagService.createCustomizeTag(vo, deptId));
    }

    @PostMapping("/editTagSingle")
    @ApiOperation("编辑标签(单体)")
    public Result editTagSingle(@RequestBody EditTagSingleVO vo,
                                @RequestHeader("deptId") Long deptId) {
        tagService.editTagSingle(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/editTagBatch")
    @ApiOperation("编辑标签(批量)")
    public Result editTagBatch(@RequestBody EditTagBatchVO vo,
                               @RequestHeader("deptId") Long deptId) {
        tagService.editTagBatch(vo, deptId);
        return Result.ok();
    }
}
