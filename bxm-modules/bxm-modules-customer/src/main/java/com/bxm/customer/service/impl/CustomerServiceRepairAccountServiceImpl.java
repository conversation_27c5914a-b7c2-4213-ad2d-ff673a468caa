package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.TaxType;
import com.bxm.common.core.enums.YesNo;
import com.bxm.common.core.enums.docHandover.WholeLevel;
import com.bxm.common.core.enums.inAccount.InAccountStatus;
import com.bxm.common.core.enums.repairAccount.RepairAccountDeliverStatus;
import com.bxm.common.core.enums.repairAccount.RepairAccountFileType;
import com.bxm.common.core.enums.repairAccount.RepairAccountStatus;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.docHandover.DocHandoverInstrumentDTO;
import com.bxm.customer.domain.dto.docHandover.DocHandoverSimpleDTO;
import com.bxm.customer.domain.dto.repairAccount.*;
import com.bxm.customer.domain.vo.CommonIdsSearchVO;
import com.bxm.customer.domain.vo.TagSearchVO;
import com.bxm.customer.domain.vo.docHandover.AddDocHandoverBaseVO;
import com.bxm.customer.domain.vo.repairAccount.*;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import com.bxm.customer.mapper.CustomerServiceRepairAccountMapper;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 补账Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Slf4j
@Service
public class CustomerServiceRepairAccountServiceImpl extends ServiceImpl<CustomerServiceRepairAccountMapper, CustomerServiceRepairAccount> implements ICustomerServiceRepairAccountService {
    private static final Integer BIZ_LOG_TYPE = BusinessLogBusinessType.REPAIR_ACCOUNT.getCode();
    public static final Integer ADD_FROM_TYPE = 2;

    @Autowired
    private CustomerServiceRepairAccountMapper customerServiceRepairAccountMapper;

    @Autowired
    private ICustomerServiceDocHandoverService iCustomerServiceDocHandoverService;

    @Autowired
    private ICCustomerServiceService icCustomerServiceService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private ICustomerServiceRepairAccountFileService iCustomerServiceRepairAccountFileService;

    @Autowired
    private ICustomerServiceInAccountService iCustomerServiceInAccountService;

    @Autowired
    private ICustomerServicePeriodMonthService iCustomerServicePeriodMonthService;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    /**
     * 查询补账
     *
     * @param id 补账主键
     * @return 补账
     */
    @Override
    public CustomerServiceRepairAccount selectCustomerServiceRepairAccountById(Long id) {
        return customerServiceRepairAccountMapper.selectCustomerServiceRepairAccountById(id);
    }

    /**
     * 查询补账列表
     *
     * @param customerServiceRepairAccount 补账
     * @return 补账
     */
    @Override
    public List<CustomerServiceRepairAccount> selectCustomerServiceRepairAccountList(CustomerServiceRepairAccount customerServiceRepairAccount) {
        return customerServiceRepairAccountMapper.selectCustomerServiceRepairAccountList(customerServiceRepairAccount);
    }

    /**
     * 新增补账
     *
     * @param customerServiceRepairAccount 补账
     * @return 结果
     */
    @Override
    public int insertCustomerServiceRepairAccount(CustomerServiceRepairAccount customerServiceRepairAccount) {
        customerServiceRepairAccount.setCreateTime(DateUtils.getNowDate());
        return customerServiceRepairAccountMapper.insertCustomerServiceRepairAccount(customerServiceRepairAccount);
    }

    /**
     * 修改补账
     *
     * @param customerServiceRepairAccount 补账
     * @return 结果
     */
    @Override
    public int updateCustomerServiceRepairAccount(CustomerServiceRepairAccount customerServiceRepairAccount) {
        customerServiceRepairAccount.setUpdateTime(DateUtils.getNowDate());
        return customerServiceRepairAccountMapper.updateCustomerServiceRepairAccount(customerServiceRepairAccount);
    }

    /**
     * 批量删除补账
     *
     * @param ids 需要删除的补账主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceRepairAccountByIds(Long[] ids) {
        return customerServiceRepairAccountMapper.deleteCustomerServiceRepairAccountByIds(ids);
    }

    /**
     * 删除补账信息
     *
     * @param id 补账主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceRepairAccountById(Long id) {
        return customerServiceRepairAccountMapper.deleteCustomerServiceRepairAccountById(id);
    }

    @Override
    public IPage<RepairAccountDTO> repairAccountList(Long deptId, RepairAccountVO vo) {
        IPage<RepairAccountDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());

        UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!Objects.isNull(vo.getSource()) && vo.getSource() == 1) {
            if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
                return result;
            }
        }

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE, deptId);
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return result;
        }

        //处理，会计搜索
        //补账ID
        CommonIdsSearchVO accountingSearchVO = accountingSearchOfRepair(vo.getAccountingEmployee());
        log.info("accountingSearchVO {}", accountingSearchVO.toString());
        if (accountingSearchVO.getNeedSearch() && accountingSearchVO.getFail()) {
            return result;
        }

        //处理，交付状态
        //补账ID
        /*CommonIdsSearchVO deliverStatusSearchVO = deliverStatusSearch(vo.getDeliverStatus());
        if (deliverStatusSearchVO.getNeedSearch() && deliverStatusSearchVO.getFail()) {
            return result;
        }*/

        //合并
        //CommonIdsSearchVO commonIdsSearchVO = CustomerServiceInAccountServiceImpl.mergeCommonIdsSearchData(Lists.newArrayList(accountingSearchVO, deliverStatusSearchVO));
        CommonIdsSearchVO commonIdsSearchVO = accountingSearchVO;
        if (commonIdsSearchVO.getNeedSearch() && commonIdsSearchVO.getFail()) {
            return result;
        }

        if (!StringUtils.isEmpty(vo.getSubmitTimeEnd())) {
            vo.setSubmitTimeEnd(vo.getSubmitTimeEnd() + " 23:59:59");
        }

        //原始数据
        List<RepairAccountDTO> source = customerServiceRepairAccountMapper.selectRepairAccountList(result, vo, tagSearchVO, commonIdsSearchVO, userDept);

        //处理数据
        if (!ObjectUtils.isEmpty(source)) {
            //List<Long> customerServiceIds = source.stream().map(RepairAccountDTO::getCustomerServiceId).distinct().collect(Collectors.toList());

            //所有的部门IDs
            List<Long> advisorDeptIds = source.stream().filter(r -> r.getAdvisorDeptId() != null).map(RepairAccountDTO::getAdvisorDeptId).distinct().collect(Collectors.toList());
            //改成取用分派时选的会计
            //List<Long> accountingDeptIds = source.stream().filter(r -> r.getAccountingDeptId() != null).map(RepairAccountDTO::getAccountingDeptId).distinct().collect(Collectors.toList());
            List<Long> accountingDeptIds = source.stream().filter(r -> r.getAssignAccountingDeptId() != null).map(RepairAccountDTO::getAssignAccountingDeptId).distinct().collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(accountingDeptIds)) {
                advisorDeptIds.addAll(accountingDeptIds);
            }

            //获取所有部门信息
            List<SysDept> depts = remoteDeptService.getByDeptIds(advisorDeptIds).getDataThrowException();
            Map<Long, SysDept> deptsMap = depts.stream().collect(Collectors.toMap(SysDept::getDeptId, r -> r));
            //获取这些部门下的所有员工
            List<SysEmployee> deptEmployees = remoteEmployeeService.getBatchEmployeeByDeptIds(advisorDeptIds).getDataThrowException();
            Map<Long, List<SysEmployee>> deptEmployeesMap = deptEmployees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

            //获取这些补账的涉及到的所有账期的交付单
            //计算这些补账涉及到的账期
            /*List<Integer> allPeriods = getAllPeriods(source);
            List<CustomerServiceInAccount> allInAccounts = iCustomerServiceInAccountService.list(
                    new LambdaQueryWrapper<CustomerServiceInAccount>()
                            .eq(CustomerServiceInAccount::getIsDel, Boolean.FALSE)
                            .in(CustomerServiceInAccount::getCustomerServiceId, customerServiceIds)
                            .in(CustomerServiceInAccount::getPeriod, allPeriods)
            );
            Map<String, CustomerServiceInAccount> allInAccountsMap = allInAccounts.stream()
                    .collect(Collectors.toMap(r -> inAccountMapKey(r.getCustomerServiceId(), r.getPeriod()), r -> r));*/

            for (RepairAccountDTO row : source) {
                /*
                 * 当补账还是待分派时，交付状态=待交付
                 * 当补账是已分派，且所有关联的账期的入账交付单都是未入账未结账时，交付状态=待交付
                 * 当补账是已分派，且所有关联的账期的入账交付单都是已入账已结账时，交付状态=已完成
                 * 其他状态为交付中
                 */

                //交付状态：1-待交付、2-交付中、3-已完成、-1-未知
                //Integer deliverStatus = getDeliverStatus(row, allInAccountsMap);

                //row.setAccountingEmployeeNameFull(handleEmployeeNameFull(deptsMap.get(row.getAccountingDeptId()), deptEmployeesMap.get(row.getAccountingDeptId())));
                row.setAccountingEmployeeNameFull(handleEmployeeNameFull(deptsMap.get(row.getAssignAccountingDeptId()), deptEmployeesMap.get(row.getAssignAccountingDeptId())));
                row.setAdvisorEmployeeNameFull(handleEmployeeNameFull(deptsMap.get(row.getAdvisorDeptId()), deptEmployeesMap.get(row.getAdvisorDeptId())));
                /*row.setDeliverStatus(deliverStatus);
                row.setDeliverStatusStr(getDeliverStatusStr(deliverStatus));*/
                row.setDeliverStatus(row.getDeliverStatus());
                row.setDeliverStatusStr(row.getDeliverStatus() == null ? null : RepairAccountDeliverStatus.getByCode(row.getDeliverStatus()).getName());
                row.setStatusStr(RepairAccountStatus.getByCode(row.getStatus()).getName());
                row.setTaxTypeStr(TaxType.getByCode(row.getTaxType()).getDesc());
            }
        }

        //返回数据
        result.setRecords(source);

        return result;
    }


    @Override
    public FirstPeriodDTO getFirstPeriod(Long customerServiceId) {
        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);
        if (cCustomerService == null) {
            throw new ServiceException("客户服务不存在");
        }
//
//        Integer firstAccountPeriod = cCustomerService.getFirstAccountPeriod();
//        if (firstAccountPeriod == null) {
//            throw new ServiceException("客户服务的首个账期不存在");
//        }
        // 从账期找了
        CustomerServicePeriodMonth maxPeriodMonth = customerServicePeriodMonthMapper.selectOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .orderByAsc(CustomerServicePeriodMonth::getPeriod).last("limit 1"));
        if (Objects.isNull(maxPeriodMonth)) {
            throw new ServiceException("客户服务的首个账期不存在");
        }
        return FirstPeriodDTO.builder()
                .customerName(cCustomerService.getCustomerName())
                .customerServiceId(customerServiceId)
                .firstAccountPeriod(maxPeriodMonth.getPeriod())
                .firstAccountPeriodStr(new StringBuilder(String.valueOf(maxPeriodMonth.getPeriod())).insert(4, "-").toString())
                .build();
    }

    @Override
    public RepairAccountPeriodEndDTO getRepairAccountPeriodEnd(Long customerServiceId) {
        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);
        if (cCustomerService == null) {
            throw new ServiceException("客户服务不存在");
        }

//        Integer firstAccountPeriod = cCustomerService.getFirstAccountPeriod();
//        if (firstAccountPeriod == null) {
//            throw new ServiceException("客户服务的首个账期不存在");
//        }

        //获取客户服务补账的结束账期
//        Integer periodEnd = getPeriodEndOfCustomerService(customerServiceId, firstAccountPeriod);

        // 从账期找了
        CustomerServicePeriodMonth maxPeriodMonth = customerServicePeriodMonthMapper.selectOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .orderByAsc(CustomerServicePeriodMonth::getPeriod).last("limit 1"));
        if (Objects.isNull(maxPeriodMonth)) {
            throw new ServiceException("客户服务的首个账期不存在");
        }
        Integer firstPeriod = maxPeriodMonth.getPeriod();
        int year = firstPeriod / 100;
        int month = firstPeriod % 100;
        Integer periodEnd = month == 1 ? (year - 1) * 100 + 12 : year * 100 + month - 1;
        return RepairAccountPeriodEndDTO.builder()
                .customerName(cCustomerService.getCustomerName())
                .customerServiceId(customerServiceId)
                .periodEnd(periodEnd)
                .periodEndStr(new StringBuilder(String.valueOf(periodEnd)).insert(4, "-").toString())
                .build();
    }

    @Transactional
    @Override
    public Long addRepairAccountBase(Long deptId, AddRepairAccountBaseVO vo) {
        Long customerServiceId = vo.getCustomerServiceId();

        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);
        if (cCustomerService == null || cCustomerService.getIsDel()) {
            throw new ServiceException("客户服务不存在");
        }

        Integer periodStart = vo.getPeriodStart();
        Integer periodEnd = vo.getPeriodEnd();

        checkPeriodStartAndEnd(periodStart, periodEnd, cCustomerService);

        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        //Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        //SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

        CustomerServiceRepairAccount newEntry = new CustomerServiceRepairAccount();
        newEntry.setIsDel(Boolean.FALSE);

        newEntry.setCustomerServiceId(customerServiceId);
        newEntry.setCustomerName(cCustomerService.getCustomerName());
        newEntry.setCreditCode(cCustomerService.getCreditCode());
        newEntry.setServiceNumber(cCustomerService.getServiceNumber());
        newEntry.setTaxNumber(cCustomerService.getTaxNumber());
        newEntry.setTaxType(cCustomerService.getTaxType());

        newEntry.setAccountingDeptId(cCustomerService.getAccountingDeptId());
        newEntry.setAccountingDeptName(
                cCustomerService.getAccountingDeptId() == null ? null :
                        remoteDeptService.getDeptInfo(cCustomerService.getAccountingDeptId())
                                .getDataThrowException()
                                .getDeptName()
        );
        newEntry.setAccountingTopDeptId(cCustomerService.getAccountingTopDeptId());

        newEntry.setAdvisorDeptId(cCustomerService.getAdvisorDeptId());
        newEntry.setAdvisorDeptName(
                cCustomerService.getAdvisorDeptId() == null ? null :
                        remoteDeptService.getDeptInfo(cCustomerService.getAdvisorDeptId())
                                .getDataThrowException()
                                .getDeptName()
        );
        newEntry.setAdvisorTopDeptId(cCustomerService.getAdvisorTopDeptId());

        newEntry.setBusinessDeptId(cCustomerService.getBusinessDeptId());
        newEntry.setBusinessDeptName(
                cCustomerService.getBusinessDeptId() == null ? null :
                        remoteDeptService.getDeptInfo(cCustomerService.getBusinessDeptId())
                                .getDataThrowException()
                                .getDeptName()
        );
        newEntry.setBusinessTopDeptId(cCustomerService.getBusinessTopDeptId());

//        newEntry.setStatus(RepairAccountStatus.NEED_PERFECT.getCode());
        // 0907 后面的信息无需填写 直接变成待分派了
        newEntry.setStatus(RepairAccountStatus.NEED_GIVE.getCode());
        newEntry.setDeliverStatus(null);

        newEntry.setStartPeriod(periodStart);
        newEntry.setEndPeriod(periodEnd);

        newEntry.setRemark(vo.getRemark());

        newEntry.setVerificationEmployeeType(2);

        //存数据
        save(newEntry);

        //存附件
        iCustomerServiceRepairAccountFileService.saveFile(newEntry.getId(), vo.getFiles(), RepairAccountFileType.BASE, String.valueOf(newEntry.getId()));

        //新增 临时的材料交接单 数据
//        createHandoverBase(deptId, periodStart, periodEnd, newEntry.getId(), customerServiceId);

        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("开始账期", periodStart);
        map.put("结束账期", periodEnd);
        map.put("备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(newEntry.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("新建补账服务")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark("新建补账服务")
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }

        return newEntry.getId();
    }

    @Override
    @Transactional
    public Long addRepairAccountBaseByRestart(Long deptId, AddRepairAccountBaseVO vo) {
        Long customerServiceId = vo.getCustomerServiceId();

        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);
        if (cCustomerService == null || cCustomerService.getIsDel()) {
            throw new ServiceException("客户服务不存在");
        }

        Integer periodStart = vo.getPeriodStart();
        Integer periodEnd = vo.getPeriodEnd();

        //同时没有其他待分派的补账服务；报错提示：该客户还有补账服务还未处理，请勿重复提交。
        Integer needGiveCount = needGiveCount(cCustomerService.getId());
        if (needGiveCount > 0) {
            throw new ServiceException("该客户还有补账服务还未处理，请勿重复提交。");
        }

        Long userId = Objects.isNull(deptId) ? 1L : SecurityUtils.getUserId();
        List<SysEmployee> employees = Objects.isNull(deptId) ? Lists.newArrayList() : remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? (Objects.isNull(deptId) ? "系统" : SecurityUtils.getLoginUser().getSysUser().getNickName()) : employees.get(0).getEmployeeName();

        CustomerServiceRepairAccount newEntry = new CustomerServiceRepairAccount();
        newEntry.setIsDel(Boolean.FALSE);

        newEntry.setCustomerServiceId(customerServiceId);
        newEntry.setCustomerName(cCustomerService.getCustomerName());
        newEntry.setCreditCode(cCustomerService.getCreditCode());
        newEntry.setServiceNumber(cCustomerService.getServiceNumber());
        newEntry.setTaxNumber(cCustomerService.getTaxNumber());
        newEntry.setTaxType(cCustomerService.getTaxType());

        newEntry.setAccountingDeptId(cCustomerService.getAccountingDeptId());
        newEntry.setAccountingDeptName(
                cCustomerService.getAccountingDeptId() == null ? null :
                        remoteDeptService.getDeptInfo(cCustomerService.getAccountingDeptId())
                                .getDataThrowException()
                                .getDeptName()
        );
        newEntry.setAccountingTopDeptId(cCustomerService.getAccountingTopDeptId());

        newEntry.setAdvisorDeptId(cCustomerService.getAdvisorDeptId());
        newEntry.setAdvisorDeptName(
                cCustomerService.getAdvisorDeptId() == null ? null :
                        remoteDeptService.getDeptInfo(cCustomerService.getAdvisorDeptId())
                                .getDataThrowException()
                                .getDeptName()
        );
        newEntry.setAdvisorTopDeptId(cCustomerService.getAdvisorTopDeptId());

        newEntry.setBusinessDeptId(cCustomerService.getBusinessDeptId());
        newEntry.setBusinessDeptName(
                cCustomerService.getBusinessDeptId() == null ? null :
                        remoteDeptService.getDeptInfo(cCustomerService.getBusinessDeptId())
                                .getDataThrowException()
                                .getDeptName()
        );
        newEntry.setBusinessTopDeptId(cCustomerService.getBusinessTopDeptId());

        // 0907 后面的信息无需填写 直接变成待分派了
        newEntry.setStatus(RepairAccountStatus.NEED_GIVE.getCode());
        newEntry.setDeliverStatus(null);

        newEntry.setStartPeriod(periodStart);
        newEntry.setEndPeriod(periodEnd);

        newEntry.setRemark(vo.getRemark());

        newEntry.setVerificationEmployeeType(2);

        //存数据
        save(newEntry);

        //新增 临时的材料交接单 数据
//        createHandoverBase(deptId, periodStart, periodEnd, newEntry.getId(), customerServiceId);

        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("开始账期", periodStart);
        map.put("结束账期", periodEnd);
        map.put("备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(newEntry.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("新建补账服务")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark("新建补账服务")
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }

        return newEntry.getId();
    }

    @Override
    public RepairAccountBaseDTO getRepairAccountBase(Long id) {
        CustomerServiceRepairAccount customerServiceRepairAccount = getById(id);
        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
            throw new ServiceException("补账不存在");
        }

        return getRepairAccountBaseResult(customerServiceRepairAccount);
    }

    @Override
    @Transactional
    public void updateRepairAccountBase(Long deptId, UpdateRepairAccountBaseVO vo) {
        CustomerServiceRepairAccount customerServiceRepairAccount = getById(vo.getId());
        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
            throw new ServiceException("补账不存在");
        }
        if (!RepairAccountStatus.canUpdate(customerServiceRepairAccount.getStatus())) {
            throw new ServiceException("当前状态的补账不可编辑");
        }
        Long customerServiceId = customerServiceRepairAccount.getCustomerServiceId();
        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);
        if (cCustomerService == null || cCustomerService.getIsDel()) {
            throw new ServiceException("客户服务不存在");
        }

        Integer periodStart = vo.getPeriodStart();
        Integer periodEnd = vo.getPeriodEnd();

        checkPeriodStartAndEnd(periodStart, periodEnd, cCustomerService);

        CustomerServiceRepairAccount updateEntry = new CustomerServiceRepairAccount();
        updateEntry.setId(vo.getId());
        updateEntry.setStartPeriod(periodStart);
        updateEntry.setEndPeriod(periodEnd);
        updateEntry.setRemark(vo.getRemark());
        updateEntry.setUpdateTime(LocalDateTime.now());

        updateById(updateEntry);

        //先删除原来的文件
        iCustomerServiceRepairAccountFileService.deleteByRepairAccount(vo.getId(), Lists.newArrayList(RepairAccountFileType.BASE));
        //再存附件
        iCustomerServiceRepairAccountFileService.saveFile(vo.getId(), vo.getFiles(), RepairAccountFileType.BASE, String.valueOf(vo.getId()));

        //删除 材料交接单
        deleteHandoverBase(deptId, updateEntry.getId());
        //新增 临时的材料交接单 数据
        createHandoverBase(deptId, periodStart, periodEnd, updateEntry.getId(), customerServiceId);

        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("开始账期", periodStart);
        map.put("结束账期", periodEnd);
        map.put("备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(updateEntry.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("编辑补账服务")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperContent(operContent)
                            .setOperRemark("编辑补账服务-基础信息")
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public RepairAccountInstrumentDTO getRepairAccountInstrument(Long id) {
        CustomerServiceRepairAccount customerServiceRepairAccount = getById(id);
        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
            throw new ServiceException("补账不存在");
        }

        List<DocHandoverInstrumentDTO> docHandoverInstrumentDTOS = iCustomerServiceDocHandoverService.getDocHandoverInstrumentByAddFromType(2, id);

        return RepairAccountInstrumentDTO.builder()
                .id(id)
                .items(docHandoverInstrumentDTOS)
                .build();
    }

    @Override
    public CheckCanSubmitDTO checkCanSubmit(RepairAccountInstrumentDTO vo) {
        CustomerServiceRepairAccount customerServiceRepairAccount = getById(vo.getId());
        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
            throw new ServiceException("补账不存在");
        }

        CheckCanSubmitDTO totalResult = CheckCanSubmitDTO.builder()
                .totalResult(false)
                .build();

        List<Integer> periods = getPeriodsBetween(customerServiceRepairAccount.getStartPeriod(), customerServiceRepairAccount.getEndPeriod());

        if (vo.getItems().size() < periods.size()) {
            return totalResult;
        }

        List<CheckCanSubmitItemDTO> itemResult = Lists.newArrayList();

        if (!ObjectUtils.isEmpty(vo.getItems())) {
            for (DocHandoverInstrumentDTO item : vo.getItems()) {
                Boolean checkResult = iCustomerServiceDocHandoverService.checkCanSubmitV2(item);

                itemResult.add(
                        CheckCanSubmitItemDTO.builder()
                                .id(item.getId())
                                .result(checkResult)
                                .build()
                );
            }
        }

        totalResult.setItemResult(itemResult);
        totalResult.setTotalResult(!ObjectUtils.isEmpty(itemResult) && itemResult.stream().allMatch(CheckCanSubmitItemDTO::getResult));

        return totalResult;
    }

    @Transactional
    @Override
    public void saveDocHandoverInstrument(Long deptId, RepairAccountInstrumentDTO vo, Boolean isSubmit) {
        Long id = vo.getId();
        CustomerServiceRepairAccount customerServiceRepairAccount = getById(id);
        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
            throw new ServiceException("补账不存在");
        }
        if (!RepairAccountStatus.canUpdate(customerServiceRepairAccount.getStatus())) {
            throw new ServiceException("当前状态的补账不可编辑");
        }

        //校验提交的是不是这个补账的票据信息
        RepairAccountInstrumentDTO repairAccountInstrumentDTO = getRepairAccountInstrument(id);
        List<Long> ac = repairAccountInstrumentDTO.getItems().stream().map(DocHandoverInstrumentDTO::getId).sorted(Long::compareTo).collect(Collectors.toList());
        List<Long> upload = vo.getItems().stream().map(DocHandoverInstrumentDTO::getId).sorted(Long::compareTo).collect(Collectors.toList());
        if (!ac.equals(upload)) {
            throw new ServiceException("数据不一致");
        }

        if (!ObjectUtils.isEmpty(vo.getItems())) {
            for (DocHandoverInstrumentDTO item : vo.getItems()) {
                iCustomerServiceDocHandoverService.saveDocHandoverInstrument(deptId, item, isSubmit);
            }
        }

        CheckCanSubmitDTO checkCanSubmitFlag = checkCanSubmit(vo);
        Integer status;
        if (isSubmit) {
            //提交

            if (checkCanSubmitFlag.getTotalResult()) {
                //通过
                status = RepairAccountStatus.NEED_GIVE.getCode();
            } else {
                throw new ServiceException("不符合可提交条件，不可提交");
            }
        } else {
            //保存

            if (checkCanSubmitFlag.getTotalResult()) {
                //通过
                status = RepairAccountStatus.NEED_SUBMIT.getCode();
            } else {
                status = RepairAccountStatus.NEED_PERFECT.getCode();
            }
        }

        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        CustomerServiceRepairAccount updateEntry = new CustomerServiceRepairAccount();
        updateEntry.setId(id);
        updateEntry.setUpdateTime(LocalDateTime.now());
        updateEntry.setStatus(status);
        updateEntry.setSubmitEmployeeDeptId(deptId);
        updateEntry.setSubmitEmployeeDeptName(operateUserInfoDTO.getSysDept().getDeptName());
        updateEntry.setSubmitEmployeeId(operateUserInfoDTO.getEmployeeId());
        updateEntry.setSubmitEmployeeName(operateUserInfoDTO.getOperName());
        updateEntry.setSubmitTime(LocalDateTime.now());

        //提交：触发交付状态=待交付
        //分派：轮一遍入账交付单，待交付/已完成
        //入账交付单编辑保存：轮一遍入账交付单，待交付/交付中/已完成
        updateEntry.setDeliverStatus(RepairAccountDeliverStatus.NEED_DELIVER.getCode());

        updateById(updateEntry);

        Map<String, Object> map = Maps.newLinkedHashMap();
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(id)
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType(isSubmit ? "提交补账服务" : "更新补账服务")
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setOperContent(operContent)
                            .setOperRemark(isSubmit ? "提交补账服务" : "更新补账服务")
                            .setOperImages(null)
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public void submitDocHandoverInstrument(Long deptId, RepairAccountInstrumentDTO vo) {
        CheckCanSubmitDTO checkCanSubmitDTO = checkCanSubmit(vo);

        if (!checkCanSubmitDTO.getTotalResult()) {
            throw new ServiceException("不符合可提交条件，不可提交");
        }

        saveDocHandoverInstrument(deptId, vo, true);
    }

    @Override
    public List<RepairAccountTitleDTO> getDetailTitles(Long id) {
        CustomerServiceRepairAccount customerServiceRepairAccount = getById(id);
        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
            throw new ServiceException("补账不存在");
        }

        List<RepairAccountTitleDTO> result = Lists.newArrayList();
        result.add(
                RepairAccountTitleDTO.builder()
                        .id(id)
                        .titleFull("服务信息")
                        .type(1)
                        .build()
        );

        //获取材料交接单
        List<DocHandoverSimpleDTO> docHandoverSimpleDTOS = iCustomerServiceDocHandoverService.getDocHandoverTitleByAddFromType(ADD_FROM_TYPE, id);
        if (!ObjectUtils.isEmpty(docHandoverSimpleDTOS)) {
            result.addAll(
                    docHandoverSimpleDTOS.stream().map(row -> RepairAccountTitleDTO.builder()
                            .id(row.getId())
                            .batchNum(row.getBatchNum())
                            .title(row.getTitle())
                            .titleFull(row.getTitleFull())
                            .type(ADD_FROM_TYPE)
                            .build()).collect(Collectors.toList())
            );
        }

        return result;
    }

    @Override
    public RepairAccountFullDTO getRepairAccountFull(Long id) {
        CustomerServiceRepairAccount customerServiceRepairAccount = getById(id);
        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
            throw new ServiceException("补账不存在");
        }

        List<CustomerServiceRepairAccountFile> files = iCustomerServiceRepairAccountFileService.selectByRepairAccount(id, Lists.newArrayList(RepairAccountFileType.BASE));

        String periodBetween = new StringBuilder(customerServiceRepairAccount.getStartPeriod().toString()).insert(4, "-").toString()
                + " ~ "
                + new StringBuilder(customerServiceRepairAccount.getStartPeriod().toString()).insert(4, "-").toString();

        //SysDept accountingDept = customerServiceRepairAccount.getAccountingDeptId() == null ? null : remoteDeptService.getDeptInfo(customerServiceRepairAccount.getAccountingDeptId()).getDataThrowException();
        SysDept accountingDept = customerServiceRepairAccount.getAssignAccountingDeptId() == null ? null : remoteDeptService.getDeptInfo(customerServiceRepairAccount.getAssignAccountingDeptId()).getDataThrowException();
        //List<SysEmployee> accountingEmployees = customerServiceRepairAccount.getAccountingDeptId() == null ? null : remoteEmployeeService.getEmployeeListByDeptId(customerServiceRepairAccount.getAccountingDeptId()).getDataThrowException();
        List<SysEmployee> accountingEmployees = customerServiceRepairAccount.getAssignAccountingDeptId() == null ? null : remoteEmployeeService.getEmployeeListByDeptId(customerServiceRepairAccount.getAssignAccountingDeptId()).getDataThrowException();
        /*String accountingDeptName = "";
        String accountingEmployeeName = "";
        if (accountingDept != null) {
            accountingDeptName = accountingDept.getDeptName();
        }
        if (!ObjectUtils.isEmpty(accountingEmployees)) {
            accountingEmployeeName = "（" + accountingEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + "）";
        }*/

        String submitEmployeeDeptName = StringUtils.isEmpty(customerServiceRepairAccount.getSubmitEmployeeDeptName()) ? "" : customerServiceRepairAccount.getSubmitEmployeeDeptName();
        String submitEmployeeName = StringUtils.isEmpty(customerServiceRepairAccount.getSubmitEmployeeName()) ? "" : ("（" + customerServiceRepairAccount.getSubmitEmployeeName() + "）");

        List<RepairAccountFullPeriodDTO> periods = getRepairAccountFullPeriod(customerServiceRepairAccount);

        return RepairAccountFullDTO.builder()
                //.accountingEmployeeNameFull(accountingDeptName + accountingEmployeeName)
                .accountingEmployeeNameFull(handleEmployeeNameFull(accountingDept, accountingEmployees))
                .files(iCustomerServiceRepairAccountFileService.covToCommonFileVO(files))
                .periodBetween(periodBetween)
                .remark(customerServiceRepairAccount.getRemark())
                .serviceTypeStr("补账")
                .submitEmployeeNameFull(submitEmployeeDeptName + submitEmployeeName)
                .submitTime(customerServiceRepairAccount.getSubmitTime())

                .periods(periods)

                .build();
    }

    @Override
    public RepairAccountFullDTO getRepairAccountFullV2(Long id) {
        CustomerServiceRepairAccount customerServiceRepairAccount = getById(id);
        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
            throw new ServiceException("补账不存在");
        }

        List<CustomerServiceRepairAccountFile> files = iCustomerServiceRepairAccountFileService.selectByRepairAccount(id, Lists.newArrayList(RepairAccountFileType.BASE));

        String periodBetween = new StringBuilder(customerServiceRepairAccount.getStartPeriod().toString()).insert(4, "-").toString()
                + " ~ "
                + new StringBuilder(customerServiceRepairAccount.getStartPeriod().toString()).insert(4, "-").toString();

        SysDept accountingDept = customerServiceRepairAccount.getAssignAccountingDeptId() == null ? null : remoteDeptService.getDeptInfo(customerServiceRepairAccount.getAssignAccountingDeptId()).getDataThrowException();
        List<SysEmployee> accountingEmployees = customerServiceRepairAccount.getAssignAccountingDeptId() == null ? null : remoteEmployeeService.getEmployeeListByDeptId(customerServiceRepairAccount.getAssignAccountingDeptId()).getDataThrowException();

        String submitEmployeeDeptName = StringUtils.isEmpty(customerServiceRepairAccount.getSubmitEmployeeDeptName()) ? "" : customerServiceRepairAccount.getSubmitEmployeeDeptName();
        String submitEmployeeName = StringUtils.isEmpty(customerServiceRepairAccount.getSubmitEmployeeName()) ? "" : ("（" + customerServiceRepairAccount.getSubmitEmployeeName() + "）");

        List<RepairAccountFullPeriodDTO> periods = getRepairAccountFullPeriodV2(customerServiceRepairAccount);

        return RepairAccountFullDTO.builder()
                .accountingEmployeeNameFull(handleEmployeeNameFull(accountingDept, accountingEmployees))
                .files(iCustomerServiceRepairAccountFileService.covToCommonFileVO(files))
                .periodBetween(periodBetween)
                .remark(customerServiceRepairAccount.getRemark())
                .serviceTypeStr("补账")
                .submitEmployeeNameFull(submitEmployeeDeptName + submitEmployeeName)
                .submitTime(customerServiceRepairAccount.getSubmitTime())

                .periods(periods)

                .build();
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceRepairAccount> batchOperateSubmit(Long deptId, List<Long> ids) {
        List<CustomerServiceRepairAccount> success = Lists.newArrayList();
        List<CustomerServiceRepairAccount> fail = Lists.newArrayList();

        //校验要分派的补账
        List<CustomerServiceRepairAccount> total = list(
                new LambdaQueryWrapper<CustomerServiceRepairAccount>()
                        .in(CustomerServiceRepairAccount::getId, ids)
        );
        for (CustomerServiceRepairAccount row : total) {
            if (RepairAccountStatus.canOperateSubmit(row.getStatus())) {
                success.add(row);
            } else {
                fail.add(row);
            }
        }

        //获取操作人员信息
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        if (!ObjectUtils.isEmpty(success)) {
            LocalDateTime now = LocalDateTime.now();

            for (CustomerServiceRepairAccount row : success) {
                //材料交接单，变成提交状态
                toSubmitHandover(deptId, row.getId());

                //更新补账
                row.setUpdateTime(LocalDateTime.now());
                row.setStatus(RepairAccountStatus.NEED_GIVE.getCode());
                row.setSubmitEmployeeDeptId(deptId);
                row.setSubmitEmployeeDeptName(operateUserInfoDTO.getSysDept().getDeptName());
                row.setSubmitEmployeeId(operateUserInfoDTO.getEmployeeId());
                row.setSubmitEmployeeName(operateUserInfoDTO.getOperName());
                row.setSubmitTime(now);

                //提交：触发交付状态=待交付
                //分派：轮一遍入账交付单，待交付/已完成
                //入账交付单编辑保存：轮一遍入账交付单，待交付/交付中/已完成
                row.setDeliverStatus(RepairAccountDeliverStatus.NEED_DELIVER.getCode());
            }

            updateBatchById(success);
        }

        TCommonOperateDTO<CustomerServiceRepairAccount> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        //插入操作日志
        success.forEach(row -> {
            try {
                Map<String, Object> map = Maps.newLinkedHashMap();
                String operContent = JSONObject.toJSONString(map);

                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(row.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType("提交补账服务")
                                .setOperName(operateUserInfoDTO.getOperName())
                                .setOperContent(operContent)
                                .setOperRemark("提交补账服务")
                                .setOperImages(null)
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });

        return result;
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceRepairAccount> batchOperateDelete(Long deptId, List<Long> ids) {
        List<CustomerServiceRepairAccount> success = Lists.newArrayList();
        List<CustomerServiceRepairAccount> fail = Lists.newArrayList();

        //校验要分派的补账
        List<CustomerServiceRepairAccount> total = list(
                new LambdaQueryWrapper<CustomerServiceRepairAccount>()
                        .in(CustomerServiceRepairAccount::getId, ids)
        );
        for (CustomerServiceRepairAccount row : total) {
            if (RepairAccountStatus.canOperateDelete(row.getStatus())) {
                success.add(row);
            } else {
                fail.add(row);
            }
        }

        //获取操作人员信息
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        if (!ObjectUtils.isEmpty(success)) {
            LocalDateTime now = LocalDateTime.now();

            for (CustomerServiceRepairAccount row : success) {
                Long customerServiceId = row.getCustomerServiceId();
                Long id = row.getId();

                List<Integer> periods = getPeriodsBetween(row.getStartPeriod(), row.getEndPeriod());

                //删除账期
                deletePeriod(deptId, id);

                //删除入账交付单
                iCustomerServiceInAccountService.softDeleteCustomerServiceInAccountFromRepairAccount(customerServiceId, periods);

                //删除材料交接单
                toUnEffectHandover(deptId, id);

                //更新补账
                row.setUpdateTime(LocalDateTime.now());
                row.setIsDel(Boolean.TRUE);
            }

            updateBatchById(success);
        }

        TCommonOperateDTO<CustomerServiceRepairAccount> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        //插入操作日志
        success.forEach(row -> {
            try {
                Map<String, Object> map = Maps.newLinkedHashMap();
                String operContent = JSONObject.toJSONString(map);

                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(row.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType("删除补账服务")
                                .setOperName(operateUserInfoDTO.getOperName())
                                .setOperContent(operContent)
                                .setOperRemark("删除补账服务")
                                .setOperImages(null)
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });

        return result;
    }

    @Override
    public List<Long> getAccountingDeptIdFullPathForSingle(Long id) {
        CustomerServiceRepairAccount customerServiceRepairAccount = getById(id);
        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
            throw new ServiceException("补账不存在");
        }

        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceRepairAccount.getCustomerServiceId());
        if (cCustomerService == null || cCustomerService.getIsDel()) {
            throw new ServiceException("客户服务不存在");
        }

        R<SysDept> accountingDeptInfoResult = remoteDeptService.getDeptInfo(cCustomerService.getAccountingDeptId());
        List<Long> accountingDeptIdPath = Objects.isNull(accountingDeptInfoResult) || Objects.isNull(accountingDeptInfoResult.getData()) ? Lists.newArrayList() : Arrays.stream(accountingDeptInfoResult.getData().getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(accountingDeptIdPath)) {
            accountingDeptIdPath.remove(0);
            accountingDeptIdPath.add(cCustomerService.getAccountingDeptId());
        }

        return accountingDeptIdPath;
    }

    @Override
    public AccountingSelectTipsDTO getAccountingSelectTips(Long accountingDeptId) {
        SysDept sysDept = remoteDeptService.getDeptInfo(accountingDeptId).getDataThrowException();
        if (sysDept == null) {
            throw new ServiceException("会计小组不存在");
        }

        Long x = sysDept.getCapacity();

        List<CustomerServiceRepairAccount> customerServiceRepairAccounts = list(
                new LambdaQueryWrapper<CustomerServiceRepairAccount>()
                        .eq(CustomerServiceRepairAccount::getIsDel, Boolean.FALSE)
                        .eq(CustomerServiceRepairAccount::getStatus, RepairAccountStatus.DONE_GIVE.getCode())
                        .eq(CustomerServiceRepairAccount::getAssignAccountingDeptId, accountingDeptId)
        );
        Long y = (long) customerServiceRepairAccounts.size();

        return AccountingSelectTipsDTO.builder()
                .x(x)
                .y(y)
                .z(x == null ? null : (x - y))
                .build();
    }

    @Transactional
    @Override
    public TCommonOperateDTO<CustomerServiceRepairAccount> batchOperateAssign(Long deptId, BatchOperateAssignRepairAccountVO vo) {
        vo.setSource(1);
        //校验会计小组
        Long accountingDeptId = vo.getAccountingDeptId();
        SysDept sysDept = remoteDeptService.getDeptInfo(accountingDeptId).getDataThrowException();
        if (sysDept == null) {
            throw new ServiceException("会计小组不存在");
        }
        List<SysEmployee> accountingEmployees = remoteEmployeeService.getEmployeeListByDeptId(accountingDeptId).getDataThrowException();
        String accountingEmployeeName = "（" + accountingEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + "）";

        List<CustomerServiceRepairAccount> success = Lists.newArrayList();
        List<CustomerServiceRepairAccount> fail = Lists.newArrayList();

        //校验要分派的补账
        List<Long> ids = vo.getIds();
        List<CustomerServiceRepairAccount> total = list(
                new LambdaQueryWrapper<CustomerServiceRepairAccount>()
                        .in(CustomerServiceRepairAccount::getId, ids)
        );
        for (CustomerServiceRepairAccount row : total) {
            if (RepairAccountStatus.canOperateAss(row.getStatus())) {
                //只有 待分派 的可以分派
                //可提交的才能被分派，  待分派 状态的前提就是可提交，所以这里不用单独校验是否符合提交条件

                success.add(row);
            } else {
                fail.add(row);
            }
        }

        //获取操作人员信息
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        if (!ObjectUtils.isEmpty(success)) {
            //生成账期、生成材料交接单，材料交接单的承验人为会计小组，是否凭票入账看账期标签（补账服务标签）、生成入账交付单。

            LocalDateTime now = LocalDateTime.now();
            Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(success.stream().map(CustomerServiceRepairAccount::getCustomerServiceId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
            for (CustomerServiceRepairAccount row : success) {
                if (Objects.equals(row.getStatus(), RepairAccountStatus.NEED_GIVE.getCode())) {
                    //已分派不用生成、生效 账期、入账交付单、材料交接单、交付状态
                    //已分派只需要更新下面的补账的字段就行
                    //带分派，需要生成 账期、入账交付单、生效材料交接单、修改状态为已分派、交付状态设置为待交付
                    Long customerServiceId = row.getCustomerServiceId();
                    CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);

                    List<TagDTO> tagDTOS = tagMap.getOrDefault(customerServiceId, Lists.newArrayList());

                    //生成账期
                    //同时会生成入账交付单
                    iCustomerServicePeriodMonthService.saveMonthPeriodListFromOther(
                            cCustomerService, tagDTOS, row.getStartPeriod(), row.getEndPeriod(), ADD_FROM_TYPE, row.getId(),
                            accountingDeptId, deptId, userId, operateUserInfoDTO.getOperName()
                    );

                    //生成材料交接单：材料交接单的承验人为会计小组，是否凭票入账看账期标签（补账服务标签）
                    //内部逻辑会捞一把刚才生成的账期，设置到材料交接单上
                    toEffectHandover(deptId, row.getId(),
                            tagDTOS.stream().anyMatch(r -> Objects.equals(r.getId(), 6L)) ? YesNo.YES.getCode() : YesNo.NO.getCode());

                    row.setStatus(RepairAccountStatus.DONE_GIVE.getCode());

                    //提交：触发交付状态=待交付
                    //分派：轮一遍入账交付单，待交付/已完成
                    //入账交付单编辑保存：轮一遍入账交付单，待交付/交付中/已完成
                    //刚生成入账交付单，都是 未入账未补账，所以是待交付
                    row.setDeliverStatus(RepairAccountDeliverStatus.NEED_DELIVER.getCode());
                } else if (Objects.equals(row.getStatus(), RepairAccountStatus.DONE_GIVE.getCode())) {
                    //禅道526：已分派 需要更新 账期的会计
                    iCustomerServicePeriodMonthService.updateAccountingByOperateAssign(
                            ADD_FROM_TYPE, row.getId(),
                            accountingDeptId
                    );
                }

                //更新补账
                row.setUpdateTime(LocalDateTime.now());
                row.setAssignEmployeeDeptId(operateUserInfoDTO.getDeptId());
                row.setAssignEmployeeDeptName(operateUserInfoDTO.getSysDept().getDeptName());
                row.setAssignEmployeeId(operateUserInfoDTO.getEmployeeId());
                row.setAssignEmployeeName(operateUserInfoDTO.getOperName());
                row.setAssignRemark(null);
                row.setAssignTime(now);
                row.setAssignAccountingDeptId(accountingDeptId);
            }

            updateBatchById(success);
        }

        TCommonOperateDTO<CustomerServiceRepairAccount> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        if (vo.getSource() == 1) {
            //插入操作日志
            success.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("分派给：", sysDept.getDeptName() + accountingEmployeeName);
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("分派补账服务")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark("分派补账服务")
                                    .setOperImages(null)
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        } else {
            //插入操作日志
            success.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("抢单：", sysDept.getDeptName() + accountingEmployeeName);
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("抢单补账服务")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark("抢单补账服务")
                                    .setOperImages(null)
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        return result;
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceRepairAccount> batchOperateBack(Long deptId, BatchOperateBatchRepairAccountVO vo) {
        if (StringUtils.isEmpty(vo.getRemark()) && ObjectUtils.isEmpty(vo.getFiles())) {
            throw new ServiceException("备注附件二者必填其一");
        }

        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();

        List<CustomerServiceRepairAccount> success = Lists.newArrayList();
        List<CustomerServiceRepairAccount> fail = Lists.newArrayList();

        //校验要分派的补账
        List<Long> ids = vo.getIds();
        List<CustomerServiceRepairAccount> total = list(
                new LambdaQueryWrapper<CustomerServiceRepairAccount>()
                        .in(CustomerServiceRepairAccount::getId, ids)
        );
        for (CustomerServiceRepairAccount row : total) {
            Integer needGiveCount = needGiveCountExceptSelf(row.getCustomerServiceId(), row.getId());
            if (needGiveCount > 0) {
                fail.add(row);
            } else {
                if (RepairAccountStatus.canOperateBack(row.getStatus())) {
                    success.add(row);
                } else {
                    fail.add(row);
                }
            }
        }

        //获取操作人员信息
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        if (!ObjectUtils.isEmpty(success)) {
            //确定的时候需要删除或失效账期、账期关联的所有交付单、交接单。因为不知道是否会被修改，并且在退回阶段的操作可能也是不对的。

            LocalDateTime now = LocalDateTime.now();

            for (CustomerServiceRepairAccount row : success) {
                Long customerServiceId = row.getCustomerServiceId();
                Long id = row.getId();

                List<Integer> periods = getPeriodsBetween(row.getStartPeriod(), row.getEndPeriod());

                //删除账期
                deletePeriod(deptId, id);

                //删除入账交付单
                iCustomerServiceInAccountService.softDeleteCustomerServiceInAccountFromRepairAccount(customerServiceId, periods);

                //删除材料交接单
                toUnEffectHandover(deptId, id);

                //更新补账
                //待分派 -> 待重提
                //已分派 -> 待分派
                row.setUpdateTime(LocalDateTime.now());
                row.setStatus(Objects.equals(row.getStatus(), RepairAccountStatus.NEED_GIVE.getCode()) ? RepairAccountStatus.RE_NEED_SUBMIT.getCode() : RepairAccountStatus.NEED_GIVE.getCode());
                row.setBackEmployeeDeptId(operateUserInfoDTO.getDeptId());
                row.setBackEmployeeDeptName(operateUserInfoDTO.getSysDept().getDeptName());
                row.setBackEmployeeId(operateUserInfoDTO.getEmployeeId());
                row.setBackEmployeeName(operateUserInfoDTO.getOperName());
                row.setBackRemark(remark);
                row.setBackTime(now);

                //先删除文件
                iCustomerServiceRepairAccountFileService.deleteByRepairAccount(id, Lists.newArrayList(RepairAccountFileType.OPERATE_BACK));

                //再存文件
                iCustomerServiceRepairAccountFileService.saveFile(id, vo.getFiles(), RepairAccountFileType.OPERATE_BACK, String.valueOf(id));
            }

            updateBatchById(success);
        }

        TCommonOperateDTO<CustomerServiceRepairAccount> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        //插入操作日志
        success.forEach(row -> {
            try {
                Map<String, Object> map = Maps.newLinkedHashMap();
                String operContent = JSONObject.toJSONString(map);

                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(row.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType("退回补账服务")
                                .setOperName(operateUserInfoDTO.getOperName())
                                .setOperContent(operContent)
                                .setOperRemark("退回补账服务")
                                .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });

        return result;
    }

    //处理，会计搜索
    //补账的ID
    @Override
    public CommonIdsSearchVO accountingSearchOfRepair(String accountingEmployee) {
        boolean needSearch = true;//是否需要搜索
        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
        List<Long> ids = null;

        if (StringUtils.isEmpty(accountingEmployee)) {
            needSearch = false;
        } else {
            ids = customerServiceRepairAccountMapper.searchAccounting(accountingEmployee);

            if (ObjectUtils.isEmpty(ids)) {
                fail = true;
            }
        }

        return CommonIdsSearchVO.builder()
                .needSearch(needSearch)
                .fail(fail)
                .ids(ids)
                .build();
    }

    @Override
    @Transactional
    public void getRepairAccountOrder(Long deptId, CommonIdVO vo) {
        Long accountingDeptId = getAccountDeptIdByUserIdAndDeptId(SecurityUtils.getUserId(), deptId);
        batchOperateAssign(deptId, BatchOperateAssignRepairAccountVO.builder().source(2).ids(Collections.singletonList(vo.getId())).accountingDeptId(accountingDeptId).build());
    }

    private Long getAccountDeptIdByUserIdAndDeptId(Long userId, Long deptId) {
        UserDeptDTO userDept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();
        if (userDept.getIsAdmin()) {
            throw new ServiceException("超级管理员不允许抢单");
        }
        if (userDept.getDeptType() == 1) {
            throw new ServiceException("当前为业务公司，不允许抢单");
        }
        if (ObjectUtils.isEmpty(userDept.getDeptIds())) {
            throw new ServiceException("会计小组为空");
        }
        Long accountDeptId = null;
        Map<Long, SysDept> deptMap = remoteDeptService.getByDeptIds(userDept.getDeptIds()).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(userDept.getDeptIds()).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        for (Long userDeptId : userDept.getDeptIds()) {
            SysDept dept = deptMap.get(userDeptId);
            if (Objects.isNull(dept) || dept.getDeptType() != 2 || dept.getLevel() != 4) {
                continue;
            }
            List<SysEmployee> employeeList = employeeMap.getOrDefault(userDeptId, Lists.newArrayList());
            if (ObjectUtils.isEmpty(employeeList)) {
                continue;
            }
            if (employeeList.size() == 1) {
                return userDeptId;
            } else {
                if (Objects.isNull(accountDeptId)) {
                    accountDeptId = userDeptId;
                }
            }
        }
        if (Objects.isNull(accountDeptId)) {
            throw new ServiceException("当前没有可用的会计小组");
        }
        return accountDeptId;
    }

    /*
     * 当补账还是待分派时，交付状态=待交付
     * 当补账是已分派，且所有关联的账期的入账交付单都是未入账未结账时，交付状态=待交付
     * 当补账是已分派，且所有关联的账期的入账交付单都是已入账已结账时，交付状态=已完成
     * 其他状态为交付中
     */
    //付状态：1-待交付、2-交付中、3-已完成
    //补账的ID
    private CommonIdsSearchVO deliverStatusSearch(Integer deliverStatus) {
        boolean needSearch = true;//是否需要搜索
        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
        List<Long> ids = null;

        if (deliverStatus == null) {
            needSearch = false;
        } else {
            ids = customerServiceRepairAccountMapper.searchDeliverStatus(deliverStatus);

            if (ObjectUtils.isEmpty(ids)) {
                fail = true;
            }
        }

        return CommonIdsSearchVO.builder()
                .needSearch(needSearch)
                .fail(fail)
                .ids(ids)
                .build();
    }

    //处理会计、顾问文案
    public static String handleEmployeeNameFull(SysDept dept, List<SysEmployee> employees) {
        String deptName = "";
        String employeeName = "";

        if (dept != null) {
            deptName = dept.getDeptName();
        }
        if (!ObjectUtils.isEmpty(employees)) {
            employeeName = "（" + employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + "）";
        }

        return deptName + employeeName;
    }

    private List<Integer> getAllPeriods(List<RepairAccountDTO> source) {
        List<Integer> allPeriods = Lists.newArrayList();

        if (ObjectUtils.isEmpty(source)) {
            return allPeriods;
        }

        for (RepairAccountDTO row : source) {
            allPeriods.addAll(
                    getPeriodsBetween(row.getStartPeriod(), row.getEndPeriod())
            );
        }

        return allPeriods.stream().distinct().collect(Collectors.toList());
    }

    private static List<Integer> getPeriodsBetween(Integer startPeriod, Integer endPeriod) {
        List<Integer> periods = Lists.newArrayList();
        for (int i = startPeriod; i <= endPeriod; ) {
            periods.add(i);

            i = Integer.parseInt(
                    DateUtils.localDateToStr(
                            (DateUtils.strToLocalDate(i + "", DateUtils.YYYYMMDD))
                                    .plusMonths(1), DateUtils.YYYYMM
                    )
            );
        }
        return periods;
    }

    private static String inAccountMapKey(Long customerServiceId, Integer period) {
        return customerServiceId + "_" + period;
    }

    public static void main(String[] args) {
        System.out.println(getPeriodsBetween(202404, 202501));
        System.out.println(getPeriodsBetween(202404, 202409));
    }

    //获取客户服务补账的结束账期
    private Integer getPeriodEndOfCustomerService(Long customerServiceId, Integer firstAccountPeriod) {
        Integer nowMinPeriod = customerServicePeriodMonthMapper.selectMinPeriod(customerServiceId);
        Integer nowMStartPeriod = (nowMinPeriod == null || nowMinPeriod >= firstAccountPeriod) ? firstAccountPeriod : nowMinPeriod;
        LocalDate nowMStartPeriodLocalDate = DateUtils.strToLocalDate(String.valueOf(nowMStartPeriod), DateUtils.YYYYMMDD);

        return Integer.parseInt(DateUtils.localDateToStr(nowMStartPeriodLocalDate.plusMonths(-1L), DateUtils.YYYYMM));
    }

    private void deleteHandoverBase(Long deptId, Long id) {
        iCustomerServiceDocHandoverService.operateDeleteByAddFromType(deptId, ADD_FROM_TYPE, id);
    }

    private void createHandoverBase(Long deptId, Integer periodStart, Integer periodEnd, Long id, Long customerServiceId) {
        for (int indexPeriod = periodStart; indexPeriod <= periodEnd; ) {
            iCustomerServiceDocHandoverService.addDocHandoverBase(deptId, AddDocHandoverBaseVO.builder()
                    .addFromType(ADD_FROM_TYPE)
                    .addFromId(id)
                    .customerServiceId(customerServiceId)
                    .customerServicePeriodMonthId(null)
                    .files(null)
                    .isVoucherEntry(YesNo.NO.getCode())
                    .period(indexPeriod)
                    .remark(null)
                    .build()
            );

            indexPeriod = nextPeriod(indexPeriod, 1L);
        }
    }

    private void toEffectHandover(Long deptId, Long id, Integer isVoucherEntry) {
        //iCustomerServiceDocHandoverService.operateEffectByAddFromType(deptId, ADD_FROM_TYPE, id, isVoucherEntry);
        iCustomerServiceDocHandoverService.operateEffectByAddFromTypeV2(deptId, ADD_FROM_TYPE, id, isVoucherEntry);
    }

    private void toUnEffectHandover(Long deptId, Long id) {
        iCustomerServiceDocHandoverService.operateUnEffectByAddFromType(deptId, ADD_FROM_TYPE, id);
    }

    private void toSubmitHandover(Long deptId, Long id) {
        iCustomerServiceDocHandoverService.operateSubmitByAddFromType(deptId, ADD_FROM_TYPE, id);
    }

    private void deletePeriod(Long deptId, Long id) {
        iCustomerServicePeriodMonthService.operateDeleteByAddFromType(deptId, ADD_FROM_TYPE, id);
    }

    private void checkPeriodStartAndEnd(Integer periodStart, Integer periodEnd, CCustomerService cCustomerService) {
        if (periodStart > periodEnd) {
            throw new ServiceException("开始账期不可大于结束账期");
        }

        //计算得出的只能是这个结束账期
        Integer periodEndCalc = getPeriodEndOfCustomerService(cCustomerService.getId(), cCustomerService.getFirstAccountPeriod());
        if (!Objects.equals(periodEnd, periodEndCalc)) {
            throw new ServiceException("服务账期不可重复提交");
        }

        //同时没有其他待分派的补账服务；报错提示：该客户还有补账服务还未处理，请勿重复提交。
        Integer needGiveCount = needGiveCount(cCustomerService.getId());
        if (needGiveCount > 0) {
            throw new ServiceException("该客户还有补账服务还未处理，请勿重复提交。");
        }
    }

    private Integer needGiveCount(Long customerServiceId) {
        //同时没有其他待分派的补账服务；报错提示：该客户还有补账服务还未处理，请勿重复提交。
        return count(new LambdaQueryWrapper<CustomerServiceRepairAccount>()
                .eq(CustomerServiceRepairAccount::getIsDel, Boolean.FALSE)
                .eq(CustomerServiceRepairAccount::getCustomerServiceId, customerServiceId)
                .eq(CustomerServiceRepairAccount::getStatus, RepairAccountStatus.NEED_GIVE.getCode())
        );
    }

    private Integer needGiveCountExceptSelf(Long customerServiceId, Long id) {
        //同时没有其他待分派的补账服务（除去自己）；报错提示：该客户还有补账服务还未处理，请勿重复提交。
        return count(new LambdaQueryWrapper<CustomerServiceRepairAccount>()
                .eq(CustomerServiceRepairAccount::getIsDel, Boolean.FALSE)
                .eq(CustomerServiceRepairAccount::getCustomerServiceId, customerServiceId)
                .eq(CustomerServiceRepairAccount::getStatus, RepairAccountStatus.NEED_GIVE.getCode())
                .ne(!Objects.isNull(id), CustomerServiceRepairAccount::getId, id)
        );
    }

    private RepairAccountBaseDTO getRepairAccountBaseResult(CustomerServiceRepairAccount customerServiceRepairAccount) {
        if (customerServiceRepairAccount == null) {
            return null;
        }

        List<CustomerServiceRepairAccountFile> files = iCustomerServiceRepairAccountFileService.selectByRepairAccount(customerServiceRepairAccount.getId(), Lists.newArrayList(RepairAccountFileType.BASE));

        return RepairAccountBaseDTO.builder()
                .customerName(customerServiceRepairAccount.getCustomerName())
                .customerServiceId(customerServiceRepairAccount.getCustomerServiceId())
                .files(iCustomerServiceRepairAccountFileService.covToCommonFileVO(files))
                .id(customerServiceRepairAccount.getId())
                .periodEnd(customerServiceRepairAccount.getEndPeriod())
                .periodStart(customerServiceRepairAccount.getStartPeriod())
                .remark(customerServiceRepairAccount.getRemark())
                .build();
    }

    public static Integer nextPeriod(Integer period, Long monthsToAdd) {
        LocalDate periodLocalDate = DateUtils.strToLocalDate(String.valueOf(period), DateUtils.YYYYMMDD);

        return Integer.parseInt(DateUtils.localDateToStr(periodLocalDate.plusMonths(monthsToAdd), DateUtils.YYYYMM));
    }

    private static Integer getDeliverStatus(RepairAccountDTO row, Map<String, CustomerServiceInAccount> allInAccountsMap) {
        Integer deliverStatus = null;

        if (Objects.equals(row.getStatus(), RepairAccountStatus.NEED_GIVE.getCode())) {
            deliverStatus = 1;
        } else if (Objects.equals(row.getStatus(), RepairAccountStatus.DONE_GIVE.getCode())) {
            List<CustomerServiceInAccount> hisInAccounts = getPeriodsBetween(row.getStartPeriod(), row.getEndPeriod()).stream()
                    .map(r -> allInAccountsMap.get(inAccountMapKey(row.getCustomerServiceId(), r)))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (hisInAccounts.stream().allMatch(r -> Objects.equals(r.getStatus(), InAccountStatus.UN_IN.getCode()))) {
                deliverStatus = 1;
            } else if (hisInAccounts.stream().allMatch(r -> Objects.equals(r.getStatus(), InAccountStatus.DONE.getCode()))) {
                deliverStatus = 3;
            }
        } else {
            deliverStatus = 2;
        }

        return deliverStatus;
    }

    private String getDeliverStatusStr(Integer deliverStatus) {
        //交付状态：1-待交付、2-交付中、3-已完成、-1-未知

        String result;

        switch (deliverStatus) {
            case 1:
                result = "待交付";
                break;
            case 2:
                result = "交付中";
                break;
            case 3:
                result = "已完成";
                break;
            default:
                result = "未知";
                break;
        }

        return result;
    }

    private List<RepairAccountFullPeriodDTO> getRepairAccountFullPeriod(CustomerServiceRepairAccount row) {
        List<RepairAccountFullPeriodDTO> result = Lists.newArrayList();

        if (row == null || row.getStartPeriod() == null || row.getEndPeriod() == null) {
            return result;
        }

        //该补涨涉及到的账期
        List<Integer> allPeriods = getPeriodsBetween(row.getStartPeriod(), row.getEndPeriod());

        //所有入账单
        List<CustomerServiceInAccount> allInAccounts = iCustomerServiceInAccountService.list(
                new LambdaQueryWrapper<CustomerServiceInAccount>()
                        .eq(CustomerServiceInAccount::getIsDel, Boolean.FALSE)
                        .eq(CustomerServiceInAccount::getCustomerServiceId, row.getCustomerServiceId())
                        .in(CustomerServiceInAccount::getPeriod, allPeriods)
        );
        Map<Integer, CustomerServiceInAccount> allInAccountsMap = allInAccounts.stream()
                .collect(Collectors.toMap(CustomerServiceInAccount::getPeriod, r -> r));

        //入账单对应账期的所有材料
        Map<Long, List<CustomerServiceDocHandover>> docHandoverMap = Maps.newHashMap();
        List<Long> customerServicePeriodMonthIds = allInAccounts.stream().map(CustomerServiceInAccount::getCustomerServicePeriodMonthId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
            docHandoverMap = iCustomerServiceDocHandoverService.getMapByPeriodMonthIds(customerServicePeriodMonthIds);
        }

        for (Integer period : allPeriods) {
            CustomerServiceInAccount customerServiceInAccount = allInAccountsMap.get(period);

            Integer wholeLevel = CustomerServiceInAccountServiceImpl.handleWholeLevelV2(
                    customerServiceInAccount == null
                            ? null
                            : docHandoverMap.get(customerServiceInAccount.getCustomerServicePeriodMonthId())
            );

            result.add(
                    RepairAccountFullPeriodDTO.builder()
                            .endTime(customerServiceInAccount == null ? null : customerServiceInAccount.getEndTime())
                            .inAccountId(customerServiceInAccount == null ? null : customerServiceInAccount.getId())
                            .inAccountStatus(customerServiceInAccount == null ? null : customerServiceInAccount.getStatus())
                            .inAccountStatusStr(customerServiceInAccount == null ? null : InAccountStatus.getByCode(customerServiceInAccount.getStatus()).getName())
                            .inTime(customerServiceInAccount == null ? null : customerServiceInAccount.getInTime())
                            .period(period)
                            .periodId(customerServiceInAccount == null ? null : customerServiceInAccount.getCustomerServicePeriodMonthId())
                            .wholeLevel(wholeLevel)
                            .wholeLevelStr(WholeLevel.getByCode(wholeLevel).getName())
                            .build()
            );
        }

        return result;
    }

    private List<RepairAccountFullPeriodDTO> getRepairAccountFullPeriodV2(CustomerServiceRepairAccount row) {
        List<RepairAccountFullPeriodDTO> result = Lists.newArrayList();

        if (row == null || row.getStartPeriod() == null || row.getEndPeriod() == null) {
            return result;
        }

        //该补涨涉及到的账期
        List<Integer> allPeriods = getPeriodsBetween(row.getStartPeriod(), row.getEndPeriod());

        //所有入账单
        List<CustomerServiceInAccount> allInAccounts = iCustomerServiceInAccountService.list(
                new LambdaQueryWrapper<CustomerServiceInAccount>()
                        .eq(CustomerServiceInAccount::getIsDel, Boolean.FALSE)
                        .eq(CustomerServiceInAccount::getCustomerServiceId, row.getCustomerServiceId())
                        .in(CustomerServiceInAccount::getPeriod, allPeriods)
        );
        Map<Integer, CustomerServiceInAccount> allInAccountsMap = allInAccounts.stream()
                .collect(Collectors.toMap(CustomerServiceInAccount::getPeriod, r -> r));

        List<CustomerServiceDocHandover> customerServiceDocHandovers = iCustomerServiceDocHandoverService.getDocHandoverByAddFromType(ADD_FROM_TYPE, row.getId());
        Map<Integer, List<CustomerServiceDocHandover>> docHandoverMap = customerServiceDocHandovers.stream().collect(Collectors.groupingBy(CustomerServiceDocHandover::getPeriod));

        for (Integer period : allPeriods) {
            CustomerServiceInAccount customerServiceInAccount = allInAccountsMap.get(period);

            Integer wholeLevel = CustomerServiceInAccountServiceImpl.handleWholeLevelV2(docHandoverMap.get(period));

            result.add(
                    RepairAccountFullPeriodDTO.builder()
                            .endTime(customerServiceInAccount == null ? null : customerServiceInAccount.getEndTime())
                            .inAccountId(customerServiceInAccount == null ? null : customerServiceInAccount.getId())
                            .inAccountStatus(customerServiceInAccount == null ? null : customerServiceInAccount.getStatus())
                            .inAccountStatusStr(customerServiceInAccount == null ? null : InAccountStatus.getByCode(customerServiceInAccount.getStatus()).getName())
                            .inTime(customerServiceInAccount == null ? null : customerServiceInAccount.getInTime())
                            .period(period)
                            .periodId(customerServiceInAccount == null ? null : customerServiceInAccount.getCustomerServicePeriodMonthId())
                            .wholeLevel(wholeLevel)
                            .wholeLevelStr(WholeLevel.getByCode(wholeLevel).getName())
                            .build()
            );
        }

        return result;
    }


    /*public static void main(String[] args) {
        Integer nowMStartPeriod = 202407;

        LocalDate nowMStartPeriodLocalDate = DateUtils.strToLocalDate(String.valueOf(nowMStartPeriod), DateUtils.YYYYMMDD);

        System.out.println(Integer.parseInt(DateUtils.localDateToStr(nowMStartPeriodLocalDate.plusMonths(-1L), DateUtils.YYYYMM)));
    }*/
}
