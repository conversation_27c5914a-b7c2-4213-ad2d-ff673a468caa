package com.bxm.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.ServiceWaitItemType;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.TagTypeEnum;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.tag.CreateTagResultDTO;
import com.bxm.customer.domain.dto.tag.TagManagerDTO;
import com.bxm.customer.domain.dto.tag.TagV2DTO;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.domain.vo.tag.CreateCustomizeTagVO;
import com.bxm.customer.domain.vo.tag.CreateTagVO;
import com.bxm.customer.domain.vo.tag.EditTagBatchVO;
import com.bxm.customer.domain.vo.tag.EditTagSingleVO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.CTagMapper;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import com.bxm.customer.properties.SpecialTagProperties;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TagService {

    @Autowired
    private ICTagService cTagService;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    @Autowired
    private ICCustomerServiceWaitItemService customerServiceWaitItemService;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private CTagMapper cTagMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;

    @Autowired
    private RedisService redisService;

    public TagV2DTO createCustomizeTag(CreateCustomizeTagVO vo, Long deptId) {
        // 校验标签名是否在系统标签中已存在
        if (cTagService.count(new LambdaQueryWrapper<CTag>()
                .eq(CTag::getTagName, vo.getTagName()).eq(CTag::getTagType, TagTypeEnum.SYSTEM_TAG.getCode())
                .eq(CTag::getIsDel, false)) > 0) {
            log.info("标签名：{}，在系统标签中已存在", vo.getTagName());
            throw new ServiceException("该标签为系统标签，请在系统标签中选择");
        }
        // 校验标签名在集团标签且非自定义标签中是否已存在
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        Long topDeptId = sysDept.getParentId();
        if (cTagService.count(new LambdaQueryWrapper<CTag>()
                .eq(CTag::getTagName, vo.getTagName()).eq(CTag::getTagType, TagTypeEnum.GROUP_TAG.getCode())
                .eq(CTag::getIsDel, false).eq(CTag::getTopDeptId, topDeptId).eq(CTag::getIsCustomize, false)) > 0) {
            log.info("标签名：{}，集团id：{}, 在集团标签中已存在", vo.getTagName(), topDeptId);
            throw new ServiceException("该标签为集团标签，请在集团标签中选择");
        }
        CTag cTag;
        List<CTag> customizeTags = cTagService.list(new LambdaQueryWrapper<CTag>()
                .eq(CTag::getTagName, vo.getTagName()).eq(CTag::getTagType, TagTypeEnum.GROUP_TAG.getCode())
                .eq(CTag::getIsDel, false).eq(CTag::getTopDeptId, topDeptId));
        if (ObjectUtils.isEmpty(customizeTags)) {
            cTag = new CTag().setTagName(vo.getTagName())
                    .setTagType(TagTypeEnum.GROUP_TAG.getCode())
                    .setIsCustomize(true)
                    .setHasParam(false)
                    .setIsDel(false)
                    .setStatus(1)
                    .setTopDeptId(topDeptId);
            cTagService.save(cTag);
        } else {
            cTag = customizeTags.get(0);
        }
        return TagV2DTO.builder()
                .isCustomize(true)
                .tagName(cTag.getTagName())
                .isCustomize(true)
                .id(cTag.getId())
                .isSelected(true)
                .hasParam(false)
                .build();
    }

    @Transactional
    public void editTagSingle(EditTagSingleVO vo, Long deptId) {
        if (Objects.isNull(vo.getBusinessType()) || Objects.isNull(vo.getBusinessId())) {
            return;
        }
        TagBusinessType tagBusinessType = TagBusinessType.getByCode(vo.getBusinessType());
        if (Objects.isNull(tagBusinessType)) {
            throw new ServiceException("业务类型错误");
        }
        List<TagV2DTO> tags = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(vo.getSystemTags())) {
            tags.addAll(vo.getSystemTags());
        }
        if (!ObjectUtils.isEmpty(vo.getGroupTags())) {
            tags.addAll(vo.getGroupTags());
        }
        if (Objects.equals(tagBusinessType.getCode(), TagBusinessType.CUSTOMER_SERVICE.getCode())) {
            CCustomerService customerService = customerServiceMapper.selectById(vo.getBusinessId());
            String operContent = getTagOperContent(tagBusinessType, vo.getBusinessId(), tags, DateUtils.getNowPeriod());
            CustomerServicePeriodMonth monthPeriod = customerServicePeriodMonthService.selectByCustomerServiceIdAndPeriod(vo.getBusinessId(), DateUtils.getNowPeriod());
            List<CBusinessTagRelation> oldTags = businessTagRelationService.selectByBusinessIdAndBusinessType(vo.getBusinessId(), TagBusinessType.CUSTOMER_SERVICE.getCode());
            Boolean oldHasIncome = oldTags.stream().noneMatch(row -> Objects.equals(row.getTagId(), specialTagProperties.getSanling()) || Objects.equals(row.getTagId(), specialTagProperties.getLingshenbao()));
            businessTagRelationService.deleteByBusinessIdAndBusinessType(vo.getBusinessId(), tagBusinessType);
            List<TagV2DTO> finalTags = Lists.newArrayList();
            if (!ObjectUtils.isEmpty(tags)) {
                finalTags = businessTagRelationService.saveByBusinessIdAndBusinessTypeV2(vo.getBusinessId(), tagBusinessType, tags);
            }
            Boolean newHasIncome = tags.stream().noneMatch(row -> Objects.equals(row.getId(), specialTagProperties.getSanling()) || Objects.equals(row.getId(), specialTagProperties.getLingshenbao()));

            if (!Objects.isNull(monthPeriod)) {
                businessTagRelationService.deleteByBusinessIdAndBusinessType(monthPeriod.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
                if (!ObjectUtils.isEmpty(tags)) {
                    businessTagRelationService.saveByBusinessIdAndBusinessTypeV2(monthPeriod.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, tags);
                }
            }
            if (!Objects.equals(oldHasIncome, newHasIncome)) {
                String itemContent;
                if (oldHasIncome) {
                    itemContent = "有收入变无收入";
                } else {
                    itemContent = "无收入变有收入";
                }
                if (customerServiceWaitItemService.selectNotDoneCountByCustomerServiceIdAndItemType(vo.getBusinessId(), ServiceWaitItemType.WAIT_DISPATCH) == 0) {
                    // 会计重派记录置为失效
                    customerServiceWaitItemService.updateInValidByCustomerServiceIdsAndItemType(Collections.singletonList(vo.getBusinessId()),
                            ServiceWaitItemType.WAIT_RE_DISPATCH.getCode());
                    customerServiceWaitItemService.save(new CCustomerServiceWaitItem().setCustomerServiceId(vo.getBusinessId())
                            .setItemType(ServiceWaitItemType.WAIT_RE_DISPATCH.getCode())
                            .setItemContent(itemContent)
                            .setIsValid(Boolean.TRUE)
                            .setDoneStatus(0));
                }
            }
            customerServicePeriodMonthMapper.updateDeliverStatusByCustomerServiceId(vo.getBusinessId());
            if (!Objects.isNull(customerService.getAdvisorDeptId())) {
                asyncService.asyncSendYsbNoticeV2(customerService.getAdvisorDeptId(), customerService.getId(), finalTags);
            }
            if (!StringUtils.isEmpty(operContent)) {
                Long userId = SecurityUtils.getUserId();
                List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getBusinessId())
                            .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑服务标签")
                            .setOperContent(operContent)
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
        } else {
            businessTagRelationService.deleteByBusinessIdAndBusinessType(vo.getBusinessId(), tagBusinessType);
            if (!ObjectUtils.isEmpty(tags)) {
                businessTagRelationService.saveByBusinessIdAndBusinessTypeV2(vo.getBusinessId(), tagBusinessType, tags);
            }
            customerServicePeriodMonthMapper.updateDeliverStatusById(vo.getBusinessId());
        }
    }

    private String getTagOperContent(TagBusinessType tagBusinessType, Long businessId, List<TagV2DTO> tagsDTOS, Integer validPeriod) {
        StringBuilder operContent = new StringBuilder();
        List<CBusinessTagRelation> relations = businessTagRelationService.selectByBusinessIdAndBusinessType(businessId, tagBusinessType.getCode());
        List<Long> oldTagIds = ObjectUtils.isEmpty(relations) ? Collections.emptyList() : relations.stream().map(CBusinessTagRelation::getTagId).collect(Collectors.toList());
        List<Long> newTagIds = ObjectUtils.isEmpty(tagsDTOS) ? Collections.emptyList() : tagsDTOS.stream().map(TagV2DTO::getId).collect(Collectors.toList());
        if (!oldTagIds.equals(newTagIds)) {
            List<CTag> tags = cTagService.list(new LambdaQueryWrapper<CTag>().eq(CTag::getIsDel, Boolean.FALSE));
            Map<Long, CTag> tagMap = ObjectUtils.isEmpty(tags) ? Collections.emptyMap() : tags.stream().collect(Collectors.toMap(CTag::getId, Function.identity()));
            if (ObjectUtils.isEmpty(newTagIds)) {
                operContent.append(String.format("原标签（%s），清除标签", ObjectUtils.isEmpty(relations) ? "无" : relations.stream().map(tag -> {
                    CTag ctag = tagMap.get(tag.getTagId());
                    if (Objects.isNull(ctag)) {
                        return "";
                    }
                    if (ctag.getHasParam()) {
                        return String.format(ctag.getTagName(), tag.getTagParamValue());
                    } else {
                        return ctag.getTagName();
                    }
                }).collect(Collectors.joining("、"))));
            } else {
                operContent.append(String.format("标签（%s）修改为：%s；", ObjectUtils.isEmpty(relations) ? "无" : relations.stream().map(tag -> {
                            CTag ctag = tagMap.get(tag.getTagId());
                            if (Objects.isNull(ctag)) {
                                return "";
                            }
                            if (ctag.getHasParam()) {
                                return String.format(ctag.getTagName(), tag.getTagParamValue());
                            } else {
                                return ctag.getTagName();
                            }
                        }).collect(Collectors.joining("、")),
                        tagsDTOS.stream().map(tag -> {
                            if (Objects.isNull(tag.getId())) {
                                return tag.getTagName();
                            } else {
                                CTag ctag = tagMap.get(tag.getId());
                                if (Objects.isNull(ctag)) {
                                    return "";
                                }
                                if (ctag.getHasParam()) {
                                    return String.format(ctag.getTagName(), tag.getParamValue());
                                } else {
                                    return ctag.getTagName();
                                }
                            }
                        }).collect(Collectors.joining("、"))));
            }
        }
        if (!Objects.isNull(validPeriod)) {
            operContent.append("生效账期：").append(DateUtils.periodToYeaMonth(validPeriod));
        }
        return operContent.toString();
    }

    public void editTagBatch(EditTagBatchVO vo, Long deptId) {
        if (Objects.isNull(vo.getBusinessType()) || ObjectUtils.isEmpty(vo.getBusinessIds())) {
            return;
        }
        TagBusinessType tagBusinessType = TagBusinessType.getByCode(vo.getBusinessType());
        if (Objects.isNull(tagBusinessType)) {
            throw new ServiceException("业务类型错误");
        }
        List<TagV2DTO> tags = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(vo.getSystemTags())) {
            tags.addAll(vo.getSystemTags());
        }
        if (!ObjectUtils.isEmpty(vo.getGroupTags())) {
            tags.addAll(vo.getGroupTags());
        }
        if (Objects.equals(tagBusinessType.getCode(), TagBusinessType.CUSTOMER_SERVICE.getCode())) {
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("编辑方式", vo.getEditType() == 1 ? "追加" : vo.getEditType() == 2 ? "清除" : "覆盖");
            if (!ObjectUtils.isEmpty(vo.getSystemTags())) {
                operContent.put("系统标签", vo.getSystemTags().stream().map(TagV2DTO::getFullTagName).collect(Collectors.joining("，")));
            }
            if (!ObjectUtils.isEmpty(vo.getGroupTags())) {
                operContent.put("集团标签", vo.getGroupTags().stream().map(TagV2DTO::getFullTagName).collect(Collectors.joining("，")));
            }
            List<CCustomerService> customerServices = customerServiceMapper.selectBatchIds(vo.getBusinessIds());
            Map<Long, CustomerServicePeriodMonth> periodMonthMap = customerServicePeriodMonthService.selectByCustomerServiceIdsAndPeriod(vo.getBusinessIds(), DateUtils.getNowPeriod()).stream().collect(Collectors.toMap(CustomerServicePeriodMonth::getCustomerServiceId, Function.identity(), (old, cur) -> old));
            List<CBusinessTagRelation> oldTags = businessTagRelationService.selectByBusinessIdsAndBusinessType(vo.getBusinessIds(), TagBusinessType.CUSTOMER_SERVICE.getCode());
            Map<Long, List<CBusinessTagRelation>> oldTagMap = oldTags.stream().collect(Collectors.groupingBy(CBusinessTagRelation::getBusinessId));
            if (vo.getEditType() == 1) {
                // 追加
                if (!ObjectUtils.isEmpty(tags)) {
                    businessTagRelationService.saveByBusinessIdsAndBusinessTypeV2(vo.getBusinessIds(), tagBusinessType, tags);
                }
            } else if (vo.getEditType() == 2) {
                // 清除
                if (!ObjectUtils.isEmpty(tags)) {
                    businessTagRelationService.deleteByBusinessIdsAndBusinessTypeAndTagIds(vo.getBusinessIds(), tagBusinessType, tags);
                }
            } else if (vo.getEditType() == 3) {
                // 覆盖
                businessTagRelationService.deleteByBusinessIdsAndBusinessTypeAndTagIds(vo.getBusinessIds(), tagBusinessType, Lists.newArrayList());
                if (!ObjectUtils.isEmpty(tags)) {
                    businessTagRelationService.saveByBusinessIdsAndBusinessTypeV2(vo.getBusinessIds(), tagBusinessType, tags);
                }
            }
            List<CBusinessTagRelation> newTags = businessTagRelationService.selectByBusinessIdsAndBusinessType(vo.getBusinessIds(), TagBusinessType.CUSTOMER_SERVICE.getCode());
            Map<Long, List<CBusinessTagRelation>> newTagMap = newTags.stream().collect(Collectors.groupingBy(CBusinessTagRelation::getBusinessId));
            Map<Long, CTag> tagMap = cTagService.list(null).stream().collect(Collectors.toMap(CTag::getId, Function.identity()));
            for (CCustomerService customerService : customerServices)   {
                Long businessId = customerService.getId();
                CustomerServicePeriodMonth customerServicePeriodMonth = periodMonthMap.get(businessId);
                List<CBusinessTagRelation> oldTagList = oldTagMap.getOrDefault(businessId, Collections.emptyList());
                List<CBusinessTagRelation> newTagList = newTagMap.getOrDefault(businessId, Collections.emptyList());
                Boolean oldHasIncome = oldTagList.stream().noneMatch(row -> Objects.equals(row.getTagId(), specialTagProperties.getSanling()) || Objects.equals(row.getTagId(), specialTagProperties.getLingshenbao()));
                Boolean newHasIncome = newTagList.stream().noneMatch(row -> Objects.equals(row.getId(), specialTagProperties.getSanling()) || Objects.equals(row.getId(), specialTagProperties.getLingshenbao()));
                List<TagV2DTO> newTagDTOList = newTagList.stream().map(relation -> {
                    CTag ctag = tagMap.get(relation.getTagId());
                    return TagV2DTO.builder()
                            .id(relation.getTagId())
                            .tagName(Objects.isNull(ctag) ? "" : ctag.getTagName())
                            .fullTagName(Objects.isNull(ctag) ? "" : (ctag.getHasParam() ? String.format(ctag.getTagName(), relation.getTagParamValue()) : ctag.getTagName()))
                            .build();
                }).collect(Collectors.toList());
                if (!Objects.isNull(customerServicePeriodMonth)) {
                    businessTagRelationService.deleteByBusinessIdAndBusinessType(customerServicePeriodMonth.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
                    if (!ObjectUtils.isEmpty(newTagList)) {
                        List<CBusinessTagRelation> periodTagRelations = newTagList.stream().map(relation -> {
                            CBusinessTagRelation periodTagRelation = new CBusinessTagRelation();
                            BeanUtils.copyProperties(relation, periodTagRelation);
                            periodTagRelation.setBusinessType(TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD.getCode());
                            periodTagRelation.setCreateTime(null);
                            periodTagRelation.setUpdateTime(null);
                            return periodTagRelation;
                        }).collect(Collectors.toList());
                        businessTagRelationService.saveBatch(periodTagRelations);
                    }
                }
                if (!Objects.equals(oldHasIncome, newHasIncome)) {
                    String itemContent;
                    if (oldHasIncome) {
                        itemContent = "有收入变无收入";
                    } else {
                        itemContent = "无收入变有收入";
                    }
                    if (customerServiceWaitItemService.selectNotDoneCountByCustomerServiceIdAndItemType(businessId, ServiceWaitItemType.WAIT_DISPATCH) == 0) {
                        // 会计重派记录置为失效
                        customerServiceWaitItemService.updateInValidByCustomerServiceIdsAndItemType(Collections.singletonList(businessId),
                                ServiceWaitItemType.WAIT_RE_DISPATCH.getCode());
                        customerServiceWaitItemService.save(new CCustomerServiceWaitItem().setCustomerServiceId(businessId)
                                .setItemType(ServiceWaitItemType.WAIT_RE_DISPATCH.getCode())
                                .setItemContent(itemContent)
                                .setIsValid(Boolean.TRUE)
                                .setDoneStatus(0));
                    }
                }
                if (!Objects.isNull(customerService.getAdvisorDeptId())) {
                    asyncService.asyncSendYsbNoticeV2(customerService.getAdvisorDeptId(), customerService.getId(), newTagDTOList);
                }
            }
            customerServicePeriodMonthMapper.updateDeliverStatusByCustomerServiceIds(vo.getBusinessIds());
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
            customerServices.forEach(customerService -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerService.getId())
                            .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑服务标签")
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setOperName(operName)
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        } else {
            if (vo.getEditType() == 1) {
                // 追加
                if (!ObjectUtils.isEmpty(tags)) {
                    businessTagRelationService.saveByBusinessIdsAndBusinessTypeV2(vo.getBusinessIds(), tagBusinessType, tags);
                }
            } else if (vo.getEditType() == 2) {
                // 清除
                if (!ObjectUtils.isEmpty(tags)) {
                    businessTagRelationService.deleteByBusinessIdsAndBusinessTypeAndTagIds(vo.getBusinessIds(), tagBusinessType, tags);
                }
            } else if (vo.getEditType() == 3) {
                // 覆盖
                businessTagRelationService.deleteByBusinessIdsAndBusinessTypeAndTagIds(vo.getBusinessIds(), tagBusinessType, Lists.newArrayList());
                if (!ObjectUtils.isEmpty(tags)) {
                    businessTagRelationService.saveByBusinessIdsAndBusinessTypeV2(vo.getBusinessIds(), tagBusinessType, tags);
                }
            }
            if (!ObjectUtils.isEmpty(vo.getBusinessIds())) {
                customerServicePeriodMonthMapper.updateDeliverStatusByIds(vo.getBusinessIds());
            }
        }
    }

    public IPage<TagManagerDTO> tagManagerList(String tagName, Integer isCustomize, Integer status, Integer pageNum, Integer pageSize, Long deptId) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        Long topDeptId = sysDept.getParentId();
        IPage<TagManagerDTO> result = new Page<>(pageNum, pageSize);
        List<TagManagerDTO> data = cTagMapper.selectTagManagerPageList(result, StringUtils.isEmpty(tagName) ? Lists.newArrayList() :
                Arrays.asList(tagName, "/"), isCustomize, status, topDeptId, sysDept.getDeptType());
        if (!ObjectUtils.isEmpty(data)) {
            fillData(data);
        }
        result.setRecords(data);
        return result;
    }

    private void fillData(List<TagManagerDTO> data) {
        if (!ObjectUtils.isEmpty(data)) {
            data.forEach(d -> {
                d.setIsCustomizeName(d.getIsCustomize() ? "是" : "否");
                d.setTagTypeName(TagTypeEnum.getNameByCode(d.getTagType()));
                d.setStatusName(d.getStatus() ? "启用" : "禁用");
            });
        }
    }

    public TCommonOperateDTO<CTag> deleteTag(CommonIdVO vo, Long deptId) {
        String operType = "删除标签";
        OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
        if (!Objects.isNull(vo.getId())) {
            CTag cTag = cTagService.getById(vo.getId());
            if (Objects.isNull(cTag) || cTag.getIsDel()) {
                throw new ServiceException("标签不存在");
            }
            cTagService.updateById(new CTag().setId(vo.getId()).setIsDel(true));
            // 删除标签关系
            businessTagRelationService.deleteByTagId(vo.getId());
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                        .setBusinessType(BusinessLogBusinessType.TAG.getCode())
                        .setDeptId(deptId)
                        .setOperType(operType)
                        .setOperName(operateUserInfo.getOperName())
                        .setOperUserId(operateUserInfo.getUserId()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            return null;
        } else {
            TCommonOperateDTO<CTag> result = new TCommonOperateDTO<>();
            List<CTag> total = cTagMapper.selectBatchIds(vo.getIds());
            if (ObjectUtils.isEmpty(total)) {
                return result;
            }
            result.setTotal(total);
            List<CTag> success = new ArrayList<>();
            List<CTag> fail = new ArrayList<>();
            for (CTag tag : total) {
                success.add(tag);
            }
            result.setSuccess(success);
            result.setFail(fail);
            if (!ObjectUtils.isEmpty(success)) {
                cTagService.updateBatchById(success.stream().map(tag -> new CTag().setId(tag.getId()).setIsDel(true)).collect(Collectors.toList()));
                // 删除标签关系
                businessTagRelationService.deleteByTagIds(vo.getIds());
                success.forEach(tag -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(tag.getId())
                                .setBusinessType(BusinessLogBusinessType.TAG.getCode())
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operateUserInfo.getOperName())
                                .setOperUserId(operateUserInfo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    public TCommonOperateDTO<CTag> changeTagStatus(CommonIdVO vo, Long deptId) {
        String operType = vo.getTargetStatus() == 1 ? "启用标签" : "禁用标签";
        OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
        if (!Objects.isNull(vo.getId())) {
            CTag cTag = cTagService.getById(vo.getId());
            if (Objects.isNull(cTag) || cTag.getIsDel()) {
                throw new ServiceException("标签不存在");
            }
            cTagService.updateById(new CTag().setId(vo.getId()).setStatus(vo.getTargetStatus()));
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                        .setBusinessType(BusinessLogBusinessType.TAG.getCode())
                        .setDeptId(deptId)
                        .setOperType(operType)
                        .setOperName(operateUserInfo.getOperName())
                        .setOperUserId(operateUserInfo.getUserId()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            return null;
        } else {
            TCommonOperateDTO<CTag> result = new TCommonOperateDTO<>();
            List<CTag> total = cTagMapper.selectBatchIds(vo.getIds());
            if (ObjectUtils.isEmpty(total)) {
                return result;
            }
            result.setTotal(total);
            List<CTag> success = new ArrayList<>();
            List<CTag> fail = new ArrayList<>();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            Integer originStatus = vo.getTargetStatus() == 1 ? 0 : 1;
            for (CTag tag : total) {
                if (!Objects.equals(tag.getStatus(), originStatus)) {
                    failIds.add(tag.getId());
                    errorMagMap.put(tag.getId(), "标签初始状态不符合");
                    fail.add(tag);
                } else {
                    success.add(tag);
                }
            }
            result.setSuccess(success);
            result.setFail(fail);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                List<TagManagerDTO> tagManagerDTOS = cTagMapper.selectTagManagerListByIds(failIds);
                fillData(tagManagerDTOS);
                tagManagerDTOS.forEach(tag -> tag.setErrorMsg(errorMagMap.get(tag.getId())));
                result.setErrorDataBatchNo(batchNo);
                redisService.setLargeCacheList(CacheConstants.TAG_OPERATE_ERROR_RECORD + batchNo, tagManagerDTOS, 500, 60 * 60, TimeUnit.SECONDS);
            }
            if (!ObjectUtils.isEmpty(success)) {
                cTagService.updateBatchById(success.stream().map(tag -> new CTag().setId(tag.getId()).setStatus(vo.getTargetStatus())).collect(Collectors.toList()));
                success.forEach(tag -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(tag.getId())
                                .setBusinessType(BusinessLogBusinessType.TAG.getCode())
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operateUserInfo.getOperName())
                                .setOperUserId(operateUserInfo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    public TCommonOperateDTO<CTag> convertTag(CommonIdVO vo, Long deptId) {
        String operType = "转换为非自定义";
        OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
        if (!Objects.isNull(vo.getId())) {
            CTag cTag = cTagService.getById(vo.getId());
            if (Objects.isNull(cTag) || cTag.getIsDel()) {
                throw new ServiceException("标签不存在");
            }
            if (!cTag.getIsCustomize()) {
                throw new ServiceException("自定义标签才可转换");
            }
            cTagService.updateById(new CTag().setId(vo.getId()).setIsCustomize(false));
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                        .setBusinessType(BusinessLogBusinessType.TAG.getCode())
                        .setDeptId(deptId)
                        .setOperType(operType)
                        .setOperName(operateUserInfo.getOperName())
                        .setOperUserId(operateUserInfo.getUserId()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            return null;
        } else {
            TCommonOperateDTO<CTag> result = new TCommonOperateDTO<>();
            List<CTag> total = cTagMapper.selectBatchIds(vo.getIds());
            if (ObjectUtils.isEmpty(total)) {
                return result;
            }
            result.setTotal(total);
            List<CTag> success = new ArrayList<>();
            List<CTag> fail = new ArrayList<>();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            for (CTag tag : total) {
                if (!tag.getIsCustomize()) {
                    failIds.add(tag.getId());
                    errorMagMap.put(tag.getId(), "自定义标签才可转换");
                    fail.add(tag);
                } else {
                    success.add(tag);
                }
            }
            result.setSuccess(success);
            result.setFail(fail);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                List<TagManagerDTO> tagManagerDTOS = cTagMapper.selectTagManagerListByIds(failIds);
                fillData(tagManagerDTOS);
                tagManagerDTOS.forEach(tag -> tag.setErrorMsg(errorMagMap.get(tag.getId())));
                result.setErrorDataBatchNo(batchNo);
                redisService.setLargeCacheList(CacheConstants.TAG_OPERATE_ERROR_RECORD + batchNo, tagManagerDTOS, 500, 60 * 60, TimeUnit.SECONDS);
            }
            if (!ObjectUtils.isEmpty(success)) {
                cTagService.updateBatchById(success.stream().map(tag -> new CTag().setId(tag.getId()).setStatus(vo.getTargetStatus())).collect(Collectors.toList()));
                success.forEach(tag -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(tag.getId())
                                .setBusinessType(BusinessLogBusinessType.TAG.getCode())
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operateUserInfo.getOperName())
                                .setOperUserId(operateUserInfo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    public CreateTagResultDTO createTag(CreateTagVO vo, Long deptId) {
        if (StringUtils.isEmpty(vo.getTagName())) {
            throw new ServiceException("标签名称不能为空");
        }
        List<String> tagNames = Arrays.asList(vo.getTagName().split("/"));
        if (tagNames.size() > 30) {
            throw new ServiceException("单次最多添加30个标签");
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        Long topDeptId = sysDept.getParentId();
        CreateTagResultDTO result = checkTagNameExist(tagNames, vo.getTagType(), topDeptId);
        if (Objects.equals(vo.getTagType(), TagTypeEnum.GROUP_TAG.getCode()) && (!ObjectUtils.isEmpty(result.getErrorSystemTagNames()) || !ObjectUtils.isEmpty(result.getErrorGroupTagNames()))) {
            return result;
        }
        cTagService.saveBatch(tagNames.stream().map(tagName -> new CTag()
                .setTagName(tagName)
                .setTagType(vo.getTagType())
                .setIsCustomize(false)
                .setHasParam(tagName.contains("%s"))
                .setIsDel(false)
                .setStatus(1)
                .setTopDeptId(Objects.equals(vo.getTagType(), TagTypeEnum.SYSTEM_TAG.getCode()) ? null : topDeptId)).collect(Collectors.toList()));
        return result;
    }

    private CreateTagResultDTO checkTagNameExist(List<String> tagNames, Integer tagType, Long topDeptId) {
        if (Objects.equals(tagType, TagTypeEnum.SYSTEM_TAG.getCode())) {
            // 系统标签全局不可重复
            List<CTag> existsTags = cTagMapper.selectList(new LambdaQueryWrapper<CTag>()
                    .eq(CTag::getIsDel, false)
                    .in(CTag::getTagName, tagNames));
            if (!ObjectUtils.isEmpty(existsTags)) {
                throw new ServiceException(String.format("以下标签已存在：%s", existsTags.stream().map(CTag::getTagName).collect(Collectors.joining("，"))));
            }
            return new CreateTagResultDTO();
        } else {
            // 集团标签
            // 1.不能与系统标签重复
            // 2.不能与当前集团已有的集团标签重复
            List<CTag> existsSystemTags = cTagMapper.selectList(new LambdaQueryWrapper<CTag>()
                    .eq(CTag::getIsDel, false).eq(CTag::getTagType, TagTypeEnum.SYSTEM_TAG.getCode())
                    .in(CTag::getTagName, tagNames));
            List<CTag> existsGroupTags = cTagMapper.selectList(new LambdaQueryWrapper<CTag>()
                    .eq(CTag::getIsDel, false).eq(CTag::getTagType, TagTypeEnum.GROUP_TAG.getCode())
                    .eq(CTag::getTopDeptId, topDeptId)
                    .in(CTag::getTagName, tagNames));
            return CreateTagResultDTO.builder()
                    .errorSystemTagNames(ObjectUtils.isEmpty(existsSystemTags) ? Lists.newArrayList() : existsSystemTags.stream().map(CTag::getTagName).collect(Collectors.toList()))
                    .errorGroupTagNames(ObjectUtils.isEmpty(existsGroupTags) ? Lists.newArrayList() : existsGroupTags.stream().map(CTag::getTagName).collect(Collectors.toList()))
                    .build();
        }
    }
}
