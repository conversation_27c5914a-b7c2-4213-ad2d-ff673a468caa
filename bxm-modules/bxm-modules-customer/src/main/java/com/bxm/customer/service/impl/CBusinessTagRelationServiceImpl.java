package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.TagTypeEnum;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.api.domain.dto.RemoteCustomerTagDTO;
import com.bxm.customer.domain.CBusinessTagRelation;
import com.bxm.customer.domain.CTag;
import com.bxm.customer.domain.dto.BusinessTagDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.tag.TagV2DTO;
import com.bxm.customer.mapper.CBusinessTagRelationMapper;
import com.bxm.customer.mapper.CTagMapper;
import com.bxm.customer.service.ICBusinessTagRelationService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.SysDept;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
public class CBusinessTagRelationServiceImpl extends ServiceImpl<CBusinessTagRelationMapper, CBusinessTagRelation> implements ICBusinessTagRelationService {
    @Autowired
    private CBusinessTagRelationMapper cBusinessTagRelationMapper;

    @Autowired
    private CTagMapper cTagMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    /**
     * 查询客户服务
     *
     * @param id 客户服务主键
     * @return 客户服务
     */
    @Override
    public CBusinessTagRelation selectCBusinessTagRelationById(Long id) {
        return cBusinessTagRelationMapper.selectCBusinessTagRelationById(id);
    }

    /**
     * 查询客户服务列表
     *
     * @param cBusinessTagRelation 客户服务
     * @return 客户服务
     */
    @Override
    public List<CBusinessTagRelation> selectCBusinessTagRelationList(CBusinessTagRelation cBusinessTagRelation) {
        return cBusinessTagRelationMapper.selectCBusinessTagRelationList(cBusinessTagRelation);
    }

    /**
     * 新增客户服务
     *
     * @param cBusinessTagRelation 客户服务
     * @return 结果
     */
    @Override
    public int insertCBusinessTagRelation(CBusinessTagRelation cBusinessTagRelation) {
        cBusinessTagRelation.setCreateTime(DateUtils.getNowDate());
        return cBusinessTagRelationMapper.insertCBusinessTagRelation(cBusinessTagRelation);
    }

    /**
     * 修改客户服务
     *
     * @param cBusinessTagRelation 客户服务
     * @return 结果
     */
    @Override
    public int updateCBusinessTagRelation(CBusinessTagRelation cBusinessTagRelation) {
        cBusinessTagRelation.setUpdateTime(DateUtils.getNowDate());
        return cBusinessTagRelationMapper.updateCBusinessTagRelation(cBusinessTagRelation);
    }

    /**
     * 批量删除客户服务
     *
     * @param ids 需要删除的客户服务主键
     * @return 结果
     */
    @Override
    public int deleteCBusinessTagRelationByIds(Long[] ids) {
        return cBusinessTagRelationMapper.deleteCBusinessTagRelationByIds(ids);
    }

    /**
     * 删除客户服务信息
     *
     * @param id 客户服务主键
     * @return 结果
     */
    @Override
    public int deleteCBusinessTagRelationById(Long id) {
        return cBusinessTagRelationMapper.deleteCBusinessTagRelationById(id);
    }

    @Override
    public List<Long> getCustomerIdsByTagNameLike(String tagName, TagBusinessType tagBusinessType, Long deptId) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        Long topDeptId = sysDept.getParentId();
        List<CTag> tags;
        if (tagName.contains("&")) {
            tags = cTagMapper.selectList(new LambdaQueryWrapper<CTag>().in(CTag::getTagName, Arrays.asList(tagName.split("&")))
                    .eq(CTag::getStatus, Boolean.TRUE)
                    .eq(CTag::getIsDel, Boolean.FALSE)
                    .and(queryWrapper -> queryWrapper.eq(CTag::getTagType, TagTypeEnum.SYSTEM_TAG.getCode())
                            .or(subWrapper -> subWrapper.eq(CTag::getTagType, TagTypeEnum.GROUP_TAG.getCode()).eq(CTag::getTopDeptId, topDeptId))));
        } else {
            tags = cTagMapper.selectList(new LambdaQueryWrapper<CTag>().like(CTag::getTagName, tagName)
                    .eq(CTag::getStatus, Boolean.TRUE)
                    .eq(CTag::getIsDel, Boolean.FALSE)
                    .and(queryWrapper -> queryWrapper.eq(CTag::getTagType, TagTypeEnum.SYSTEM_TAG.getCode())
                            .or(subWrapper -> subWrapper.eq(CTag::getTagType, TagTypeEnum.GROUP_TAG.getCode()).eq(CTag::getTopDeptId, topDeptId))));
        }
        if (ObjectUtils.isEmpty(tags)) {
            return Collections.emptyList();
        }
        List<CBusinessTagRelation> relations = baseMapper.selectList(new LambdaQueryWrapper<CBusinessTagRelation>()
                .in(CBusinessTagRelation::getTagId, tags.stream().map(CTag::getId).collect(Collectors.toList()))
                .eq(CBusinessTagRelation::getBusinessType, tagBusinessType.getCode()));
        if (ObjectUtils.isEmpty(relations)) {
            return Collections.emptyList();
        }
        if (tagName.contains("&")) {
            return relations.stream().collect(Collectors.groupingBy(CBusinessTagRelation::getBusinessId))
                    .entrySet().stream().filter(entry -> entry.getValue().size() == tagName.split("&").length)
                    .map(Map.Entry::getKey).collect(Collectors.toList());
        }
        return relations.stream().map(CBusinessTagRelation::getBusinessId).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<TagDTO>> getTagsByBusinessTypeForList(List<Long> businessIds, TagBusinessType tagBusinessType) {
        if (ObjectUtils.isEmpty(businessIds)) {
            return Collections.emptyMap();
        }
        List<CBusinessTagRelation> relations = baseMapper.selectList(new LambdaQueryWrapper<CBusinessTagRelation>()
                .in(CBusinessTagRelation::getBusinessId, businessIds)
                .eq(CBusinessTagRelation::getBusinessType, tagBusinessType.getCode()));
        if (ObjectUtils.isEmpty(relations)) {
            return Collections.emptyMap();
        }
        Map<Long, List<CBusinessTagRelation>> relationMap = relations.stream().collect(Collectors.groupingBy(CBusinessTagRelation::getBusinessId));
        List<CTag> tags = cTagMapper.selectList(new LambdaQueryWrapper<CTag>()
                .in(CTag::getId, relations.stream().map(CBusinessTagRelation::getTagId).distinct().collect(Collectors.toList()))
                .eq(CTag::getIsDel, Boolean.FALSE));
        Map<Long, CTag> tagMap = ObjectUtils.isEmpty(tags) ? Maps.newHashMap() :
                tags.stream().collect(Collectors.toMap(CTag::getId, row -> row));
        Map<Long, List<TagDTO>> result = Maps.newHashMap();
        for (Long businessId : businessIds) {
            List<CBusinessTagRelation> relationList = relationMap.get(businessId);
            if (ObjectUtils.isEmpty(relationList)) {
                result.put(businessId, Lists.newArrayList());
            } else {
                List<TagDTO> tagDTOList = Lists.newArrayList();
                for (CBusinessTagRelation relation : relationList) {
                    CTag tag = tagMap.get(relation.getTagId());
                    if (!Objects.isNull(tag)) {
                        tagDTOList.add(TagDTO.builder()
                                .id(tag.getId())
                                .isSelected(Boolean.TRUE)
                                .tagType(tag.getTagType() == 1 ? 1 : (tag.getIsCustomize() ? 3 : 2))
                                .tagName(tag.getHasParam() ? tag.getTagName().replace("%s", "") : tag.getTagName())
                                .fullTagName(tag.getHasParam() ? String.format(tag.getTagName(), relation.getTagParamValue()) : tag.getTagName())
                                .paramValue(relation.getTagParamValue())
                                .build());
                    }
                }
                if (!ObjectUtils.isEmpty(tagDTOList)) {
                    // tagDTOList先按tagType升序排序，再按id升序排序
                    tagDTOList.sort(Comparator.comparingInt(TagDTO::getTagType).thenComparingLong(TagDTO::getId));
                }
                result.put(businessId, tagDTOList);
            }
        }
        return result;
    }

    @Override
    public List<TagDTO> getTagsByBusinessTypeForDetail(Long businessId, TagBusinessType tagBusinessType, Long deptId) {
        if (Objects.isNull(businessId)) {
            return Collections.emptyList();
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        Long topDeptId = sysDept.getParentId();
        List<CTag> tags = cTagMapper.selectList(new LambdaQueryWrapper<CTag>()
                .eq(CTag::getTagType, TagTypeEnum.SYSTEM_TAG.getCode()).eq(CTag::getStatus, true).eq(CTag::getIsDel, Boolean.FALSE)
                .orderByAsc(CTag::getId));
        List<CBusinessTagRelation> relations = baseMapper.selectList(new LambdaQueryWrapper<CBusinessTagRelation>()
                .eq(CBusinessTagRelation::getBusinessId, businessId)
                .eq(CBusinessTagRelation::getBusinessType, tagBusinessType.getCode()));
        Map<Long, CBusinessTagRelation> relationMap = ObjectUtils.isEmpty(relations) ? Maps.newHashMap() :
                relations.stream().collect(Collectors.toMap(CBusinessTagRelation::getTagId, row -> row));
        if (ObjectUtils.isEmpty(tags) && ObjectUtils.isEmpty(relations)) {
            return Collections.emptyList();
        }
        List<TagDTO> result = Lists.newArrayList();
        for (CTag tag : tags) {
            // 系统标签
            CBusinessTagRelation relation = relationMap.get(tag.getId());
            result.add(TagDTO.builder()
                    .id(tag.getId())
                    .tagName(tag.getHasParam() ? tag.getTagName().replace("%s", "") : tag.getTagName())
                    .fullTagName(tag.getHasParam() && !Objects.isNull(relation) ? String.format(tag.getTagName(), relation.getTagParamValue()) : tag.getTagName())
                    .tagType(1)
                    .paramValue(Objects.isNull(relation) ? "" : relation.getTagParamValue())
                    .isSelected(!Objects.isNull(relation))
                    .build());
            relationMap.remove(tag.getId());
        }
        List<CTag> groupTags = cTagMapper.selectList(new LambdaQueryWrapper<CTag>()
                .eq(CTag::getTagType, TagTypeEnum.GROUP_TAG.getCode())
                .eq(CTag::getTopDeptId, topDeptId)
                .eq(CTag::getIsCustomize, false)
                .eq(CTag::getStatus, true).eq(CTag::getIsDel, Boolean.FALSE)
                .orderByAsc(CTag::getId));
        for (CTag tag : groupTags) {
            // 集团标签
            CBusinessTagRelation relation = relationMap.get(tag.getId());
            result.add(TagDTO.builder()
                    .id(tag.getId())
                    .tagName(tag.getHasParam() ? tag.getTagName().replace("%s", "") : tag.getTagName())
                    .fullTagName(tag.getHasParam() && !Objects.isNull(relation) ? String.format(tag.getTagName(), relation.getTagParamValue()) : tag.getTagName())
                    .tagType(2)
                    .paramValue(Objects.isNull(relation) ? "" : relation.getTagParamValue())
                    .isSelected(!Objects.isNull(relation))
                    .build());
            relationMap.remove(tag.getId());
        }
        if (!ObjectUtils.isEmpty(relationMap)) {
            // 自定义标签
            List<CTag> selfTags = cTagMapper.selectList(new LambdaQueryWrapper<CTag>()
                    .eq(CTag::getTagType, TagTypeEnum.GROUP_TAG.getCode())
                    .eq(CTag::getIsCustomize, true)
                    .eq(CTag::getTopDeptId, topDeptId)
                    .eq(CTag::getStatus, true)
                    .in(CTag::getId, relationMap.keySet())
                    .eq(CTag::getIsDel, Boolean.FALSE));
            for (CTag tag : selfTags) {
                CBusinessTagRelation relation = relationMap.get(tag.getId());
                result.add(TagDTO.builder()
                        .id(tag.getId())
                        .tagName(tag.getHasParam() ? tag.getTagName().replace("%s", "") : tag.getTagName())
                        .fullTagName(tag.getHasParam() && !Objects.isNull(relation) ? String.format(tag.getTagName(), relation.getTagParamValue()) : tag.getTagName())
                        .tagType(3)
                        .isSelected(Boolean.TRUE)
                        .paramValue(relation.getTagParamValue())
                        .build());
            }
        }
        return result;
    }

    @Override
    public void deleteByBusinessIdAndBusinessType(Long businessId, TagBusinessType tagBusinessType) {
        if (Objects.isNull(businessId)) {
            return;
        }
        remove(new LambdaQueryWrapper<CBusinessTagRelation>().eq(CBusinessTagRelation::getBusinessType, tagBusinessType.getCode())
                .eq(CBusinessTagRelation::getBusinessId, businessId));
    }

    @Override
    public void deleteBatchByBusinessIdAndBusinessType(List<Long> businessIds, TagBusinessType tagBusinessType) {
        if (ObjectUtils.isEmpty(businessIds)) {
            return;
        }
        remove(new LambdaQueryWrapper<CBusinessTagRelation>().eq(CBusinessTagRelation::getBusinessType, tagBusinessType.getCode())
                .in(CBusinessTagRelation::getBusinessId, businessIds));
    }

    @Override
    public List<TagDTO> saveByBusinessIdAndBusinessType(Long businessId, TagBusinessType tagBusinessType, List<TagDTO> tagDTOList) {
        if (Objects.isNull(businessId)) {
            return Lists.newArrayList();
        }
        if (ObjectUtils.isEmpty(tagDTOList)) {
            return Lists.newArrayList();
        }
        List<TagDTO> tags = Lists.newArrayList();
        tagDTOList.forEach(tagDTO -> {
            if (Objects.isNull(tagDTO.getId())) {
                CTag tag = cTagMapper.selectOne(new LambdaQueryWrapper<CTag>().eq(CTag::getTagName, tagDTO.getTagName())
                        .eq(CTag::getIsDel, Boolean.FALSE).last("limit 1"));
                if (Objects.isNull(tag)) {
                    tag = new CTag().setTagType(2).setTagName(tagDTO.getTagName()).setHasParam(Boolean.FALSE);
                    cTagMapper.insert(tag);
                }
                save(new CBusinessTagRelation().setTagId(tag.getId())
                        .setBusinessId(businessId).setBusinessType(tagBusinessType.getCode())
                        .setTagParamValue(tagDTO.getParamValue()));
                tags.add(TagDTO.builder().id(tag.getId()).fullTagName(tagDTO.getTagName()).build());
            } else {
                CTag tag = cTagMapper.selectById(tagDTO.getId());
                if (!Objects.isNull(tag)) {
                    tagDTO.setTagName(tag.getTagName());
                }
                save(new CBusinessTagRelation().setTagId(tagDTO.getId())
                        .setBusinessId(businessId).setBusinessType(tagBusinessType.getCode())
                        .setTagParamValue(tagDTO.getParamValue()));
                tags.add(TagDTO.builder().id(tagDTO.getId()).fullTagName(!StringUtils.isEmpty(tagDTO.getParamValue()) && !StringUtils.isEmpty(tagDTO.getTagName()) ? String.format(tagDTO.getTagName(), tagDTO.getParamValue()) : tagDTO.getTagName()).build());
            }
        });
        return tags;
    }

    @Override
    public List<TagV2DTO> saveByBusinessIdAndBusinessTypeV2(Long businessId, TagBusinessType tagBusinessType, List<TagV2DTO> tagDTOList) {
        if (Objects.isNull(businessId)) {
            return Lists.newArrayList();
        }
        if (ObjectUtils.isEmpty(tagDTOList)) {
            return Lists.newArrayList();
        }
        List<TagV2DTO> tags = Lists.newArrayList();
        tagDTOList.forEach(tagDTO -> {
            if (!Objects.isNull(tagDTO.getId())) {
                CTag tag = cTagMapper.selectById(tagDTO.getId());
                if (!Objects.isNull(tag)) {
                    tagDTO.setTagName(tag.getTagName());
                }
                save(new CBusinessTagRelation().setTagId(tagDTO.getId())
                        .setBusinessId(businessId).setBusinessType(tagBusinessType.getCode())
                        .setTagParamValue(tagDTO.getParamValue()));
                tags.add(TagV2DTO.builder().id(tagDTO.getId()).fullTagName(!StringUtils.isEmpty(tagDTO.getParamValue()) && !StringUtils.isEmpty(tagDTO.getTagName()) ? String.format(tagDTO.getTagName(), tagDTO.getParamValue()) : tagDTO.getTagName()).build());
            }
        });
        return tags;
    }

    @Override
    @Transactional
    public void saveByBusinessIdsAndBusinessTypeV2(List<Long> businessIds, TagBusinessType tagBusinessType, List<TagV2DTO> tagDTOList) {
        if (ObjectUtils.isEmpty(businessIds)) {
            return;
        }
        if (ObjectUtils.isEmpty(tagDTOList)) {
            return;
        }
        List<CBusinessTagRelation> existsRelations = list(new LambdaQueryWrapper<CBusinessTagRelation>()
                .eq(CBusinessTagRelation::getBusinessType, tagBusinessType)
                .in(CBusinessTagRelation::getBusinessId, businessIds));
        Map<Long, List<CBusinessTagRelation>> tagRelationMap = existsRelations.stream().collect(Collectors.groupingBy(CBusinessTagRelation::getBusinessId));
        List<CBusinessTagRelation> relations = Lists.newArrayList();
        businessIds.forEach(businessId -> {
            List<CBusinessTagRelation> existsRelationList = tagRelationMap.get(businessId);
            List<Long> exitsTagIds = ObjectUtils.isEmpty(existsRelationList) ? Lists.newArrayList() : existsRelationList.stream().map(CBusinessTagRelation::getTagId).collect(Collectors.toList());
            tagDTOList.forEach(tag -> {
                if (!exitsTagIds.contains(tag.getId())) {
                    CBusinessTagRelation relation = new CBusinessTagRelation()
                            .setBusinessId(businessId)
                            .setBusinessType(tagBusinessType.getCode())
                            .setTagId(tag.getId())
                            .setTagParamValue(tag.getParamValue());
                    relations.add(relation);
                }
            });
        });
        if (!ObjectUtils.isEmpty(relations)) {
            saveBatch(relations);
        }
    }

    @Override
    public void saveByBusinessIdAndBusinessTypeOnlySelected(Long businessId, TagBusinessType tagBusinessType, List<TagDTO> tagDTOList) {
        //只保存已选中的tag
        tagDTOList = tagDTOList.stream().filter(TagDTO::getIsSelected).collect(Collectors.toList());

        if (!ObjectUtils.isEmpty(tagDTOList)) {
            saveByBusinessIdAndBusinessType(businessId, tagBusinessType, tagDTOList);
        }
    }

    @Override
    public CBusinessTagRelation selectByTagIdAndBusinessTypeAndBusinessId(Long tagId, Integer tagBusinessType, Long businessId) {
        return getOne(new LambdaQueryWrapper<CBusinessTagRelation>()
                .eq(CBusinessTagRelation::getTagId, tagId).eq(CBusinessTagRelation::getBusinessType, tagBusinessType)
                .eq(CBusinessTagRelation::getBusinessId, businessId).last("limit 1"));
    }

    @Override
    public List<CBusinessTagRelation> selectSysTagsByBusinessTypeAndBusinessId(Integer businessType, Long businessId) {
        return list(new LambdaQueryWrapper<CBusinessTagRelation>()
                .eq(CBusinessTagRelation::getBusinessType, businessType)
                .eq(CBusinessTagRelation::getBusinessId, businessId)
                .inSql(CBusinessTagRelation::getTagId, "select id from c_tag where tag_type = 1"));
    }

    @Override
    public List<CBusinessTagRelation> selectByBusinessIdAndBusinessType(Long businessId, Integer businessType) {
        return list(new LambdaQueryWrapper<CBusinessTagRelation>()
                .eq(CBusinessTagRelation::getBusinessType, businessType)
                .eq(CBusinessTagRelation::getBusinessId, businessId)
                .inSql(CBusinessTagRelation::getTagId, "select id from c_tag where is_del = 0"));
    }

    @Override
    public List<CBusinessTagRelation> selectByBusinessIdsAndBusinessType(List<Long> businessIds, Integer businessType) {
        return list(new LambdaQueryWrapper<CBusinessTagRelation>()
                .eq(CBusinessTagRelation::getBusinessType, businessType)
                .in(CBusinessTagRelation::getBusinessId, businessIds)
                .inSql(CBusinessTagRelation::getTagId, "select id from c_tag where is_del = 0"));
    }

    @Override
    public Boolean existsTagId(Long businessId, Integer businessType, Long tagId) {
        return count(new LambdaQueryWrapper<CBusinessTagRelation>()
                .eq(CBusinessTagRelation::getBusinessId, businessId)
                .eq(CBusinessTagRelation::getBusinessType, businessType)
                .eq(CBusinessTagRelation::getTagId, tagId)) > 0;
    }

    @Override
    public List<BusinessTagDTO> selectTagsByBusinessTypeAndBusinessIds(List<Long> businessIds, TagBusinessType tagBusinessType) {
        if (ObjectUtils.isEmpty(businessIds) || Objects.isNull(tagBusinessType)) {
            return Collections.emptyList();
        }
        return cBusinessTagRelationMapper.selectTagsByBusinessTypeAndBusinessIds(businessIds, tagBusinessType.getCode());
    }

    @Override
    public void deleteByBusinessIdAndBusinessTypeAndTagIds(Long businessId, TagBusinessType tagBusinessType, List<Long> tagIds) {
        if (Objects.isNull(businessId) || ObjectUtils.isEmpty(tagIds)) {
            return;
        }
        remove(new LambdaQueryWrapper<CBusinessTagRelation>().eq(CBusinessTagRelation::getBusinessType, tagBusinessType.getCode())
                .eq(CBusinessTagRelation::getBusinessId, businessId)
                .in(CBusinessTagRelation::getTagId, tagIds));
    }

    @Override
    public void deleteBatchByBusinessIdAndBusinessTypeAndTagIds(List<Long> businessIds, TagBusinessType tagBusinessType, List<Long> tagIds) {
        if (ObjectUtils.isEmpty(businessIds) || ObjectUtils.isEmpty(tagIds)) {
            return;
        }
        remove(new LambdaQueryWrapper<CBusinessTagRelation>().eq(CBusinessTagRelation::getBusinessType, tagBusinessType.getCode())
                .in(CBusinessTagRelation::getBusinessId, businessIds)
                .in(CBusinessTagRelation::getTagId, tagIds));
    }

    @Override
    public void deleteByBusinessIdsAndBusinessTypeAndTagIds(List<Long> businessIds, TagBusinessType tagBusinessType, List<TagV2DTO> tags) {
        List<Long> removeTagIds = ObjectUtils.isEmpty(tags) ? Lists.newArrayList() : tags.stream().map(TagV2DTO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(businessIds) || Objects.isNull(tagBusinessType)) {
            return;
        }
        remove(new LambdaQueryWrapper<CBusinessTagRelation>()
                .eq(CBusinessTagRelation::getBusinessType, tagBusinessType.getCode())
                .in(CBusinessTagRelation::getBusinessId, businessIds)
                .in(!ObjectUtils.isEmpty(removeTagIds), CBusinessTagRelation::getTagId, removeTagIds));
    }

    @Override
    public void deleteByTagId(Long tagId) {
        if (Objects.isNull(tagId)) {
            return;
        }
        remove(new LambdaQueryWrapper<CBusinessTagRelation>().eq(CBusinessTagRelation::getTagId, tagId));
    }

    @Override
    public void deleteByTagIds(List<Long> tagIds) {
        if (ObjectUtils.isEmpty(tagIds)) {
            return;
        }
        remove(new LambdaQueryWrapper<CBusinessTagRelation>().in(CBusinessTagRelation::getTagId, tagIds));
    }
}
