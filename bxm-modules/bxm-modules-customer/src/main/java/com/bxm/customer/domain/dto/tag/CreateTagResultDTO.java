package com.bxm.customer.domain.dto.tag;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class CreateTagResultDTO {

    @ApiModelProperty("与系统标签冲突的标签名称列表")
    private List<String> errorSystemTagNames;

    @ApiModelProperty("与集团标签冲突的标签名称列表")
    private List<String> errorGroupTagNames;

    public CreateTagResultDTO() {
        this.errorSystemTagNames = Lists.newArrayList();
        this.errorGroupTagNames = Lists.newArrayList();
    }
}
