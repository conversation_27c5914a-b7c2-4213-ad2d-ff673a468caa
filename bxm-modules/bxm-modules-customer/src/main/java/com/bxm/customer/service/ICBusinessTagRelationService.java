package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.customer.domain.CBusinessTagRelation;
import com.bxm.customer.domain.dto.BusinessTagDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.tag.TagV2DTO;

import java.util.List;
import java.util.Map;

/**
 * 客户服务Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface ICBusinessTagRelationService extends IService<CBusinessTagRelation>
{
    /**
     * 查询客户服务
     * 
     * @param id 客户服务主键
     * @return 客户服务
     */
    public CBusinessTagRelation selectCBusinessTagRelationById(Long id);

    /**
     * 查询客户服务列表
     * 
     * @param cBusinessTagRelation 客户服务
     * @return 客户服务集合
     */
    public List<CBusinessTagRelation> selectCBusinessTagRelationList(CBusinessTagRelation cBusinessTagRelation);

    /**
     * 新增客户服务
     * 
     * @param cBusinessTagRelation 客户服务
     * @return 结果
     */
    public int insertCBusinessTagRelation(CBusinessTagRelation cBusinessTagRelation);

    /**
     * 修改客户服务
     * 
     * @param cBusinessTagRelation 客户服务
     * @return 结果
     */
    public int updateCBusinessTagRelation(CBusinessTagRelation cBusinessTagRelation);

    /**
     * 批量删除客户服务
     * 
     * @param ids 需要删除的客户服务主键集合
     * @return 结果
     */
    public int deleteCBusinessTagRelationByIds(Long[] ids);

    /**
     * 删除客户服务信息
     * 
     * @param id 客户服务主键
     * @return 结果
     */
    public int deleteCBusinessTagRelationById(Long id);

    /**
     * 根据标签名称模糊搜索绑定的业务id列表
     * @param tagName 标签名称
     * @param tagBusinessType 业务类型
     * @return
     */
    List<Long> getCustomerIdsByTagNameLike(String tagName, TagBusinessType tagBusinessType, Long deptId);

    Map<Long, List<TagDTO>> getTagsByBusinessTypeForList(List<Long> businessIds, TagBusinessType tagBusinessType);

    List<TagDTO> getTagsByBusinessTypeForDetail(Long businessId, TagBusinessType tagBusinessType, Long deptId);

    void deleteByBusinessIdAndBusinessType(Long businessId, TagBusinessType tagBusinessType);

    void deleteBatchByBusinessIdAndBusinessType(List<Long> businessIds, TagBusinessType tagBusinessType);

    List<TagDTO> saveByBusinessIdAndBusinessType(Long businessId, TagBusinessType tagBusinessType, List<TagDTO> tagDTOList);

    List<TagV2DTO> saveByBusinessIdAndBusinessTypeV2(Long businessId, TagBusinessType tagBusinessType, List<TagV2DTO> tagDTOList);

    void saveByBusinessIdsAndBusinessTypeV2(List<Long> businessIds, TagBusinessType tagBusinessType, List<TagV2DTO> tagDTOList);

    void saveByBusinessIdAndBusinessTypeOnlySelected(Long businessId, TagBusinessType tagBusinessType, List<TagDTO> tagDTOList);

    CBusinessTagRelation selectByTagIdAndBusinessTypeAndBusinessId(Long tagId, Integer tagBusinessType, Long businessId);

    List<CBusinessTagRelation> selectSysTagsByBusinessTypeAndBusinessId(Integer businessType, Long businessId);

    List<CBusinessTagRelation> selectByBusinessIdAndBusinessType(Long businessId, Integer businessType);

    List<CBusinessTagRelation> selectByBusinessIdsAndBusinessType(List<Long> businessIds, Integer businessType);

    Boolean existsTagId(Long businessId, Integer businessType, Long tagId);

    List<BusinessTagDTO> selectTagsByBusinessTypeAndBusinessIds(List<Long> businessIds, TagBusinessType tagBusinessType);

    void deleteByBusinessIdAndBusinessTypeAndTagIds(Long businessId, TagBusinessType tagBusinessType, List<Long> tagIds);

    void deleteBatchByBusinessIdAndBusinessTypeAndTagIds(List<Long> businessIds, TagBusinessType tagBusinessType, List<Long> tagIds);

    void deleteByBusinessIdsAndBusinessTypeAndTagIds(List<Long> businessIds, TagBusinessType tagBusinessType, List<TagV2DTO> tags);

    void deleteByTagId(Long tagId);

    void deleteByTagIds(List<Long> tagIds);
}
