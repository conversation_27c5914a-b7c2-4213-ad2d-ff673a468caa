package com.bxm.customer.domain.dto;

import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.CustomerServicePeriodMonthIncome;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/12 18:59
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceMonthPeriodDTO {
    @ApiModelProperty(value = "账期id")
    private Long id;

    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    @ApiModelProperty(value = "账期")
    private Integer period;

    @ApiModelProperty(value = "账期(格式)")
    private String periodStr;

    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中，确认后这里是针对客户服务状态值的筛选，不是对账期状态值的筛选。最新沟通：取账期的状态，且只有 1=服务中/正常,3=冻结中/冻结")
    private Integer serviceStatus;

    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中，确认后这里是针对客户服务状态值的筛选，不是对账期状态值的筛选。最新沟通：取账期的状态，且只有 1=服务中/正常,3=冻结中/冻结")
    private String serviceStatusStr;

    @ApiModelProperty(value = "全量开票金额")
    private BigDecimal allTicketAmount;

    @ApiModelProperty(value = "全量开票金额")
    private String allTicketAmountStr;

    @ApiModelProperty(value = "预认证")
    private CustomerServiceMonthPeriodItemDTO preAuth;

    @ApiModelProperty(value = "医保")
    private CustomerServiceMonthPeriodItemDTO medicalInsurance;

    @ApiModelProperty(value = "社保")
    private CustomerServiceMonthPeriodItemDTO socialInsurance;

    @ApiModelProperty(value = "个税（工资薪金）")
    private CustomerServiceMonthPeriodItemDTO tax;

    @ApiModelProperty(value = "国税")
    private CustomerServiceMonthPeriodItemDTO nationalTax;

    @ApiModelProperty(value = "个税（经营所得）")
    private CustomerServiceMonthPeriodItemDTO taxOperating;

    @ApiModelProperty("服务类型，1-代账，2-补账")
    private Integer serviceType;

    @ApiModelProperty("服务类型名称")
    private String serviceTypeStr;

    @ApiModelProperty("入账交付单id")
    private Long inAccountId;

    @ApiModelProperty("入账交付单状态")
    private String inAccountStatus;

    @ApiModelProperty("有无材料交接")
    private Boolean hasDocHandoverFile;

    @ApiModelProperty("材料交接状态")
    private String docHandoverFileStatus;

    //账期不存在的none数据
    public static CustomerServiceMonthPeriodDTO nonePeriod(Long customerServiceId, Integer period, CustomerServicePeriodMonthIncome income) {
        return CustomerServiceMonthPeriodDTO.builder()
                .allTicketAmount(income == null ? null : income.getAllTicketAmount())
                .allTicketAmountStr("-")
                .customerServiceId(customerServiceId)
                .id(null)
                .medicalInsurance(CustomerServiceMonthPeriodItemDTO.none())
                .nationalTax(CustomerServiceMonthPeriodItemDTO.none())
                .period(period)
                .preAuth(CustomerServiceMonthPeriodItemDTO.none())
                .socialInsurance(CustomerServiceMonthPeriodItemDTO.none())
                .tax(CustomerServiceMonthPeriodItemDTO.none())
                .taxOperating(CustomerServiceMonthPeriodItemDTO.none())
                .serviceType(null)
                .serviceTypeStr(null)
                .inAccountId(null)
                .inAccountStatus(null)
                .hasDocHandoverFile(false)
                .docHandoverFileStatus(null)
                .periodStr(DateUtils.periodToYeaMonth(period).replace("-", "/"))
                .build();
    }
}
