package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.BorrowOrderFileType;
import com.bxm.common.core.enums.BorrowOrderStatus;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.domain.CommonOperDTO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.BorrowAttachment;
import com.bxm.customer.domain.BorrowOrder;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.dto.borrow.BorrowOrderDTO;
import com.bxm.customer.domain.dto.borrow.BorrowOrderDetailDTO;
import com.bxm.customer.domain.dto.workBench.BorrowOrderWorkBenchDTO;
import com.bxm.customer.domain.vo.borrow.BorrowOrderSearchVO;
import com.bxm.customer.domain.vo.borrow.BorrowOrderVO;
import com.bxm.customer.mapper.BorrowOrderMapper;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 借阅单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-20
 */
@Service
@Slf4j
public class BorrowOrderServiceImpl extends ServiceImpl<BorrowOrderMapper, BorrowOrder> implements IBorrowOrderService {
    @Autowired
    private BorrowOrderMapper borrowOrderMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private IBorrowAttachmentService borrowAttachmentService;

    @Autowired
    private FileService fileService;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    /**
     * 查询借阅单
     *
     * @param id 借阅单主键
     * @return 借阅单
     */
    @Override
    public BorrowOrder selectBorrowOrderById(Long id) {
        return borrowOrderMapper.selectBorrowOrderById(id);
    }

    /**
     * 查询借阅单列表
     *
     * @param borrowOrder 借阅单
     * @return 借阅单
     */
    @Override
    public List<BorrowOrder> selectBorrowOrderList(BorrowOrder borrowOrder) {
        return borrowOrderMapper.selectBorrowOrderList(borrowOrder);
    }

    /**
     * 新增借阅单
     *
     * @param borrowOrder 借阅单
     * @return 结果
     */
    @Override
    public int insertBorrowOrder(BorrowOrder borrowOrder) {
        borrowOrder.setCreateTime(DateUtils.getNowDate());
        return borrowOrderMapper.insertBorrowOrder(borrowOrder);
    }

    /**
     * 修改借阅单
     *
     * @param borrowOrder 借阅单
     * @return 结果
     */
    @Override
    public int updateBorrowOrder(BorrowOrder borrowOrder) {
        borrowOrder.setUpdateTime(DateUtils.getNowDate());
        return borrowOrderMapper.updateBorrowOrder(borrowOrder);
    }

    /**
     * 批量删除借阅单
     *
     * @param ids 需要删除的借阅单主键
     * @return 结果
     */
    @Override
    public int deleteBorrowOrderByIds(Long[] ids) {
        return borrowOrderMapper.deleteBorrowOrderByIds(ids);
    }

    /**
     * 删除借阅单信息
     *
     * @param id 借阅单主键
     * @return 结果
     */
    @Override
    public int deleteBorrowOrderById(Long id) {
        return borrowOrderMapper.deleteBorrowOrderById(id);
    }

    @Override
    public IPage<BorrowOrderDTO> borrowOrderList(BorrowOrderSearchVO vo, Long deptId) {
        IPage<BorrowOrderDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return result;
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (!StringUtils.isEmpty(vo.getSubmitDateMin())) {
            vo.setSubmitDateMin(vo.getSubmitDateMin() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getSubmitDateMax())) {
            vo.setSubmitDateMax(vo.getSubmitDateMax() + " 23:59:59");
        }
        List<Long> customerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getTagName())) {
            if (Objects.isNull(vo.getTagIncludeFlag())) {
                vo.setTagIncludeFlag(1);
            }
            customerServiceIds = businessTagRelationService.getCustomerIdsByTagNameLike(vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE, deptId);
            if (ObjectUtils.isEmpty(customerServiceIds) && vo.getTagIncludeFlag() == 1) {
                return result;
            }
        }
        vo.setCustomerServiceIds(customerServiceIds);
        List<BorrowOrderDTO> data = borrowOrderMapper.borrowOrderList(result, vo, userDeptDTO.getDeptIds(), sysDept.getIsHeadquarters());
        if (!ObjectUtils.isEmpty(data)) {
            List<SysDept> deptList = remoteDeptService.getByDeptIds(data.stream().map(BorrowOrderDTO::getDeptId).distinct().collect(Collectors.toList())).getDataThrowException();
            Map<Long, SysDept> deptMap = deptList.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
            data.forEach(d -> {
                d.setDeptInfo(d.getDeptName() + "(" + d.getEmployeeName() + ")");
                d.setSubmitDate(d.getSubmitTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                d.setStatusStr(BorrowOrderStatus.getByCode(d.getStatus()).getName());
                SysDept dept = deptMap.get(d.getDeptId());
                if (!Objects.isNull(dept)) {
                    d.setTopDeptId(Long.parseLong(dept.getAncestors().split(",")[1]));
                    d.setDeptType(dept.getDeptType());
                }
            });
        }
        result.setRecords(data);
        return result;
    }

    @Override
    public BorrowOrderDetailDTO borrowOrderDetail(Long id) {
        BorrowOrder borrowOrder = getById(id);
        if (Objects.isNull(borrowOrder) || borrowOrder.getIsDel()) {
            throw new ServiceException("借阅单不存在");
        }
        CCustomerService customerService = customerServiceService.getById(borrowOrder.getCustomerServiceId());
        List<BorrowAttachment> borrowAttachments = borrowAttachmentService.selectByBorrowOrderIdAndFileType(id, BorrowOrderFileType.CREATE);
        return BorrowOrderDetailDTO.builder()
                .customerServiceId(borrowOrder.getCustomerServiceId())
                .customerName(Objects.isNull(customerService) || customerService.getIsDel() ? "" : customerService.getCustomerName())
                .id(id)
                .title(borrowOrder.getTitle())
                .status(borrowOrder.getStatus())
                .statusStr(BorrowOrderStatus.getByCode(borrowOrder.getStatus()).getName())
                .borrowAmount(borrowOrder.getBorrowAmount())
                .remark(borrowOrder.getRemark())
                .files(ObjectUtils.isEmpty(borrowAttachments) ? Lists.newArrayList() :
                        borrowAttachments.stream().map(attachment -> CommonFileVO.builder()
                                .fileName(attachment.getFileName())
                                .fileUrl(attachment.getFileUrl())
                                .fullFileUrl(fileService.getFullFileUrl(attachment.getFileUrl()))
                                .build())
                                .collect(Collectors.toList()))
                .build();
    }

    @Override
    @Transactional
    public void createBorrowOrder(BorrowOrderVO vo, Long deptId) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept)) {
            throw new ServiceException("组织不存在");
        }
        CCustomerService customerService = customerServiceService.getById(vo.getCustomerServiceId());
        if (Objects.isNull(customerService) || customerService.getIsDel()) {
            throw new ServiceException("客户服务不存在");
        }
        if (sysDept.getDeptType() == 1 && Objects.isNull(customerService.getAdvisorDeptId())) {
            throw new ServiceException("客户未分配顾问，无法创建");
        }
        if (sysDept.getDeptType() == 2 && Objects.isNull(customerService.getAccountingDeptId())) {
            throw new ServiceException("客户未分配会计，无法创建");
        }
        SysDept currentDept;
        if (sysDept.getDeptType() == 1) {
            currentDept = remoteDeptService.getDeptInfo(customerService.getAdvisorDeptId()).getDataThrowException();
        } else {
            currentDept = remoteDeptService.getDeptInfo(customerService.getAccountingDeptId()).getDataThrowException();
        }
        if (Objects.isNull(currentDept)) {
            throw new ServiceException("小组不存在");
        }
        vo.setReturnSetting(1);
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        BorrowOrder borrowOrder = new BorrowOrder().setTitle("借阅单_" + customerService.getServiceNumber() + "_" + (selectBorrowOrderCount() + 1))
                .setCustomerServiceId(customerService.getId())
                .setDeptId(sysDept.getDeptType() == 1 ? customerService.getAdvisorDeptId() : customerService.getAccountingDeptId())
                .setDeptName(currentDept.getDeptName())
                .setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId())
                .setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                .setBorrowAmount(vo.getBorrowAmount())
                .setReturnSetting(!Objects.isNull(vo.getReturnSetting()) && vo.getReturnSetting() == 1)
                .setRemark(vo.getRemark())
                .setStatus(BorrowOrderStatus.WAIT_OUT_STATION.getCode())
                .setSubmitTime(LocalDateTime.now());
        save(borrowOrder);
        borrowAttachmentService.removeAndSaveFile(borrowOrder.getId(), BorrowOrderFileType.CREATE, vo.getFiles());

        Map<String, String> map = new HashMap<>();
        map.put("归还设置", borrowOrder.getReturnSetting() ? "需要归还" : "无需归还");
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(borrowOrder.getId())
                    .setBusinessType(BusinessLogBusinessType.BORROW_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType("新建借阅")
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperContent(JSONObject.toJSONString(map))
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" :
                            JSONArray.toJSONString(vo.getFiles()))
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void modifyBorrowOrder(BorrowOrderVO vo, Long deptId) {
        BorrowOrder borrowOrder = getById(vo.getId());
        if (Objects.isNull(borrowOrder) || borrowOrder.getIsDel()) {
            throw new ServiceException("借阅单不存在");
        }
        if (!Objects.equals(BorrowOrderStatus.WAIT_RESUBMIT.getCode(), borrowOrder.getStatus())) {
            throw new ServiceException("只有待重提状态下可编辑");
        }
        BorrowOrder update = new BorrowOrder().setId(vo.getId())
                .setBorrowAmount(vo.getBorrowAmount())
                .setReturnSetting(!Objects.isNull(vo.getReturnSetting()) && vo.getReturnSetting() == 1)
                .setRemark(vo.getRemark())
                .setStatus(BorrowOrderStatus.WAIT_OUT_STATION.getCode())
                .setSubmitTime(LocalDateTime.now());
        updateById(update);
        borrowAttachmentService.removeAndSaveFile(borrowOrder.getId(), BorrowOrderFileType.CREATE, vo.getFiles());

        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        Map<String, String> map = new HashMap<>();
        map.put("归还设置", update.getReturnSetting() ? "需要归还" : "无需归还");
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(borrowOrder.getId())
                    .setBusinessType(BusinessLogBusinessType.BORROW_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType("编辑借阅并重新提交")
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperContent(JSONObject.toJSONString(map))
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" :
                            JSONArray.toJSONString(vo.getFiles()))
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public CommonOperateResultDTO submit(CommonIdVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return new CommonOperateResultDTO();
        }
        List<BorrowOrder> borrowOrders = selectByIds(vo.getIds());
        if (ObjectUtils.isEmpty(borrowOrders)) {
            return new CommonOperateResultDTO();
        }
        List<CommonOperDTO> totalList = borrowOrders.stream().map(borrowOrder ->
                CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> successList = borrowOrders.stream()
                .filter(borrowOrder -> Objects.equals(borrowOrder.getStatus(), BorrowOrderStatus.WAIT_RESUBMIT.getCode()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> failList = borrowOrders.stream()
                .filter(borrowOrder -> !Objects.equals(borrowOrder.getStatus(), BorrowOrderStatus.WAIT_RESUBMIT.getCode()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream()
                    .map(d ->
                            new BorrowOrder().setId(d.getId()).setStatus(BorrowOrderStatus.WAIT_OUT_STATION.getCode())
                                    .setSubmitTime(LocalDateTime.now())).collect(Collectors.toList()));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            successList.forEach(d -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(d.getId())
                            .setBusinessType(BusinessLogBusinessType.BORROW_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("重新提交")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return CommonOperateResultDTO.builder().totalList(totalList).successList(successList).failList(failList).build();
    }

    @Override
    @Transactional
    public CommonOperateResultDTO outStation(CommonIdVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return new CommonOperateResultDTO();
        }
        List<BorrowOrder> borrowOrders = selectByIds(vo.getIds());
        if (ObjectUtils.isEmpty(borrowOrders)) {
            return new CommonOperateResultDTO();
        }
        Map<Long, BorrowOrder> map = borrowOrders.stream().collect(Collectors.toMap(BorrowOrder::getId, Function.identity()));
        List<CommonOperDTO> totalList = borrowOrders.stream().map(borrowOrder ->
                CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> successList = borrowOrders.stream()
                .filter(borrowOrder -> Objects.equals(borrowOrder.getStatus(), BorrowOrderStatus.WAIT_OUT_STATION.getCode()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> failList = borrowOrders.stream()
                .filter(borrowOrder -> !Objects.equals(borrowOrder.getStatus(), BorrowOrderStatus.WAIT_OUT_STATION.getCode()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream()
                    .map(d ->
                            new BorrowOrder().setId(d.getId()).setStatus(map.get(d.getId()).getReturnSetting() ? BorrowOrderStatus.WAIT_RETURN.getCode() : BorrowOrderStatus.NO_RETURN.getCode())
                    ).collect(Collectors.toList()));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            successList.forEach(d -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(d.getId())
                            .setBusinessType(BusinessLogBusinessType.BORROW_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("借阅出站")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId)
                            .setOperRemark(vo.getReason())
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" :
                                    JSONArray.toJSONString(vo.getFiles())));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return CommonOperateResultDTO.builder().totalList(totalList).successList(successList).failList(failList).build();
    }

    @Override
    @Transactional
    public CommonOperateResultDTO reBack(CommonIdVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return new CommonOperateResultDTO();
        }
        List<BorrowOrder> borrowOrders = selectByIds(vo.getIds());
        if (ObjectUtils.isEmpty(borrowOrders)) {
            return new CommonOperateResultDTO();
        }
        List<Integer> canReBackStatus = BorrowOrderStatus.canReBackStatus();
        Map<Long, BorrowOrder> map = borrowOrders.stream().collect(Collectors.toMap(BorrowOrder::getId, Function.identity()));
        List<CommonOperDTO> totalList = borrowOrders.stream().map(borrowOrder ->
                CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> successList = borrowOrders.stream()
                .filter(borrowOrder -> canReBackStatus.contains(borrowOrder.getStatus()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> failList = borrowOrders.stream()
                .filter(borrowOrder -> !canReBackStatus.contains(borrowOrder.getStatus()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream()
                    .map(d ->
                            new BorrowOrder().setId(d.getId()).setStatus(BorrowOrderStatus.getByCode(map.get(d.getId()).getStatus()).getReBackStatus())
                    ).collect(Collectors.toList()));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            successList.forEach(d -> {
                BorrowOrder borrowOrder = map.get(d.getId());
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(d.getId())
                            .setBusinessType(BusinessLogBusinessType.BORROW_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType(Objects.equals(borrowOrder.getStatus(), BorrowOrderStatus.WAIT_CONFIRM.getCode()) ? "退回归还" : "退回借阅")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId)
                            .setOperRemark(vo.getReason())
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" :
                                    JSONArray.toJSONString(vo.getFiles())));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return CommonOperateResultDTO.builder().totalList(totalList).successList(successList).failList(failList).build();
    }

    @Override
    @Transactional
    public CommonOperateResultDTO returnBorrow(CommonIdVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return new CommonOperateResultDTO();
        }
        List<BorrowOrder> borrowOrders = selectByIds(vo.getIds());
        if (ObjectUtils.isEmpty(borrowOrders)) {
            return new CommonOperateResultDTO();
        }
        Map<Long, BorrowOrder> borrowMap = borrowOrders.stream().collect(Collectors.toMap(BorrowOrder::getId, Function.identity()));
        List<Integer> canReturnStatus = BorrowOrderStatus.canReturnStatus();
        List<CommonOperDTO> totalList = borrowOrders.stream().map(borrowOrder ->
                CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> successList = borrowOrders.stream()
                .filter(borrowOrder -> canReturnStatus.contains(borrowOrder.getStatus()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> failList = borrowOrders.stream()
                .filter(borrowOrder -> !canReturnStatus.contains(borrowOrder.getStatus()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream()
                    .map(d -> {
                        BorrowOrder borrowOrder = borrowMap.get(d.getId());
                        return new BorrowOrder().setId(d.getId()).setStatus(borrowOrder.getReturnSetting() ? BorrowOrderStatus.WAIT_CONFIRM.getCode() : BorrowOrderStatus.NO_RETURN.getCode());
                    }).collect(Collectors.toList()));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            successList.forEach(d -> {
//                Map<String, String> operContentMap = new HashMap<>();
//                operContentMap.put("归还设置", vo.getReturnSetting() == 1 ? "需要归还" : "无需归还");
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(d.getId())
                            .setBusinessType(BusinessLogBusinessType.BORROW_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("借阅归还")
//                            .setOperContent(JSONObject.toJSONString(operContentMap))
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId)
                            .setOperRemark(vo.getReason())
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" :
                                    JSONArray.toJSONString(vo.getFiles())));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return CommonOperateResultDTO.builder().totalList(totalList).successList(successList).failList(failList).build();
    }

    @Override
    @Transactional
    public CommonOperateResultDTO check(CommonIdVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return new CommonOperateResultDTO();
        }
        List<BorrowOrder> borrowOrders = selectByIds(vo.getIds());
        if (ObjectUtils.isEmpty(borrowOrders)) {
            return new CommonOperateResultDTO();
        }
        List<CommonOperDTO> totalList = borrowOrders.stream().map(borrowOrder ->
                CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> successList = borrowOrders.stream()
                .filter(borrowOrder -> Objects.equals(borrowOrder.getStatus(), BorrowOrderStatus.WAIT_CONFIRM.getCode()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> failList = borrowOrders.stream()
                .filter(borrowOrder -> !Objects.equals(borrowOrder.getStatus(), BorrowOrderStatus.WAIT_CONFIRM.getCode()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream()
                    .map(d ->
                            new BorrowOrder().setId(d.getId()).setStatus(BorrowOrderStatus.CONFIRMED.getCode())
                    ).collect(Collectors.toList()));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            successList.forEach(d -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(d.getId())
                            .setBusinessType(BusinessLogBusinessType.BORROW_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("归还验收")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId)
                            .setOperRemark(vo.getReason())
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" :
                                    JSONArray.toJSONString(vo.getFiles())));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return CommonOperateResultDTO.builder().totalList(totalList).successList(successList).failList(failList).build();
    }

    @Override
    @Transactional
    public CommonOperateResultDTO dispatch(CommonIdVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return new CommonOperateResultDTO();
        }
        List<BorrowOrder> borrowOrders = selectByIds(vo.getIds());
        if (ObjectUtils.isEmpty(borrowOrders)) {
            return new CommonOperateResultDTO();
        }
        Map<Long, BorrowOrder> map = borrowOrders.stream().collect(Collectors.toMap(BorrowOrder::getId, Function.identity()));
        List<Long> oldDeptIds = borrowOrders.stream().map(BorrowOrder::getDeptId).filter(id -> !Objects.isNull(id)).distinct().collect(Collectors.toList());
        List<Long> oldEmployeeIds = borrowOrders.stream().map(BorrowOrder::getEmployeeId).filter(id -> !Objects.isNull(id)).distinct().collect(Collectors.toList());
        List<SysDept> oldDeptList = ObjectUtils.isEmpty(oldDeptIds) ? Lists.newArrayList() : remoteDeptService.getByDeptIds(oldDeptIds).getDataThrowException();
        Map<Long, SysDept> oldDeptMap = oldDeptList.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        List<SysEmployee> oldEmployees = ObjectUtils.isEmpty(oldEmployeeIds) ? Lists.newArrayList() : remoteEmployeeService.getBatchEmployeeByIds(oldEmployeeIds).getDataThrowException();
        Map<Long, SysEmployee> oldEmployeeMap = oldEmployees.stream().collect(Collectors.toMap(SysEmployee::getEmployeeId, Function.identity()));
        SysDept newDept = remoteDeptService.getDeptInfo(vo.getTargetDeptId()).getDataThrowException();
        SysEmployee newEmployee = remoteEmployeeService.getEmployeeInfo(vo.getTargetEmployeeId()).getDataThrowException();
        List<CommonOperDTO> totalList = borrowOrders.stream().map(borrowOrder ->
                CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(totalList)) {
            String newDeptName = Objects.isNull(newDept) ? "" : newDept.getDeptName();
            String newEmployeeName = Objects.isNull(newEmployee) ? "" : newEmployee.getEmployeeName();
            updateBatchById(totalList.stream()
                    .map(d ->
                            new BorrowOrder().setId(d.getId()).setDeptId(vo.getTargetDeptId()).setDeptName(newDeptName)
                                    .setEmployeeId(vo.getTargetEmployeeId()).setEmployeeName(newEmployeeName)
                    ).collect(Collectors.toList()));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            totalList.forEach(d -> {
                BorrowOrder borrowOrder = map.get(d.getId());
                SysDept oldDept = oldDeptMap.get(borrowOrder.getDeptId());
                SysEmployee oldEmployee = oldEmployeeMap.get(borrowOrder.getEmployeeId());
                Map<String, String> operContentMap = new HashMap<>();
                operContentMap.put("接收人", newDeptName + "(" + newEmployeeName + ")");
                operContentMap.put("原归属", (Objects.isNull(oldDept) ? "" : oldDept.getDeptName()) + "(" + (Objects.isNull(oldEmployee) ? "" : oldEmployee.getEmployeeName()) + ")");
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(d.getId())
                            .setBusinessType(BusinessLogBusinessType.BORROW_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("移交材料借阅单")
                            .setOperContent(JSONObject.toJSONString(operContentMap))
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return CommonOperateResultDTO.builder().totalList(totalList).successList(totalList).failList(Lists.newArrayList()).build();
    }

    @Override
    @Transactional
    public CommonOperateResultDTO delete(CommonIdVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return new CommonOperateResultDTO();
        }
        List<BorrowOrder> borrowOrders = selectByIds(vo.getIds());
        if (ObjectUtils.isEmpty(borrowOrders)) {
            return new CommonOperateResultDTO();
        }
        List<CommonOperDTO> totalList = borrowOrders.stream().map(borrowOrder ->
                CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> successList = borrowOrders.stream()
                .filter(borrowOrder -> Objects.equals(borrowOrder.getStatus(), BorrowOrderStatus.WAIT_RESUBMIT.getCode()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        List<CommonOperDTO> failList = borrowOrders.stream()
                .filter(borrowOrder -> !Objects.equals(borrowOrder.getStatus(), BorrowOrderStatus.WAIT_RESUBMIT.getCode()))
                .map(borrowOrder ->
                        CommonOperDTO.builder().id(borrowOrder.getId()).name(borrowOrder.getTitle()).build()).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream()
                    .map(d ->
                            new BorrowOrder().setId(d.getId()).setIsDel(Boolean.TRUE)).collect(Collectors.toList()));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            successList.forEach(d -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(d.getId())
                            .setBusinessType(BusinessLogBusinessType.BORROW_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("删除借阅单")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return CommonOperateResultDTO.builder().totalList(totalList).successList(successList).failList(failList).build();
    }

    @Override
    public Integer selectBorrowOrderCount() {
        return count();
    }

    @Override
    public List<BorrowOrder> selectByIds(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<BorrowOrder>().eq(BorrowOrder::getIsDel, Boolean.FALSE)
                .in(BorrowOrder::getId, ids));
    }

    @Override
    public BorrowOrderWorkBenchDTO borrowOrderStatistic(Long deptId) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException(false);
        if (Objects.isNull(userDeptDTO)) {
            return new BorrowOrderWorkBenchDTO();
        }
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return new BorrowOrderWorkBenchDTO();
        }
        List<BorrowOrder> borrowOrders = list(new LambdaQueryWrapper<BorrowOrder>()
                .in(!ObjectUtils.isEmpty(userDeptDTO.getDeptIds()), BorrowOrder::getDeptId, userDeptDTO.getDeptIds())
                .select(BorrowOrder::getId, BorrowOrder::getStatus));
        return BorrowOrderWorkBenchDTO.builder()
                .waitOutStationCount(borrowOrders.stream().filter(d -> Objects.equals(d.getStatus(), BorrowOrderStatus.WAIT_OUT_STATION.getCode())).count())
                .waitReturnCount(borrowOrders.stream().filter(d -> Objects.equals(d.getStatus(), BorrowOrderStatus.WAIT_RETURN.getCode())).count())
                .waitCheckCount(borrowOrders.stream().filter(d -> Objects.equals(d.getStatus(), BorrowOrderStatus.WAIT_CONFIRM.getCode())).count())
                .build();
    }
}
