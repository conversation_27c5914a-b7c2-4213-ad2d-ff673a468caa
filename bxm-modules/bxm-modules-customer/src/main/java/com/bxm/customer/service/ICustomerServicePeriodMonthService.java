package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.ServiceStatus;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodDTO;
import com.bxm.customer.domain.CBusinessTagRelation;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.vo.CustomerPeriodVO;
import com.bxm.customer.domain.vo.CustomerServiceChangeBusinessDeptVO;
import com.bxm.customer.domain.vo.CustomerServiceDispatchVO;
import com.bxm.customer.domain.vo.CustomerServiceTaxTypeCheckVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;

import java.util.List;

/**
 * 客户服务月度账期Service接口
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
public interface ICustomerServicePeriodMonthService extends IService<CustomerServicePeriodMonth> {
    /**
     * 查询客户服务月度账期
     *
     * @param id 客户服务月度账期主键
     * @return 客户服务月度账期
     */
    public CustomerServicePeriodMonth selectCustomerServicePeriodMonthById(Long id);

    /**
     * 查询客户服务月度账期列表
     *
     * @param customerServicePeriodMonth 客户服务月度账期
     * @return 客户服务月度账期集合
     */
    public List<CustomerServicePeriodMonth> selectCustomerServicePeriodMonthList(CustomerServicePeriodMonth customerServicePeriodMonth);

    /**
     * 新增客户服务月度账期
     *
     * @param customerServicePeriodMonth 客户服务月度账期
     * @return 结果
     */
    public int insertCustomerServicePeriodMonth(CustomerServicePeriodMonth customerServicePeriodMonth);

    /**
     * 修改客户服务月度账期
     *
     * @param customerServicePeriodMonth 客户服务月度账期
     * @return 结果
     */
    public int updateCustomerServicePeriodMonth(CustomerServicePeriodMonth customerServicePeriodMonth);

    /**
     * 批量删除客户服务月度账期
     *
     * @param ids 需要删除的客户服务月度账期主键集合
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthByIds(Long[] ids);

    /**
     * 删除客户服务月度账期信息
     *
     * @param id 客户服务月度账期主键
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthById(Long id);

    void saveMonthPeriodList(CCustomerService customerService, List<CustomerServicePeriodMonth> monthPeriodList, List<TagDTO> periodTags, List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList, Long deptId, Long userId, String operName);

    void saveMonthPeriodListV2(CCustomerService customerService, List<CustomerServicePeriodMonth> monthPeriodList, List<CBusinessTagRelation> relations, List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList);

    void updateMonthPeriodList(List<CustomerServicePeriodMonth> monthPeriodList, List<TagDTO> periodTags, Integer validPeriod, List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList);

    void deleteMonthPeriodList(List<Long> periodIds);

    void updateBusinessDept(CustomerServiceChangeBusinessDeptVO vo, List<Long> customerServiceIds, Long topBusinessDeptId, Integer validPeriod);

    void updateAdvisorDept(CustomerServiceDispatchVO vo, List<Long> customerServiceIds, Long topAdvisorDeptId, Long businessTopDeptId, Integer validPeriod);

    void updateAccountingDept(CustomerServiceDispatchVO vo, List<Long> customerServiceIds, Long topAccountingDeptId, Integer validPeriod);

    void updateAccountingTopDept(List<Long> customerServiceIds, Long accountingTopDeptId);

    CustomerServicePeriodMonth selectLastPeriodByCustomerServiceId(Long customerServiceId);

    List<CustomerServicePeriodMonth> selectByCustomerServiceId(Long customerServiceId);

    CustomerServicePeriodMonth selectByCustomerServiceIdAndPeriod(Long customerServiceId, Integer period);

    List<CustomerServicePeriodMonth> selectByCustomerServiceIdsAndPeriod(List<Long> customerServiceIds, Integer period);

    List<CustomerServicePeriodMonth> periodSelect(Long customerServiceId, Integer tabType);

    List<CustomerServicePeriodMonth> periodSelectForDeliver(Long customerServiceId, Integer tabType, Long deptId);

    List<CustomerServicePeriodMonth> periodSelectForDeliverV2(Long customerServiceId, Integer tabType, Long deptId);

    List<CustomerServicePeriodMonth> getCustomerPeriodByCreditCodeAndPeriod(CustomerPeriodVO vo);

    List<CustomerServicePeriodMonth> selectByTaxTypeAndPeriodAndStatus(Integer taxType, Integer period, ServiceStatus serviceStatus);

    List<CustomerServicePeriodMonth> incomePeriodSelect(Long customerServiceId);

    void updatePeriodTaxCheck(Long customerServiceId, List<CustomerServiceTaxTypeCheckVO> taxTypes);

    void deletePeriodMonthTagRelationByCustomerIds(List<Long> customerServiceIds, Integer startPeriod, Integer endPeriod);

    void deletePeriodMonthTaxCheckByCustomerIds(List<Long> customerServiceIds, Integer startPeriod, Integer endPeriod);

    void savePeriodMonthTagRelationByCustomerIds(List<Long> customerServiceIds, Integer startPeriod, Integer endPeriod);

    void savePeriodMonthEmployeeByCustomerIds(List<Long> customerServiceIds);

    void savePeriodMonthTaxCheckByCustomerIds(List<Long> customerServiceIds, Integer startPeriod, Integer endPeriod);

    List<RemoteCustomerPeriodDTO> getCustomerPeriodByPeriodRange(Integer periodMin, Integer periodMax, Long deptId, Long userId);

    //获取没有入账交付单的账期
    //历史数据处理
    List<CustomerServicePeriodMonth> getNoInAccount();

    List<CustomerServicePeriodMonth> getNoInAccountByCustomerService(Long customerServiceId);

    List<CustomerServicePeriodMonth> getNoInAccountByCustomerServiceBatch(List<Long> customerServiceIds);

    //从其他地方生成账期
    void saveMonthPeriodListFromOther(
            CCustomerService cCustomerService, List<TagDTO> tagDTOS, Integer startPeriod, Integer endPeriod, Integer addFromType, Long addFromId,
            Long accountingDeptId, Long deptId, Long userId, String operName
    );

    //分派后再次分派修改账期的会计相关信息
    void updateAccountingByOperateAssign(Integer addFromType, Long id, Long accountingDeptId);

    //删除其他地方生成的账期
    void operateDeleteByAddFromType(Long deptId, Integer addFromType, Long addFromId);

    List<CustomerServicePeriodMonth> periodSelectForAccountingCashier(Long customerServiceId, Integer accountingCashierType);

    List<String> materialFilePeriodSelect(Long materialDeliverId);

    boolean checkCanCollectDebt(Long customerServiceId, Integer periodStart, Integer periodEnd);

    void checkCanCollectDebtV2(Long customerServiceId, Integer periodStart, Integer periodEnd);

    /**
     * 检查指定条件下是否存在账期记录
     *
     * @param businessTopDeptId 顶级业务部门ID
     * @param creditCode 统一社会信用代码
     * @param periodStart 账期开始时间
     * @param periodEnd 账期结束时间
     * @return 是否存在记录
     */
    boolean checkPeriodExists(Long businessTopDeptId, String creditCode, Integer periodStart, Integer periodEnd);
}
