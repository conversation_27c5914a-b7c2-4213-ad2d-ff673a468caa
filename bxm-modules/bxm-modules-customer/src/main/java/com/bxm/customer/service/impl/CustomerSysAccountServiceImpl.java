package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerSysAccountMapper;
import com.bxm.customer.domain.CustomerSysAccount;
import com.bxm.customer.service.ICustomerSysAccountService;

/**
 * 客户服务系统账号Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Service
public class CustomerSysAccountServiceImpl extends ServiceImpl<CustomerSysAccountMapper, CustomerSysAccount> implements ICustomerSysAccountService
{
    @Autowired
    private CustomerSysAccountMapper customerSysAccountMapper;

    /**
     * 查询客户服务系统账号
     * 
     * @param id 客户服务系统账号主键
     * @return 客户服务系统账号
     */
    @Override
    public CustomerSysAccount selectCustomerSysAccountById(Long id)
    {
        return customerSysAccountMapper.selectCustomerSysAccountById(id);
    }

    /**
     * 查询客户服务系统账号列表
     * 
     * @param customerSysAccount 客户服务系统账号
     * @return 客户服务系统账号
     */
    @Override
    public List<CustomerSysAccount> selectCustomerSysAccountList(CustomerSysAccount customerSysAccount)
    {
        return customerSysAccountMapper.selectCustomerSysAccountList(customerSysAccount);
    }

    /**
     * 新增客户服务系统账号
     * 
     * @param customerSysAccount 客户服务系统账号
     * @return 结果
     */
    @Override
    public int insertCustomerSysAccount(CustomerSysAccount customerSysAccount)
    {
        customerSysAccount.setCreateTime(DateUtils.getNowDate());
        return customerSysAccountMapper.insertCustomerSysAccount(customerSysAccount);
    }

    /**
     * 修改客户服务系统账号
     * 
     * @param customerSysAccount 客户服务系统账号
     * @return 结果
     */
    @Override
    public int updateCustomerSysAccount(CustomerSysAccount customerSysAccount)
    {
        customerSysAccount.setUpdateTime(DateUtils.getNowDate());
        return customerSysAccountMapper.updateCustomerSysAccount(customerSysAccount);
    }

    /**
     * 批量删除客户服务系统账号
     * 
     * @param ids 需要删除的客户服务系统账号主键
     * @return 结果
     */
    @Override
    public int deleteCustomerSysAccountByIds(Long[] ids)
    {
        return customerSysAccountMapper.deleteCustomerSysAccountByIds(ids);
    }

    /**
     * 删除客户服务系统账号信息
     * 
     * @param id 客户服务系统账号主键
     * @return 结果
     */
    @Override
    public int deleteCustomerSysAccountById(Long id)
    {
        return customerSysAccountMapper.deleteCustomerSysAccountById(id);
    }

    @Override
    public List<CustomerSysAccount> selectByCustomerServiceId(Long customerServiceId, Integer deptType) {
        if (Objects.isNull(customerServiceId)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(new LambdaQueryWrapper<CustomerSysAccount>()
                .eq(CustomerSysAccount::getCustomerServiceId, customerServiceId)
                .eq(CustomerSysAccount::getIsDel, Boolean.FALSE)
                .and(queryWrapper -> queryWrapper.eq(CustomerSysAccount::getIsPrivate, false)
                        .or(subWrapper -> subWrapper.eq(CustomerSysAccount::getIsPrivate, true).eq(CustomerSysAccount::getDeptType, deptType)))
                .orderByDesc(CustomerSysAccount::getId));
    }

    @Override
    public long selectCountByCustomerServiceId(Long customerServiceId) {
        if (Objects.isNull(customerServiceId)) {
            return 0L;
        }
        return baseMapper.selectCount(new LambdaQueryWrapper<CustomerSysAccount>()
                .eq(CustomerSysAccount::getCustomerServiceId, customerServiceId)
                .eq(CustomerSysAccount::getIsDel, Boolean.FALSE)
                .orderByDesc(CustomerSysAccount::getId));
    }
}
