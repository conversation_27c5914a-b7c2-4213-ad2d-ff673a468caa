package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 客户服务税种核定对象 c_customer_tax_type_check
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Data
@ApiModel("客户服务税种核定对象")
@Accessors(chain = true)
@TableName("c_customer_tax_type_check")
public class CustomerTaxTypeCheck extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 客户服务id,0代表默认 */
    @Excel(name = "客户服务id,0代表默认")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id,0代表默认")
    private Long customerServiceId;

    /** 上报类型，1-月报，2-季报，3-年报，4-次报 */
    @Excel(name = "上报类型，1-月报，2-季报，3-年报，4-次报")
    @TableField("report_type")
    @ApiModelProperty(value = "上报类型，1-月报，2-季报，3-年报，4-次报")
    private Integer reportType;

    /** 税种类型 */
    @Excel(name = "税种类型")
    @TableField("tax_type")
    @ApiModelProperty(value = "税种类型")
    private String taxType;

    @Excel(name = "税款所属期起")
    @TableField("tax_period_start")
    @ApiModelProperty(value = "税款所属期起")
    private String taxPeriodStart;

    @Excel(name = "税款所属期止")
    @TableField("tax_period_end")
    @ApiModelProperty(value = "税款所属期止")
    private String taxPeriodEnd;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerTaxTypeCheck that = (CustomerTaxTypeCheck) o;
        return Objects.equals(id, that.id) && Objects.equals(customerServiceId, that.customerServiceId) && Objects.equals(reportType, that.reportType) && Objects.equals(taxType, that.taxType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, customerServiceId, reportType, taxType);
    }
}
