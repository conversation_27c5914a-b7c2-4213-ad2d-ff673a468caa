package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePeriodCashierAccountingSimpleDTO {

    @ApiModelProperty("交付单id")
    private Long accountingCashierId;

    @ApiModelProperty("交付单标题")
    private String title;

    @ApiModelProperty("交付单类型")
    private Integer deliverType;

    @ApiModelProperty("交付单类型名称")
    private String deliverTypeName;

    @ApiModelProperty("交付单状态")
    private Integer deliverStatus;

    @ApiModelProperty("交付单状态名称")
    private String deliverStatusName;

    @ApiModelProperty("材料件数量，点击查看附件调用接口/bxmCustomer/accountingCashier/getAccountingCashierFiles，fileType传1")
    private Long materialFileCount;

    @ApiModelProperty("交付附件数量，点击查看附件调用接口/bxmCustomer/accountingCashier/getAccountingCashierFiles，fileType传2")
    private Long deliverFileCount;
}
