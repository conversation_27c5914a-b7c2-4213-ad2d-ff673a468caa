package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.TaxType;
import com.bxm.common.core.enums.quality.QualityCheckingCycle;
import com.bxm.common.core.enums.quality.QualityCheckingRecordResult;
import com.bxm.common.core.enums.quality.QualityCheckingRecordStatus;
import com.bxm.common.core.enums.quality.QualityCheckingType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.QualityCheckingItem;
import com.bxm.customer.domain.QualityCheckingRecord;
import com.bxm.customer.domain.QualityCheckingResult;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingRecordDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingRecordOperateDTO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingRecordVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultCloseVO;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.mapper.QualityCheckingItemMapper;
import com.bxm.customer.mapper.QualityCheckingRecordMapper;
import com.bxm.customer.mapper.QualityCheckingResultMapper;
import com.bxm.customer.service.ICBusinessTagRelationService;
import com.bxm.customer.service.ICustomerServiceDocHandoverService;
import com.bxm.customer.service.IQualityCheckingFileService;
import com.bxm.customer.service.IQualityCheckingRecordService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 质检记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Slf4j
public class QualityCheckingRecordServiceImpl extends ServiceImpl<QualityCheckingRecordMapper, QualityCheckingRecord> implements IQualityCheckingRecordService
{
    @Autowired
    private QualityCheckingRecordMapper qualityCheckingRecordMapper;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private QualityCheckingItemMapper qualityCheckingItemMapper;

    @Autowired
    private IQualityCheckingFileService qualityCheckingFileService;

    @Autowired
    private QualityCheckingResultMapper qualityCheckingResultMapper;

    @Autowired
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    /**
     * 查询质检记录
     * 
     * @param id 质检记录主键
     * @return 质检记录
     */
    @Override
    public QualityCheckingRecord selectQualityCheckingRecordById(Long id)
    {
        return qualityCheckingRecordMapper.selectQualityCheckingRecordById(id);
    }

    /**
     * 查询质检记录列表
     * 
     * @param qualityCheckingRecord 质检记录
     * @return 质检记录
     */
    @Override
    public List<QualityCheckingRecord> selectQualityCheckingRecordList(QualityCheckingRecord qualityCheckingRecord)
    {
        return qualityCheckingRecordMapper.selectQualityCheckingRecordList(qualityCheckingRecord);
    }

    /**
     * 新增质检记录
     * 
     * @param qualityCheckingRecord 质检记录
     * @return 结果
     */
    @Override
    public int insertQualityCheckingRecord(QualityCheckingRecord qualityCheckingRecord)
    {
        qualityCheckingRecord.setCreateTime(DateUtils.getNowDate());
        return qualityCheckingRecordMapper.insertQualityCheckingRecord(qualityCheckingRecord);
    }

    /**
     * 修改质检记录
     * 
     * @param qualityCheckingRecord 质检记录
     * @return 结果
     */
    @Override
    public int updateQualityCheckingRecord(QualityCheckingRecord qualityCheckingRecord)
    {
        qualityCheckingRecord.setUpdateTime(DateUtils.getNowDate());
        return qualityCheckingRecordMapper.updateQualityCheckingRecord(qualityCheckingRecord);
    }

    /**
     * 批量删除质检记录
     * 
     * @param ids 需要删除的质检记录主键
     * @return 结果
     */
    @Override
    public int deleteQualityCheckingRecordByIds(Long[] ids)
    {
        return qualityCheckingRecordMapper.deleteQualityCheckingRecordByIds(ids);
    }

    /**
     * 删除质检记录信息
     * 
     * @param id 质检记录主键
     * @return 结果
     */
    @Override
    public int deleteQualityCheckingRecordById(Long id)
    {
        return qualityCheckingRecordMapper.deleteQualityCheckingRecordById(id);
    }

    @Override
    public IPage<QualityCheckingRecordDTO> qualityCheckingRecordPageList(QualityCheckingRecordVO vo) {
        IPage<QualityCheckingRecordDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        UserDeptDTO userDept = remoteDeptService.userDeptList(vo.getUserId(), vo.getDeptId()).getDataThrowException();
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return result;
        }
        List<Long> batchSearchCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            batchSearchCustomerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(batchSearchCustomerServiceIds)) {
                return result;
            }
        }
        if (!StringUtils.isEmpty(vo.getCreateTimeStart())) {
            vo.setCreateTimeStart(vo.getCreateTimeStart() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getCreateTimeEnd())) {
            vo.setCreateTimeEnd(vo.getCreateTimeEnd() + " 23:59:59");
        }
        if (!StringUtils.isEmpty(vo.getPeriodTagName())) {
            if (!vo.getPeriodTagName().contains("&")) {
                vo.setPeriodTagType(1);
                vo.setPeriodTagNameList(Collections.singletonList(vo.getPeriodTagName()));
                vo.setPeriodTagSize(1);
            } else {
                vo.setPeriodTagType(2);
                vo.setPeriodTagNameList(Arrays.asList(vo.getPeriodTagName().split("&")));
                vo.setPeriodTagSize(vo.getPeriodTagNameList().size());
            }
        }
        List<QualityCheckingRecordDTO> data = qualityCheckingRecordMapper.qualityCheckingRecordPageList(result, vo, userDept, batchSearchCustomerServiceIds);
        if (!ObjectUtils.isEmpty(data)) {
            fillQualityCheckingRecordData(data);
        }
        result.setRecords(data);
        return result;
    }

    @Override
    @Transactional
    public TCommonOperateDTO<QualityCheckingRecord> closeQualityRecord(Long deptId, QualityCheckingResultCloseVO vo) {
        OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
        String operType = "关闭质检记录";
        LocalDateTime operTime = LocalDateTime.now();
        String operContentStr = "";
        if (!Objects.isNull(vo.getQualityCheckingRecordId())) {
            closeSingle(vo.getQualityCheckingRecordId(), operateUserInfo, operType, operTime, operContentStr, vo);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getQualityCheckingRecordIds())) {
                throw new ServiceException("请选择要关闭的质检记录");
            }
            return closeBatch(vo.getQualityCheckingRecordIds(), operateUserInfo, operType, operTime, operContentStr, vo);
        }
    }

    @Override
    public boolean existsCheckingRecordByQualityCheckingResultId(Long qualityCheckingResultId) {
        if (Objects.isNull(qualityCheckingResultId)) {
            return false;
        }
        return count(new LambdaQueryWrapper<QualityCheckingRecord>()
                .eq(QualityCheckingRecord::getQualityCheckingResultId, qualityCheckingResultId)
                .eq(QualityCheckingRecord::getStatus, QualityCheckingRecordStatus.UNEXECUTED.getCode())
                .eq(QualityCheckingRecord::getIsDel, false)) > 0;
    }

    @Override
    public Map<Long, Boolean> existsCheckingRecordByQualityCheckingResultIds(List<Long> qualityCheckingResultIds) {
        if (ObjectUtils.isEmpty(qualityCheckingResultIds)) {
            return Collections.emptyMap();
        }
        Map<Long, List<QualityCheckingRecord>> recordMap = list(new LambdaQueryWrapper<QualityCheckingRecord>()
                .in(QualityCheckingRecord::getQualityCheckingResultId, qualityCheckingResultIds)
                .eq(QualityCheckingRecord::getStatus, QualityCheckingRecordStatus.UNEXECUTED.getCode())
                .eq(QualityCheckingRecord::getIsDel, false)).stream().collect(Collectors.groupingBy(QualityCheckingRecord::getQualityCheckingResultId));
        return qualityCheckingResultIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        recordMap::containsKey
                ));
    }

    @Override
    public QualityCheckingRecord createNewQualityCheckingRecord(QualityCheckingResult checkingResult, OperateUserInfoDTO operateUserInfo, LocalDateTime operTime) {
        QualityCheckingRecord qualityCheckingRecord = new QualityCheckingRecord()
                .setBatchNo(generateRecordBatchNo())
                .setQualityCheckingResultId(checkingResult.getId())
                .setCustomerServiceId(checkingResult.getCustomerServiceId())
                .setCustomerServicePeriodMonthId(checkingResult.getCustomerServicePeriodMonthId())
                .setPeriod(checkingResult.getPeriod())
                .setQualityCheckingItemId(checkingResult.getQualityCheckingItemId())
                .setQualityCheckingType(checkingResult.getQualityCheckingType())
                .setQualityCheckingCycle(checkingResult.getQualityCheckingCycle())
                .setStatus(QualityCheckingRecordStatus.UNEXECUTED.getCode())
                .setCreateId(operateUserInfo.getUserId())
                .setIsDel(false);
        qualityCheckingRecord.setCreateBy(operateUserInfo.getOperName());
        save(qualityCheckingRecord);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(qualityCheckingRecord.getId())
                    .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RECORD.getCode())
                    .setDeptId(operateUserInfo.getDeptId())
                    .setOperType("新建")
                    .setOperName(operateUserInfo.getOperName())
                    .setCreateTime(operTime)
                    .setOperUserId(operateUserInfo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return qualityCheckingRecord;
    }

    @Override
    @Transactional
    public QualityCheckingRecord createNewQualityCheckingRecord(QualityCheckingResult checkingResult, Long userId, Long deptId, String operName, LocalDateTime operTime, String batchNo) {
        if (existsCheckingRecordByQualityCheckingResultId(checkingResult.getId())) {
            return null;
        }
        QualityCheckingRecord qualityCheckingRecord = new QualityCheckingRecord()
                .setBatchNo(batchNo)
                .setQualityCheckingResultId(checkingResult.getId())
                .setCustomerServiceId(checkingResult.getCustomerServiceId())
                .setCustomerServicePeriodMonthId(checkingResult.getCustomerServicePeriodMonthId())
                .setPeriod(checkingResult.getPeriod())
                .setQualityCheckingItemId(checkingResult.getQualityCheckingItemId())
                .setQualityCheckingType(checkingResult.getQualityCheckingType())
                .setQualityCheckingCycle(checkingResult.getQualityCheckingCycle())
                .setStatus(QualityCheckingRecordStatus.UNEXECUTED.getCode())
                .setCreateId(userId)
                .setIsDel(false);
        qualityCheckingRecord.setCreateBy(operName);
        return qualityCheckingRecord;
    }

    @Override
    public Integer getCheckedTimesByQualityCheckingResultId(Long qualityCheckingResultId) {
        if (Objects.isNull(qualityCheckingResultId)) {
            return 0;
        }
        return count(new LambdaQueryWrapper<QualityCheckingRecord>()
                .eq(QualityCheckingRecord::getQualityCheckingResultId, qualityCheckingResultId)
                .eq(QualityCheckingRecord::getIsDel, false)
                .eq(QualityCheckingRecord::getStatus, QualityCheckingRecordStatus.EXECUTING.getCode()));
    }

    @Override
    public List<QualityCheckingRecord> getCheckingRecordList(List<Long> customerServicePeriodMonthIds) {
        if (ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<QualityCheckingRecord>()
                .eq(QualityCheckingRecord::getIsDel, false)
                .eq(QualityCheckingRecord::getStatus, QualityCheckingRecordStatus.UNEXECUTED.getCode())
                .in(QualityCheckingRecord::getCustomerServicePeriodMonthId, customerServicePeriodMonthIds)
                .select(QualityCheckingRecord::getId, QualityCheckingRecord::getCustomerServicePeriodMonthId, QualityCheckingRecord::getQualityCheckingItemId));
    }

    private TCommonOperateDTO<QualityCheckingRecord> closeBatch(List<Long> qualityCheckingRecordIds, OperateUserInfoDTO operateUserInfo, String operType, LocalDateTime operTime, String operContentStr, QualityCheckingResultCloseVO vo) {
        TCommonOperateDTO<QualityCheckingRecord> result = new TCommonOperateDTO<>();
        List<QualityCheckingRecord> total = list(new LambdaQueryWrapper<QualityCheckingRecord>()
                .eq(QualityCheckingRecord::getIsDel, false)
                .in(QualityCheckingRecord::getId, qualityCheckingRecordIds));
        result.setTotal(total);
        if (ObjectUtils.isEmpty(total)) {
            result.setSuccess(Collections.emptyList());
            result.setFail(Collections.emptyList());
            return result;
        }
        List<QualityCheckingRecord> success = Lists.newArrayList();
        List<QualityCheckingRecord> fail = Lists.newArrayList();
        List<Long> failIds = Lists.newArrayList();
        Map<Long, String> errorMagMap = new HashMap<>();
        for (QualityCheckingRecord qualityCheckingRecord : total) {
            if (Objects.equals(qualityCheckingRecord.getStatus(), QualityCheckingRecordStatus.UNEXECUTED.getCode())) {
                success.add(qualityCheckingRecord);
            } else {
                fail.add(qualityCheckingRecord);
                failIds.add(qualityCheckingRecord.getId());
                errorMagMap.put(qualityCheckingRecord.getId(), "只有进行中状态下可关闭");
            }
        }
        result.setSuccess(success);
        result.setFail(fail);
        if (!ObjectUtils.isEmpty(failIds)) {
            String batchNo = StringUtils.getUuid();
            result.setErrorDataBatchNo(batchNo);
            buildErrorDataList(failIds, errorMagMap, batchNo);
        }
        if (!ObjectUtils.isEmpty(success)) {
            updateBatchById(success.stream().map(row -> new QualityCheckingRecord().setId(row.getId())
                    .setStatus(QualityCheckingRecordStatus.UNCLOSED.getCode())
                    .setCloseTime(operTime)).collect(Collectors.toList()));
            List<QualityCheckingResult> checkingResults = qualityCheckingResultMapper.selectList(new LambdaQueryWrapper<QualityCheckingResult>()
                    .in(QualityCheckingResult::getId, success.stream().map(QualityCheckingRecord::getQualityCheckingResultId).distinct().collect(Collectors.toList()))
                    .eq(QualityCheckingResult::getIsDel, false));
            success.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RECORD.getCode())
                            .setDeptId(operateUserInfo.getDeptId())
                            .setOperType(operType)
                            .setOperName(operateUserInfo.getOperName())
                            .setCreateTime(operTime)
                            .setOperContent(operContentStr)
                            .setOperUserId(operateUserInfo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
            if (!ObjectUtils.isEmpty(checkingResults)) {
                checkingResults.forEach(row -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                                .setDeptId(operateUserInfo.getDeptId())
                                .setOperType(operType)
                                .setOperName(operateUserInfo.getOperName())
                                .setCreateTime(operTime)
                                .setOperContent(operContentStr)
                                .setOperUserId(operateUserInfo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
        }
        return result;
    }

    private void buildErrorDataList(List<Long> failIds, Map<Long, String> errorMagMap, String batchNo) {
        List<QualityCheckingRecordDTO> failRecordList = baseMapper.qualityCheckingRecordListByIds(failIds);
        if (!ObjectUtils.isEmpty(failRecordList)) {
            fillQualityCheckingRecordData(failRecordList);
            List<QualityCheckingRecordOperateDTO> errorDTOList = failRecordList.stream().map(e -> {
                QualityCheckingRecordOperateDTO dto = new QualityCheckingRecordOperateDTO();
                BeanUtils.copyProperties(e, dto);
                dto.setErrorMsg(errorMagMap.getOrDefault(e.getQualityCheckingRecordId(), ""));
                return dto;
            }).collect(Collectors.toList());
            redisService.setLargeCacheList(CacheConstants.QUALITY_CHECKING_RECORD_OPERATE_ERROR_RECORD + batchNo, errorDTOList, 500, 60 * 60, TimeUnit.SECONDS);
        }
    }

    private void closeSingle(Long qualityCheckingRecordId, OperateUserInfoDTO operateUserInfo, String operType, LocalDateTime operTime, String operContentStr, QualityCheckingResultCloseVO vo) {
        QualityCheckingRecord checkingRecord = getById(qualityCheckingRecordId);
        if (Objects.isNull(checkingRecord) || checkingRecord.getIsDel()) {
            throw new ServiceException("质检记录不存在");
        }
        if (!Objects.equals(checkingRecord.getStatus(), QualityCheckingRecordStatus.UNEXECUTED.getCode())) {
            throw new ServiceException("只有进行中状态下可关闭");
        }
        updateById(new QualityCheckingRecord().setId(qualityCheckingRecordId)
                .setStatus(QualityCheckingRecordStatus.UNCLOSED.getCode())
                .setCloseTime(operTime));
        QualityCheckingResult checkingResult = qualityCheckingResultMapper.selectById(checkingRecord.getQualityCheckingResultId());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(qualityCheckingRecordId)
                    .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RECORD.getCode())
                    .setDeptId(operateUserInfo.getDeptId())
                    .setOperType(operType)
                    .setOperName(operateUserInfo.getOperName())
                    .setCreateTime(operTime)
                    .setOperContent(operContentStr)
                    .setOperUserId(operateUserInfo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!Objects.isNull(checkingResult) && !checkingResult.getIsDel()) {
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(checkingResult.getId())
                        .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                        .setDeptId(operateUserInfo.getDeptId())
                        .setOperType(operType)
                        .setOperName(operateUserInfo.getOperName())
                        .setCreateTime(operTime)
                        .setOperContent(operContentStr)
                        .setOperUserId(operateUserInfo.getUserId()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    private void fillQualityCheckingRecordData(List<QualityCheckingRecordDTO> data) {
        if (ObjectUtils.isEmpty(data)) {
            return;
        }
        Set<Long> deptIdSet = new HashSet<>();
        List<Long> lastDeptIds = Lists.newArrayList();
        Set<Long> advisorDeptIds = data.stream().map(QualityCheckingRecordDTO::getPeriodAdvisorDeptId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> accountingDeptIds = data.stream().map(QualityCheckingRecordDTO::getPeriodAccountingDeptId).filter(Objects::nonNull).collect(Collectors.toSet());
        deptIdSet.addAll(data.stream().map(QualityCheckingRecordDTO::getPeriodBusinessTopDeptId).filter(Objects::nonNull).collect(Collectors.toSet()));
        deptIdSet.addAll(data.stream().map(QualityCheckingRecordDTO::getPeriodBusinessDeptId).filter(Objects::nonNull).collect(Collectors.toSet()));
        deptIdSet.addAll(advisorDeptIds);
        deptIdSet.addAll(data.stream().map(QualityCheckingRecordDTO::getPeriodAccountingTopDeptId).filter(Objects::nonNull).collect(Collectors.toSet()));
        deptIdSet.addAll(accountingDeptIds);
        lastDeptIds.addAll(advisorDeptIds);
        lastDeptIds.addAll(accountingDeptIds);
        Map<Long, String> deptMap = ObjectUtils.isEmpty(deptIdSet) ? Maps.newHashMap() :
                remoteDeptService.getByDeptIds(new ArrayList<>(deptIdSet)).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(lastDeptIds) ? Maps.newHashMap() :
                remoteEmployeeService.getBatchEmployeeByDeptIds(lastDeptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(data.stream().map(QualityCheckingRecordDTO::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
        Map<Long, String> itemMap = qualityCheckingItemMapper.selectBatchIds(data.stream().map(QualityCheckingRecordDTO::getQualityCheckingItemId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(QualityCheckingItem::getId, QualityCheckingItem::getItemName));
        Map<Long, List<CommonFileVO>> fileMap = qualityCheckingFileService.getBatchByBusinessIdAndBusinessType(Constants.QUALITY_CHECKING_RECORD_TYPE, data.stream().map(QualityCheckingRecordDTO::getQualityCheckingRecordId).distinct().collect(Collectors.toList()));
        data.forEach(item -> {
            List<TagDTO> tags = tagMap.get(item.getCustomerServicePeriodMonthId());
            item.setPeriodStr(DateUtils.periodToYeaMonth(item.getPeriod()));
            item.setPeriodBusinessTopDeptName(deptMap.getOrDefault(item.getPeriodBusinessTopDeptId(), ""));
            item.setPeriodBusinessDeptName(deptMap.getOrDefault(item.getPeriodBusinessDeptId(), ""));
            item.setPeriodAdvisorDeptName(deptMap.getOrDefault(item.getPeriodAdvisorDeptId(), ""));
            item.setPeriodAdvisorEmployeeName(employeeMap.getOrDefault(item.getPeriodAdvisorDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")));
            item.setPeriodAccountingTopDeptName(deptMap.getOrDefault(item.getPeriodAccountingTopDeptId(), ""));
            item.setPeriodAccountingDeptName(deptMap.getOrDefault(item.getPeriodAccountingDeptId(), ""));
            item.setPeriodAccountingEmployeeName(employeeMap.getOrDefault(item.getPeriodAccountingDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")));
            item.setPeriodTags(ObjectUtils.isEmpty(tags) ? "" :
                    tags.stream().map(TagDTO::getFullTagName).collect(Collectors.joining("，")));
            item.setPeriodTaxTypeName(Objects.isNull(item.getPeriodTaxType()) ? "" : TaxType.getByCode(item.getPeriodTaxType()).getDesc());
            item.setQualityCheckingTypeName(QualityCheckingType.getByCode(item.getQualityCheckingType()).getName());
            item.setQualityCheckingItemName(itemMap.getOrDefault(item.getQualityCheckingItemId(), ""));
            item.setQualityCheckingCycleName(QualityCheckingCycle.getByCode(item.getQualityCheckingCycle()).getName());
            item.setStatusName(QualityCheckingRecordStatus.getByCode(item.getStatus()).getName());
            item.setCheckingResultName(Objects.isNull(item.getCheckingResult()) ? "" : QualityCheckingRecordResult.getByCode(item.getCheckingResult()).getName());
            item.setFiles(fileMap.getOrDefault(item.getQualityCheckingRecordId(), Lists.newArrayList()));
        });
    }

    private String generateRecordBatchNo() {
        return "QCR" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")) + StringUtils.generateRandomCode(5);
    }
}
