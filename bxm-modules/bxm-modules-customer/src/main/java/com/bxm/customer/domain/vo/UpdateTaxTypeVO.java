package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateTaxTypeVO {

    @ApiModelProperty("服务id")
    private Long customerServiceId;

    @ApiModelProperty("生效账期，1-当月生效，2-次月生效")
    private Integer validPeriod;

    @ApiModelProperty(hidden = true)
    private Integer isTask;
}
