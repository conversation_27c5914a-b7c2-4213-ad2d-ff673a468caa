package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.api.domain.dto.RemoteAccountingCashierSimpleDTO;
import com.bxm.customer.api.domain.vo.RemoteAccountingCashierPeriodTypeVO;
import com.bxm.customer.domain.CustomerServiceCashierAccounting;
import com.bxm.customer.domain.CustomerServiceCashierAccountingFile;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDTO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDeliverStatusDataCount;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierMiniListDTO;
import com.bxm.customer.domain.dto.accoutingCashier.CustomerServicePeriodMonthFileCountDTO;
import com.bxm.customer.domain.dto.businessTask.BizIdBankAccountDTO;
import com.bxm.customer.domain.dto.inAccount.CustomerInAccountMaxPeriodDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialPushPreviewListDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialRepeatDTO;
import com.bxm.customer.domain.vo.accoutingCashier.AccountingCashierMiniListSearchVO;
import com.bxm.customer.domain.vo.accoutingCashier.AccountingCashierSearchVO;
import com.bxm.customer.domain.vo.accoutingCashier.MaterialFileSimpleVO;
import com.bxm.customer.domain.vo.accoutingCashier.PeriodBankAccountNumberVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客户账务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@Mapper
public interface CustomerServiceCashierAccountingMapper extends BaseMapper<CustomerServiceCashierAccounting>
{
    /**
     * 查询客户账务
     * 
     * @param id 客户账务主键
     * @return 客户账务
     */
    public CustomerServiceCashierAccounting selectCustomerServiceCashierAccountingById(Long id);

    /**
     * 查询客户账务列表
     * 
     * @param customerServiceCashierAccounting 客户账务
     * @return 客户账务集合
     */
    public List<CustomerServiceCashierAccounting> selectCustomerServiceCashierAccountingList(CustomerServiceCashierAccounting customerServiceCashierAccounting);

    /**
     * 新增客户账务
     * 
     * @param customerServiceCashierAccounting 客户账务
     * @return 结果
     */
    public int insertCustomerServiceCashierAccounting(CustomerServiceCashierAccounting customerServiceCashierAccounting);

    /**
     * 修改客户账务
     * 
     * @param customerServiceCashierAccounting 客户账务
     * @return 结果
     */
    public int updateCustomerServiceCashierAccounting(CustomerServiceCashierAccounting customerServiceCashierAccounting);

    /**
     * 删除客户账务
     * 
     * @param id 客户账务主键
     * @return 结果
     */
    public int deleteCustomerServiceCashierAccountingById(Long id);

    /**
     * 批量删除客户账务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceCashierAccountingByIds(Long[] ids);

    Long selectAccountCashierCount(@Param("vo") AccountingCashierSearchVO vo,
                                   @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                   @Param("ids") List<Long> ids,
                                   @Param("customerServiceIds") List<Long> customerServiceIds,
                                   @Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds);

    List<AccountingCashierDTO> accountingCashierList(IPage<AccountingCashierDTO> result,
                                                     @Param("vo") AccountingCashierSearchVO vo,
                                                     @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                     @Param("ids") List<Long> ids,
                                                     @Param("customerServiceIds") List<Long> customerServiceIds,
                                                     @Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds);

    List<AccountingCashierDTO> accountingCashierListPage(@Param("vo") AccountingCashierSearchVO vo,
                                                     @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                     @Param("ids") List<Long> ids,
                                                     @Param("customerServiceIds") List<Long> customerServiceIds,
                                                     @Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds,
                                                         @Param("queryDeptIds") List<Long> queryDeptIds,
                                                         @Param("start") Integer start,
                                                         @Param("limit") Integer limit);

    List<AccountingCashierDTO> accountingCashierListByIds(@Param("ids") List<Long> ids);

    List<CommonDeptCountDTO> accountingCashierPeriodAccountingDeptCountList(@Param("type") Integer type, @Param("userDeptDTO") UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> accountingCashierPeriodAdvisorDeptCountList(@Param("type") Integer type, @Param("userDeptDTO") UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> accountingCashierCustomerAdvisorDeptCountList(@Param("type") Integer type, @Param("userDeptDTO") UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> accountingCashierCustomerAccountingDeptCountList(@Param("type") Integer type, @Param("userDeptDTO") UserDeptDTO userDeptDTO);

    List<CustomerServicePeriodMonthFileCountDTO> getFileCountByPeriodMonthIds(@Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds,
                                                                              @Param("fileType") Integer fileType);

    Long selectLastInAccountIdByCustomerServiceIdAndPeriod(@Param("customerServiceId") Long customerServiceId,
                                                           @Param("startPeriod") Integer startPeriod,
                                                           @Param("endPeriod") Integer endPeriod);

    List<MaterialFileSimpleVO> materialFilesByPeriodId(@Param("customerServicePeriodMonthId") Long customerServicePeriodMonthId,
                                                       @Param("fileType") Integer fileType,
                                                       @Param("bankName") String bankName,
                                                       @Param("fileRemark") String fileRemark);

    List<MaterialFileSimpleVO> materialFilesByIds(@Param("ids") List<Long> ids);

    List<String> materialFileBankSelect(@Param("customerServicePeriodMonthId") Long customerServicePeriodMonthId);

    List<AccountingCashierDeliverStatusDataCount> selectAccountingCashierDeliverStatusDataCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                          @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                          @Param("accountingCashierType") Integer accountingCashierType,
                                                                                          @Param("deliverStatusList") List<Integer> deliverStatusList);

    Long selectAccountingCashierLackOfMaterialCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                    @Param("queryDeptIds") List<Long> queryDeptIds,
                                                    @Param("accountingCashierType") Integer accountingCashierType);

    Long selectHasChangeCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                              @Param("queryDeptIds") List<Long> queryDeptIds,
                              @Param("accountingCashierType") Integer accountingCashierType);

    List<AccountingCashierMiniListDTO> selectAccountingCashierDeliverList(IPage<AccountingCashierMiniListDTO> result,
                                                                          @Param("vo") AccountingCashierMiniListSearchVO vo,
                                                                          @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                          @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                          @Param("tagCustomerServiceIds") List<Long> tagCustomerServiceIds,
                                                                          @Param("batchSearchCustomerServiceIds") List<Long> batchSearchCustomerServiceIds,
                                                                          @Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds,
                                                                          @Param("deliverStatus") Integer deliverStatus,
                                                                          @Param("hasChanged") Integer hasChanged,
                                                                          @Param("materialIntegrity") Integer materialIntegrity,
                                                                          @Param("accountingCashierType") Integer accountingCashierType);

    List<AccountingCashierMiniListDTO> selectAccountingCashierInAccountWaitCreateList(IPage<AccountingCashierMiniListDTO> result,
                                                                                      @Param("vo") AccountingCashierMiniListSearchVO vo,
                                                                                      @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                      @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                      @Param("tagCustomerServiceIds") List<Long> tagCustomerServiceIds,
                                                                                      @Param("batchSearchCustomerServiceIds") List<Long> batchSearchCustomerServiceIds,
                                                                                      @Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds,
                                                                                      @Param("nowPeriod") Integer nowPeriod);

    List<AccountingCashierMiniListDTO> selectAccountingCashierBankWaitCreateList(IPage<AccountingCashierMiniListDTO> result,
                                                                                               @Param("vo") AccountingCashierMiniListSearchVO vo,
                                                                                               @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                               @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                               @Param("tagCustomerServiceIds") List<Long> tagCustomerServiceIds,
                                                                                               @Param("batchSearchCustomerServiceIds") List<Long> batchSearchCustomerServiceIds,
                                                                                               @Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds,
                                                                                 @Param("nowPeriod") Integer nowPeriod);

    List<AccountingCashierMiniListDTO> selectAccountingCashierBankPatrialMisssList(IPage<AccountingCashierMiniListDTO> result,
                                                                                 @Param("vo") AccountingCashierMiniListSearchVO vo,
                                                                                 @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                 @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                 @Param("tagCustomerServiceIds") List<Long> tagCustomerServiceIds,
                                                                                 @Param("batchSearchCustomerServiceIds") List<Long> batchSearchCustomerServiceIds,
                                                                                 @Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds,
                                                                                   @Param("nowPeriod") Integer nowPeriod);

    List<AccountingCashierMiniListDTO> selectAccountingCashierBankUnOpenList(IPage<AccountingCashierMiniListDTO> result,
                                                                             @Param("vo") AccountingCashierMiniListSearchVO vo,
                                                                             @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                             @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                             @Param("tagCustomerServiceIds") List<Long> tagCustomerServiceIds,
                                                                             @Param("batchSearchCustomerServiceIds") List<Long> batchSearchCustomerServiceIds,
                                                                             @Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds,
                                                                             @Param("prePeriod") Integer prePeriod);

    List<CommonDeptCountDTO> selectAccountingCashierBankUnOpenAccountingDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                 @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                 @Param("prePeriod") Integer prePeriod);

    List<CommonDeptCountDTO> selectAccountingCashierBankWaitCreateAccountingDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                                   @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                     @Param("nowPeriod") Integer nowPeriod,
                                                                                     @Param("statisticType") Integer statisticType);

    List<CommonDeptCountDTO> selectAccountingCashierBankPatrialMisssAccountingDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                                   @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                       @Param("nowPeriod") Integer nowPeriod);

    List<CommonDeptCountDTO> selectAccountingCashierDeliverAccountingDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                              @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                              @Param("deliverStatus") Integer deliverStatus,
                                                                              @Param("hasChanged") Integer hasChanged,
                                                                              @Param("accountingCashierType") Integer accountingCashierType);

    List<CommonDeptCountDTO> selectAccountingCashierInAccountWaitCreateAccountingDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                          @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                          @Param("nowPeriod") Integer nowPeriod);

    List<CommonDeptCountDTO> selectAccountingCashierBankUnOpenAdvisorDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                 @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                 @Param("prePeriod") Integer prePeriod);

    List<CommonDeptCountDTO> selectAccountingCashierBankWaitCreateAdvisorDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                                   @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                  @Param("nowPeriod") Integer nowPeriod,
                                                                                  @Param("statisticType") Integer statisticType);

    List<CommonDeptCountDTO> selectAccountingCashierBankPatrialMisssAdvisorDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                                   @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                    @Param("nowPeriod") Integer nowPeriod);

    List<CommonDeptCountDTO> selectAccountingCashierDeliverAdvisorDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                              @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                              @Param("deliverStatus") Integer deliverStatus,
                                                                              @Param("hasChanged") Integer hasChanged,
                                                                              @Param("accountingCashierType") Integer accountingCashierType);

    List<CommonDeptCountDTO> selectAccountingCashierInAccountWaitCreateAdvisorDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                          @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                       @Param("nowPeriod") Integer nowPeriod);

    List<CustomerInAccountMaxPeriodDTO> selectCustomerInAccountMaxPeriod(@Param("customerServiceIds") List<Long> needRpaCustomerServiceIds);

    List<CustomerServiceCashierAccounting> selectBatchByCustomerServicePeriodIdAndBankAccountNumber(@Param("param") List<MaterialPushPreviewListDTO> bankAccountingCashier);

    List<CommonDeptCountDTO> selectAccountingCashierBankWaitCreateCustomerAccountingDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                                           @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                             @Param("nowPeriod") Integer nowPeriod);

    List<CommonDeptCountDTO> selectAccountingCashierBankPatrialMisssCustomerAccountingDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                                           @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                               @Param("nowPeriod") Integer nowPeriod);

    List<CommonDeptCountDTO> selectAccountingCashierDeliverCustomerAccountingDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                      @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                      @Param("deliverStatus") Integer deliverStatus,
                                                                                      @Param("hasChanged") Integer hasChanged,
                                                                                      @Param("accountingCashierType") Integer accountingCashierType);

    List<CommonDeptCountDTO> selectAccountingCashierInAccountWaitCreateCustomerAccountingDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                                  @Param("queryDeptIds") List<Long> queryDeptIds);

    List<MaterialRepeatDTO> selectRepeatFile(@Param("customerServiceId") Long customerServiceId,
                                             @Param("fileName") String fileName);

    List<RemoteAccountingCashierSimpleDTO> getAccountingCashierByPeriodIdsAndAccountingCashierType(@Param("vo") RemoteAccountingCashierPeriodTypeVO vo);

    List<CustomerServiceCashierAccounting> selectBatchByCustomerServicePeriodMonthIdAndBankAccountNumber(@Param("dtoList") List<BizIdBankAccountDTO> dtoList);

    void saveNewPeriodInAccount(@Param("period") Integer period);

    void saveExceptionPeriodInAccount(@Param("period") Integer period);

    List<CommonDeptCountDTO> selectAccountingCashierLakOfMaterialDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                          @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                          @Param("accountingCashierType") Integer accountingCashierType);

    List<CommonDeptCountDTO> selectAccountingCashierLakOfMaterialAdvisorDeptList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                 @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                 @Param("accountingCashierType") Integer accountingCashierType);

    void createFlowAccountingCashier(@Param("period") Integer period,
                                     @Param("customerServicePeriodMonthId") Long customerServicePeriodMonthId,
                                     @Param("createTime") String createTime);

    List<CustomerServiceCashierAccountingFile> selectBatchByPeriodIdAndBankAccountNumber(@Param("voList") List<PeriodBankAccountNumberVO> voList);
}
