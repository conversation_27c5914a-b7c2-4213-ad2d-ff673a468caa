package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.NewCustomerTransferStatus;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.dto.newCustomerTransfer.*;
import com.bxm.customer.domain.vo.newCustomer.NewCustomerCreateVO;
import com.bxm.customer.domain.vo.newCustomer.NewCustomerTransferVO;
import com.bxm.customer.service.INewCustomerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@Api(tags = "新户流转相关")
@RequestMapping("/newCustomerTransfer")
public class NewCustomerTransferController {

    @Autowired
    private INewCustomerInfoService newCustomerInfoService;

    @Autowired
    private RedisService redisService;

    @GetMapping("/list")
    @ApiOperation("新户流转列表")
    @RequiresPermissions("customer:newCustomerTransfer:list")
    public Result<IPage<NewCustomerTransferDTO>> list(@RequestHeader("deptId") Long deptId,
                                                      @RequestParam(defaultValue = "1") int pageNum,
                                                      @RequestParam(defaultValue = "10") int pageSize,
                                                      @RequestParam(required = false, value = "keyWord") @ApiParam("关键词搜索") String keyWord,
                                                      @RequestParam(required = false, value = "status") @ApiParam("流转状态， 0-待完善, 1-待提交, 2-待重提，3-待流转, 4-已流转") Integer status,
                                                      @RequestParam(required = false, value = "taxType") @ApiParam("纳税人性质，1-小规模，2-一般纳税人") Integer taxType,
                                                      @RequestParam(required = false, value = "queryDeptId") @ApiParam("筛选组织id") Long queryDeptId,
                                                      @RequestParam(required = false, value = "tagName") @ApiParam("标签名称搜索") String tagName,
                                                      @RequestParam(required = false, value = "tagIncludeFlag") @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeFlag,
                                                      @RequestParam(required = false, value = "businessDeptIds") @ApiParam("业务公司id") String businessDeptIds,
                                                      @RequestParam(required = false, value = "firstPeriodStart") @ApiParam("首个账期开始") Integer firstPeriodStart,
                                                      @RequestParam(required = false, value = "firstPeriodEnd") @ApiParam("首个账期结束") Integer firstPeriodEnd) {
        return Result.ok(newCustomerInfoService.selectNewCustomerTransferList(deptId, pageNum, pageSize, keyWord, status, taxType, queryDeptId, tagName, tagIncludeFlag, businessDeptIds, firstPeriodStart, firstPeriodEnd));
    }

    @PostMapping("/newCustomerTransferListExport")
    @ApiOperation("导出新户流转列表")
    @RequiresPermissions("customer:newCustomerTransfer:export")
    public void newCustomerTransferListExport(@RequestHeader("deptId") Long deptId,
                                              @RequestParam(defaultValue = "1") int pageNum,
                                              @RequestParam(defaultValue = "10") int pageSize,
                                              @RequestParam(required = false, value = "keyWord") @ApiParam("关键词搜索") String keyWord,
                                              @RequestParam(required = false, value = "status") @ApiParam("流转状态， 0-待完善, 1-待提交, 2-待重提，3-待流转, 4-已流转") Integer status,
                                              @RequestParam(required = false, value = "taxType") @ApiParam("纳税人性质，1-小规模，2-一般纳税人") Integer taxType,
                                              @RequestParam(required = false, value = "queryDeptId") @ApiParam("筛选组织id") Long queryDeptId,
                                              @RequestParam(required = false, value = "tagName") @ApiParam("标签名称搜索") String tagName,
                                              @RequestParam(required = false, value = "tagIncludeFlag") @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeFlag,
                                              @RequestParam(required = false, value = "businessDeptIds") @ApiParam("业务公司id") String businessDeptIds,
                                              @RequestParam(required = false, value = "firstPeriodStart") @ApiParam("首个账期开始") Integer firstPeriodStart,
                                              @RequestParam(required = false, value = "firstPeriodEnd") @ApiParam("首个账期结束") Integer firstPeriodEnd,
                                              HttpServletResponse response) {
        ExcelUtil<NewCustomerTransferDTO> util = new ExcelUtil<>(NewCustomerTransferDTO.class);
        util.exportExcel(response, newCustomerInfoService.selectNewCustomerTransferList(deptId, 1, -1, keyWord, status, taxType, queryDeptId, tagName, tagIncludeFlag, businessDeptIds, firstPeriodStart, firstPeriodEnd).getRecords(), "新户流转列表");
    }

    @PostMapping("/nextCheck")
    @ApiOperation("下一步校验")
    public Result<NextCheckDTO> nextCheck(@RequestHeader("deptId") Long deptId,
                                          @RequestBody NewCustomerCreateVO vo) {
        return Result.ok(newCustomerInfoService.nextCheck(deptId, vo));
    }

    @PostMapping("/getEndCustomerServiceInfo")
    @ApiOperation("获取最近结束服务的银行账号、系统账号、税种列表")
    public Result<NewCustomerOtherInfoDTO> getEndCustomerServiceInfo(@RequestHeader("deptId") Long deptId,
                                          @RequestBody NewCustomerCreateVO vo) {
        return Result.ok(newCustomerInfoService.getEndCustomerServiceInfo(deptId, vo));
    }

    @PostMapping("/createNewCustomerBaseInfo")
    @ApiOperation("新增流转-基础信息，返回新户流转id")
    @RequiresPermissions("customer:newCustomerTransfer:create")
    public Result<Long> createNewCustomerBaseInfo(@RequestHeader("deptId") Long deptId,
                                            @RequestBody @Valid NewCustomerCreateVO vo) {
        return Result.ok(newCustomerInfoService.createNewCustomerBaseInfo(deptId, vo));
    }

    @PostMapping("/modifyNewCustomerBaseInfo")
    @ApiOperation("编辑流转-基础信息，返回新户流转id")
    @RequiresPermissions("customer:newCustomerTransfer:edit")
    public Result<Long> modifyNewCustomerBaseInfo(@RequestHeader("deptId") Long deptId,
                                                  @RequestBody @Valid NewCustomerCreateVO vo) {
        return Result.ok(newCustomerInfoService.modifyNewCustomerBaseInfo(deptId, vo));
    }

    @GetMapping("/getNewCustomerTransferBankAccountList")
    @ApiOperation("获取新户流转服务银行卡列表")
    public Result<List<NewCustomerTransferBankAccountDTO>> getNewCustomerTransferBankAccountList(@RequestParam("id") @ApiParam("新户流转id") Long id) {
        return Result.ok(newCustomerInfoService.getNewCustomerTransferBankAccountList(id));
    }

    @PostMapping("/deleteNewCustomerTransferBankAccount")
    @ApiOperation("删除新户流转银行卡信息，单个删除传id")
    public Result deleteNewCustomerTransferBankAccount(@RequestBody CommonIdVO vo) {
        newCustomerInfoService.deleteNewCustomerTransferBankAccount(vo);
        return Result.ok();
    }

    @PostMapping("/addNewCustomerTransferBankAccount")
    @ApiOperation("新增新户流转银行卡信息")
    public Result addNewCustomerTransferBankAccount(@RequestBody NewCustomerTransferBankAccountDTO vo) {
        newCustomerInfoService.addNewCustomerTransferBankAccount(vo);
        return Result.ok();
    }

    @PostMapping("/modifyNewCustomerTransferAccount")
    @ApiOperation("编辑新户流转银行卡信息")
    public Result modifyCustomerServiceBankAccount(@RequestBody NewCustomerTransferBankAccountDTO vo) {
        newCustomerInfoService.modifyNewCustomerTransferAccount(vo);
        return Result.ok();
    }

    @GetMapping(value = "/getNewCustomerTransferSysAccount")
    @ApiOperation(value = "获取新户流转系统账号列表")
    public Result<List<NewCustomerTransferSysAccountDTO>> getNewCustomerTransferSysAccount(@RequestParam("id") @ApiParam("新户流转id") Long id) {
        return Result.ok(newCustomerInfoService.getNewCustomerTransferSysAccount(id));
    }

    @PostMapping(value = "/addNewCustomerTransferSysAccount")
    @ApiOperation(value = "创建新户流转系统账号")
    public Result addNewCustomerTransferSysAccount(@RequestBody NewCustomerTransferSysAccountDTO dto) {
        newCustomerInfoService.addNewCustomerTransferSysAccount(dto);
        return Result.ok();
    }

    @PostMapping(value = "/modifyNewCustomerTransferSysAccount")
    @ApiOperation(value = "编辑新户流转系统账号")
    public Result modifyNewCustomerTransferSysAccount(@RequestBody NewCustomerTransferSysAccountDTO dto) {
        newCustomerInfoService.modifyNewCustomerTransferSysAccount(dto);
        return Result.ok();
    }

    @PostMapping(value = "/deleteNewCustomerTransferSysAccount")
    @ApiOperation(value = "删除新户流转系统账号，单个删除，传id")
    public Result deleteNewCustomerTransferSysAccount(@RequestBody CommonIdVO vo) {
        newCustomerInfoService.deleteNewCustomerTransferSysAccount(vo);
        return Result.ok();
    }

    @GetMapping(value = "/detailInfo")
    @ApiOperation(value = "新户流转详情")
    public Result<NewCustomerTransferDetailInfoDTO> detailInfo(@RequestParam("id") @ApiParam("新户流转id") Long id,
                                                               @RequestHeader("deptId") Long deptId) {
        return Result.ok(newCustomerInfoService.detailInfo(id, deptId));
    }

    @PostMapping(value = "/saveCustomerInfo")
    @ApiOperation(value = "保存")
    public Result<Long> saveCustomerInfo(@RequestBody NewCustomerTransferDetailInfoDTO vo,
                                         @RequestHeader("deptId") Long deptId) {
        return Result.ok(newCustomerInfoService.saveCustomerInfo(vo, deptId));
    }

    @PostMapping(value = "/submitCustomerInfo")
    @ApiOperation(value = "提交(新增流转和编辑流转弹窗中点提交)")
    @RequiresPermissions("customer:newCustomerTransfer:submit")
    public Result<Long> submitCustomerInfo(@RequestBody NewCustomerTransferDetailInfoDTO vo,
                                           @RequestHeader("deptId") Long deptId) {
        Long result = newCustomerInfoService.submitCustomerInfo(vo, deptId);
        if (Objects.equals(result, -1L)) {
            return Result.fail("信息未完善，不可提交");
        }
        return Result.ok(result);
    }

    @PostMapping("/delete")
    @ApiOperation("批量删除，传ids")
    @RequiresPermissions("customer:newCustomerTransfer:delete")
    public Result<CommonOperateResultDTO> deleteNewCustomerTransfer(@RequestBody CommonIdVO vo) {
        return Result.ok(newCustomerInfoService.deleteNewCustomerTransfer(vo.getIds()));
    }

    @PostMapping("/submit")
    @ApiOperation("批量提交，传ids")
    public Result<CommonOperateResultDTO> submitNewCustomerTransfer(@RequestBody CommonIdVO vo) {
        return Result.ok(newCustomerInfoService.submitNewCustomerTransfer(vo.getIds()));
    }

    @PostMapping("/reBack")
    @ApiOperation("批量退回，传ids")
    @RequiresPermissions("customer:newCustomerTransfer:reBack")
    public Result<CommonOperateResultDTO> reBackNewCustomerTransfer(@RequestBody CommonIdVO vo) {
        return Result.ok(newCustomerInfoService.reBackNewCustomerTransfer(vo.getIds()));
    }

    @GetMapping("/serviceNumberCheckRepeat")
    @ApiOperation("档案编号重复校验")
    public Result<ServiceNumberRepeatCheckResultDTO> serviceNumberCheckRepeat(@RequestHeader("deptId") Long deptId,
                                                                              @RequestParam("serviceNumber") @ApiParam("档案编号") String serviceNumber) {
        return Result.ok(newCustomerInfoService.serviceNumberCheckRepeat(deptId, serviceNumber));
    }

    @PostMapping("/singleTransfer")
    @ApiOperation("单个流转")
    @RequiresPermissions("customer:newCustomerTransfer:transfer")
    public Result singleTransfer(@RequestHeader("deptId") Long deptId,
                           @RequestBody NewCustomerTransferVO vo) {
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("lock:customer:transfer:singleTransfer:" + vo.getId(), userId.toString(), 5L)) {
            try {
                newCustomerInfoService.singleTransfer(deptId, vo);
                return Result.ok();
            } catch (ServiceException e) {
                return Result.fail(e.getMessage());
            } finally {
                redisService.unlock("lock:customer:transfer:singleTransfer:" + vo.getId(), userId.toString());
            }
        } else {
            return Result.fail("请勿重复提交");
        }
    }

    @PostMapping("/downloadWaitTransferData")
    @ApiOperation("下载待流转数据")
    @RequiresPermissions("customer:newCustomerTransfer:transfer")
    public void downloadWaitTransferData(@RequestHeader("deptId") Long deptId,
                                         HttpServletResponse response) {
        ExcelUtil<NewCustomerWaitTransferDTO> util = new ExcelUtil<>(NewCustomerWaitTransferDTO.class);
        List<NewCustomerTransferDTO> records = newCustomerInfoService.selectNewCustomerTransferList(deptId, 1, -1, null, NewCustomerTransferStatus.UN_TRANSFER.getCode(), null, null, null, null, null, null, null).getRecords();
        List<NewCustomerWaitTransferDTO> data = records.stream().map(item -> {
            NewCustomerWaitTransferDTO dto = new NewCustomerWaitTransferDTO();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());
        util.exportExcel(response, data, "待流转列表");
    }

    @PostMapping("/checkFile")
    @ApiOperation("校验文件内容")
    @RequiresPermissions("customer:newCustomerTransfer:transfer")
    public Result<BatchTransferCheckResultDTO> checkFile(@RequestParam("file") MultipartFile file) {
        return Result.ok(newCustomerInfoService.checkFile(file));
    }

    @PostMapping("/downloadErrorData")
    @ApiOperation("下载校验异常数据")
    @RequiresPermissions("customer:newCustomerTransfer:transfer")
    public void downloadErrorData(HttpServletResponse response,
                                  @RequestParam("batchNo") String batchNo) {
        newCustomerInfoService.downloadErrorData(batchNo, response);
    }

    @PostMapping("/downloadExistServiceNumberData")
    @ApiOperation("下载档案编号已存在数据")
    @RequiresPermissions("customer:newCustomerTransfer:transfer")
    public void downloadExistServiceNumberData(HttpServletResponse response,
                                  @RequestParam("batchNo") String batchNo) {
        newCustomerInfoService.downloadExistServiceNumberData(batchNo, response);
    }

    @PostMapping("/confirmBatchTransfer/{batchNo}/{accountingTopDeptId}")
    @ApiOperation("确认批量流转")
    @RequiresPermissions("customer:newCustomerTransfer:transfer")
    public Result confirmBatchTransfer(@PathVariable @ApiParam("批次号") String batchNo, @PathVariable @ApiParam("会计区域id") Long accountingTopDeptId,
                                       @RequestHeader("deptId") Long deptId) {
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("lock:customer:transfer:batchTransfer:" + batchNo, userId.toString(), 500L)) {
            try {
                newCustomerInfoService.confirmBatchTransfer(batchNo, accountingTopDeptId, deptId);
                return Result.ok();
            } catch (ServiceException e) {
                return Result.fail(e.getMessage());
            } finally {
                redisService.unlock("lock:customer:transfer:batchTransfer:" + batchNo, userId.toString());
            }
        } else {
            return Result.fail("请勿重复操作");
        }
    }
}
