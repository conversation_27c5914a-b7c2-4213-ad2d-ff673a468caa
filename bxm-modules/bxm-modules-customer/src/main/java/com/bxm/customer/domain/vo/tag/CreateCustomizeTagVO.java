package com.bxm.customer.domain.vo.tag;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateCustomizeTagVO {

    @ApiModelProperty("标签名")
    @NotEmpty(message = "标签名不能为空")
    private String tagName;

}
