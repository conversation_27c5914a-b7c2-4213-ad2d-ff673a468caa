package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePersonDetailDTO {

    @ApiModelProperty("数据最后更新时间")
    private String dataUpdateTime;

    @ApiModelProperty("数据总数")
    private Integer dataCount;

    @ApiModelProperty("人员明细列表（只返回5条，给前端显示）")
    private List<CustomerServicePersonDetailListDTO> personDetailList;

    @ApiModelProperty(hidden = true)
    private List<CustomerServicePersonDetailListDTO> allPersonDetailList;
}
