package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.enums.quality.QualityCheckingType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.CommonExcelUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.dto.CustomerServiceCountV2DTO;
import com.bxm.customer.domain.dto.CustomerServiceWaitItemDTO;
import com.bxm.customer.domain.dto.CustomerServiceWorkbenchV2DTO;
import com.bxm.customer.domain.dto.accoutingCashier.*;
import com.bxm.customer.domain.dto.workBench.*;
import com.bxm.customer.domain.dto.workBench.QualityCheckingStatisticDTO;
import com.bxm.customer.domain.dto.workBench.QualityExceptionMiniListDTO;
import com.bxm.customer.domain.dto.workBench.SyncItemSearchDTO;
import com.bxm.customer.domain.dto.workBench.QualityCheckingStatisticDTO;
import com.bxm.customer.domain.dto.workBench.QualityExceptionMiniListDTO;
import com.bxm.customer.domain.vo.accoutingCashier.AccountingCashierMiniListSearchVO;
import com.bxm.customer.domain.vo.workBench.CustomerServicePeriodMonthMiniListSearchVO;
import com.bxm.customer.domain.vo.workBench.CustomerServicePeriodMonthNoAccountingNoAdvisorVO;
import com.bxm.customer.domain.vo.workBench.InAccountDocHandoverStatisticVO;
import com.bxm.customer.domain.vo.workBench.SyncItemSearchVO;
import com.bxm.customer.domain.vo.workOrder.QualityExceptionMiniListSearchVO;
import com.bxm.customer.service.*;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/workBench")
@Api(tags = "工作台")
public class WorkBenchController {

    @Autowired
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;

    @Autowired
    private IBorrowOrderService borrowOrderService;

    @Autowired
    private WorkBenchService workBenchService;

    @Autowired
    private ExportService exportService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @GetMapping("/customerServiceComprehensiveStatistic")
    @ApiOperation("工作台综合待办数据统计")
    public Result<CustomerServiceWorkbenchV2DTO> customerServiceComprehensiveStatistic(@RequestHeader("deptId") Long deptId) {
        return Result.ok(workBenchService.customerServiceComprehensiveStatistic(deptId, SecurityUtils.getUserId()));
    }

    @GetMapping("/periodNoAccountingNoAdvisorPageList")
    @ApiOperation("账期无会计、账期无顾问miniList")
    public Result<IPage<CustomerServicePeriodMonthSimpleDTO>> periodNoAccountingNoAdvisorPageList(@RequestHeader("deptId") Long deptId,
                                                                                                  CustomerServicePeriodMonthNoAccountingNoAdvisorVO vo) {
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        return Result.ok(workBenchService.periodNoAccountingNoAdvisorPageList(deptId, vo));
    }

    @PostMapping("/periodNoAccountingNoAdvisorExportAndUpload")
    @ApiOperation("账期无会计、账期无顾问miniList导出（异步导出）")
    public Result periodNoAccountingNoAdvisorExportAndUpload(@RequestHeader("deptId") Long deptId,
                                                             CustomerServicePeriodMonthNoAccountingNoAdvisorVO vo) {
        String title = (vo.getType() == 1 ? "账期无会计" : "账期无顾问") + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        if (vo.getType() == 1) {
            exportService.exportCopyAsync(
                    title,
                    vo,
                    deptId,
                    workBenchService::periodNoAccountingNoAdvisorPageList,
                    DownloadType.PERIOD_NO_ACCOUNTING_ADVISOR,
                    CustomerServicePeriodMonthNoAccountingDTO.class,
                    downloadRecordService
            );
        } else {
            exportService.exportCopyAsync(
                    title,
                    vo,
                    deptId,
                    workBenchService::periodNoAccountingNoAdvisorPageList,
                    DownloadType.PERIOD_NO_ACCOUNTING_ADVISOR,
                    CustomerServicePeriodMonthNoAdvisorDTO.class,
                    downloadRecordService
            );
        }
        return Result.ok();
    }

    @GetMapping("/customerServiceWaitDispatchList")
    @ApiOperation("客户会计待分派，重启待分派，顾问待分派列表")
    public Result<IPage<CustomerServiceWaitItemDTO>> customerServiceWaitDispatchList(@RequestParam("itemType") @ApiParam("待处理类型，1-会计待分派，2-重启待分派，3-顾问待分派") Integer itemType,
                                                                                 @RequestParam(value = "keyWord", required = false) @ApiParam("关键字搜索") String keyWord,
                                                                                 @RequestHeader("deptId") Long deptId,
                                                                                 @RequestParam(value = "tagName", required = false) @ApiParam("标签名称") String tagName,
                                                                                 @RequestParam(value = "tagIncludeFlag", required = false) @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeFlag,
                                                                                 @RequestParam(value = "taxType", required = false) @ApiParam("纳税人性质，1-小规模，2-一般纳税人") Integer taxType,
                                                                                 @RequestParam("pageNum") Integer pageNum,
                                                                                 @RequestParam("pageSize") Integer pageSize) {
        return Result.ok(workBenchService.customerServiceWaitDispatchList(itemType, keyWord, deptId, tagName, tagIncludeFlag, taxType, pageNum, pageSize));
    }

    @GetMapping("/docHandoverStatistic")
    @ApiOperation("材料交接工作台数据统计")
    @RequiresPermissions("customer:workBench:docHandoverStatistic")
    public Result<DocHandoverWorkBenchDTO> docHandoverStatistic(@RequestHeader("deptId") Long deptId) {
        return Result.ok(customerServiceDocHandoverService.docHandoverStatistic(deptId));
    }

    @GetMapping("/borrowOrderStatistic")
    @ApiOperation("材料借阅工作台数据统计")
    public Result<BorrowOrderWorkBenchDTO> borrowOrderStatistic(@RequestHeader("deptId") Long deptId) {
        return Result.ok(borrowOrderService.borrowOrderStatistic(deptId));
    }

    @GetMapping("/inAccountMiniList")
    @ApiOperation("入账/结账miniList")
    public Result<IPage<InAccountMiniListDTO>> inAccountMiniList(@RequestHeader("deptId") Long deptId,
                                                                 @RequestParam(value = "queryDeptId", required = false) @ApiParam("组织范围筛选") Long queryDeptId,
                                                                 @RequestParam(value = "deptIds", required = false) @ApiParam("组织选择（多选）") String deptIds,
                                                                 @RequestParam(value = "customerName", required = false) @ApiParam("客户名") String customerName,
                                                                 @RequestParam(value = "miniListType") @ApiParam("类型，1-入账miniList，2-结账miniList，3-未入账miniList，4-未结账miniList") Integer miniListType,
                                                                 @RequestParam(value = "period", required = false) @ApiParam("账期，yyyyMM") Integer period,
                                                                 @RequestParam(value = "statisticTaxType", required = false) @ApiParam("账期类型，1-小规模，2-一般纳税人，3-0申报") Integer statisticTaxType,
                                                                 @RequestParam(value = "pageNum", defaultValue = "1") @ApiParam("当前页") Integer pageNum,
                                                                 @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam("每页条数") Integer pageSize) {
        return Result.ok(workBenchService.inAccountMiniList(deptId, deptIds, queryDeptId, customerName, miniListType, period, statisticTaxType, pageNum, pageSize));
    }

    @PostMapping("/inAccountMiniListExport")
    @ApiOperation("入账/结账miniList导出（同步导出）")
    public void inAccountMiniListExport(HttpServletResponse response, @RequestHeader("deptId") Long deptId,
                                                                 @RequestParam(value = "queryDeptId", required = false) @ApiParam("组织范围筛选") Long queryDeptId,
                                                                 @RequestParam(value = "deptIds", required = false) @ApiParam("组织选择（多选）") String deptIds,
                                                                 @RequestParam(value = "customerName", required = false) @ApiParam("客户名") String customerName,
                                                                 @RequestParam(value = "miniListType") @ApiParam("类型，1-入账miniList，2-结账miniList，3-未入账miniList，4-未结账miniList") Integer miniListType,
                                                                 @RequestParam(value = "period", required = false) @ApiParam("账期，yyyyMM") Integer period,
                                                                @RequestParam(value = "statisticTaxType", required = false) @ApiParam("账期类型，1-小规模，2-一般纳税人，3-0申报") Integer statisticTaxType) {
        List<InAccountMiniListDTO> export = Lists.newArrayList();
        Integer pageNum = 1;
        Integer pageSize = 5000;
        while (true) {
            List<InAccountMiniListDTO> l = workBenchService.inAccountMiniList(deptId, deptIds, queryDeptId, customerName, miniListType, period, statisticTaxType, pageNum, pageSize).getRecords();
            if (!ObjectUtils.isEmpty(l)) {
                export.addAll(l);
                pageNum++;
            } else {
                break;
            }
        }
        ExcelUtil<InAccountMiniListDTO> util = new ExcelUtil<>(InAccountMiniListDTO.class);
        util.exportExcel(response, export, miniListType == 1 ? "未入账账期列表" : "未结账账期列表");
    }

    @GetMapping("/inAccountDocHandoverStatistic")
    @ApiOperation("入账统计表(点开入账和结账的miniList调用接口/bxmCustomer/workBench/inAccountMiniList，导出miniList调用接口/bxmCustomer/workBench/inAccountMiniListExport)")
    public Result<List<InAccountDocHandoverStatisticDTO>> inAccountDocHandoverStatistic(InAccountDocHandoverStatisticVO vo,
                                                                                           @RequestHeader("deptId") Long deptId) {
        return Result.ok(workBenchService.inAccountDocHandoverStatistic(vo, deptId));
    }

    @PostMapping("/inAccountDocHandoverStatisticExport")
    @ApiOperation("入账统计表-导出")
    public void inAccountDocHandoverStatisticExport(HttpServletResponse response,
                                                    InAccountDocHandoverStatisticVO vo,
                                                    @RequestHeader("deptId") Long deptId) {
        List<InAccountDocHandoverStatisticDTO> statistic = workBenchService.inAccountDocHandoverStatistic(vo, deptId);
        if (vo.getStatisticType() == 1) {
            ExcelUtil<InAccountDocHandoverRateStatisticExportDTO> util = new ExcelUtil<>(InAccountDocHandoverRateStatisticExportDTO.class);
            util.exportExcel(response, statistic.stream().map(s -> {
                InAccountDocHandoverRateStatisticExportDTO dto = new InAccountDocHandoverRateStatisticExportDTO();
                dto.setPeriod(s.getPeriod());
                dto.setServicePeriodCount(s.getServicePeriodCount());
                dto.setInAccountRate(StringUtils.isEmpty(s.getInAccountRate()) ? "-" : s.getInAccountRate());
                dto.setEndAccountRate(StringUtils.isEmpty(s.getEndAccountRate()) ? "-" : s.getEndAccountRate());
                return dto;
            }).collect(Collectors.toList()), "入账统计表");
        } else {
            ExcelUtil<InAccountDocHandoverCountStatisticExportDTO> util = new ExcelUtil<>(InAccountDocHandoverCountStatisticExportDTO.class);
            util.exportExcel(response, statistic.stream().map(s -> {
                InAccountDocHandoverCountStatisticExportDTO dto = new InAccountDocHandoverCountStatisticExportDTO();
                dto.setPeriod(s.getPeriod());
                dto.setNotInAccountCount(Objects.isNull(s.getNotInAccountCount()) ? "-" : s.getNotInAccountCount().toString());
                dto.setNotEndAccountCount(Objects.isNull(s.getNotEndAccountCount()) ? "-" : s.getNotEndAccountCount().toString());
                return dto;
            }).collect(Collectors.toList()), "入账统计表");
        }
    }

    @GetMapping("/managerStatistic")
    @ApiOperation("经理/主管统计表")
    public Result<List<ManagerStatisticDTO>> managerStatistic(@RequestParam("statisticType") @ApiParam("统计类型，1-顾问经理/主管统计，2-会计经理/主管统计") Integer statisticType,
                                                              @RequestHeader("deptId") Long deptId) {
        return Result.ok(workBenchService.managerStatistic(statisticType, deptId));
    }

    @PostMapping("/managerStatisticExport")
    @ApiOperation("经理/主管统计表-导出")
    public void managerStatisticExport(HttpServletResponse response,
            @RequestParam("statisticType") @ApiParam("统计类型，1-顾问经理/主管统计，2-会计经理/主管统计") Integer statisticType,
                                                              @RequestHeader("deptId") Long deptId) {
        List<ManagerStatisticDTO> records = workBenchService.managerStatistic(statisticType, deptId);
        Long totalPreValidCount = 0L;
        Long totalPreRestartCount = 0L;
        Long totalPreEndCount = 0L;
        Long totalPreNewCount = 0L;
        Long totalThisValidCount = 0L;
        for (ManagerStatisticDTO record : records) {
            totalPreValidCount += record.getPreValidCount();
            totalPreRestartCount += record.getPreRestartCount();
            totalPreEndCount += record.getPreEndCount();
            totalPreNewCount += record.getPreNewCount();
            totalThisValidCount += record.getThisValidCount();
        }
        records.add(ManagerStatisticDTO.builder()
                        .deptInfo("合计")
                        .preValidCount(totalPreValidCount)
                        .preRestartCount(totalPreRestartCount)
                        .preEndCount(totalPreEndCount)
                        .preNewCount(totalPreNewCount)
                        .thisValidCount(totalThisValidCount)
                .build());
        ExcelUtil<ManagerStatisticDTO> util = new ExcelUtil<>(ManagerStatisticDTO.class);
        try {
            LocalDate now = LocalDate.now();
            Integer thisMonth = now.getMonthValue();
            Integer preMonth = now.minusMonths(1).getMonthValue();
            Field preValidCountField = ManagerStatisticDTO.class.getDeclaredField("preValidCount");
            Excel preValidCountExcelAnnotation = preValidCountField.getAnnotation(Excel.class);
            CommonExcelUtils.setAnnotationValue(preValidCountExcelAnnotation, "name", preMonth + "月有效户");
            Field preRestartCountField = ManagerStatisticDTO.class.getDeclaredField("preRestartCount");
            Excel preRestartCountExcelAnnotation = preRestartCountField.getAnnotation(Excel.class);
            CommonExcelUtils.setAnnotationValue(preRestartCountExcelAnnotation, "name", preMonth + "月重启");
            Field preEndCountField = ManagerStatisticDTO.class.getDeclaredField("preEndCount");
            Excel preEndCountExcelAnnotation = preEndCountField.getAnnotation(Excel.class);
            CommonExcelUtils.setAnnotationValue(preEndCountExcelAnnotation, "name", preMonth + "月移出");
            Field preNewCountField = ManagerStatisticDTO.class.getDeclaredField("preNewCount");
            Excel preNewCountExcelAnnotation = preNewCountField.getAnnotation(Excel.class);
            CommonExcelUtils.setAnnotationValue(preNewCountExcelAnnotation, "name", preMonth + "月新户");
            Field thisValidCountField = ManagerStatisticDTO.class.getDeclaredField("thisValidCount");
            Excel thisValidCountExcelAnnotation = thisValidCountField.getAnnotation(Excel.class);
            CommonExcelUtils.setAnnotationValue(thisValidCountExcelAnnotation, "name", thisMonth + "月有效户");
        } catch (Exception e) {
            throw new ServiceException("下载失败");
        }
        util.exportExcel(response, records, statisticType == 1 ? "顾问经理统计表" : "会计经理统计表");
    }

    @GetMapping("/customerServiceStatistic")
    @ApiOperation("客户统计表")
    public Result<CustomerServiceCountV2DTO> customerServiceStatistic(@RequestParam("statisticType") @ApiParam("统计类型，1-顾问客户统计，2-会计客户统计") Integer statisticType,
                                                                      @RequestHeader("deptId") Long headerDeptId, @RequestParam(value = "deptId", required = false) @ApiParam("选择的部门") Long deptId,
                                                                      @RequestParam(value = "deptIds", required = false) @ApiParam("选择的部门") String deptIds) {
        return Result.ok(workBenchService.customerServiceStatistic(statisticType, headerDeptId, deptId, deptIds));
    }

    @GetMapping("/customerServiceStatisticMiniList")
    @ApiOperation("客户统计表miniList")
    public Result<IPage<CustomerServicePeriodMonthMiniListDTO>> customerServiceStatisticMiniList(CustomerServicePeriodMonthMiniListSearchVO vo,
                                                                                                 @RequestHeader("deptId") Long headerDeptId) {
        return Result.ok(workBenchService.customerServiceStatisticMiniList(vo, headerDeptId));
    }

    @PostMapping("/customerServiceStatisticMiniListExport")
    @ApiOperation("客户统计表miniList-导出")
    public void customerServiceStatisticMiniListExport(CustomerServicePeriodMonthMiniListSearchVO vo,
                                                       @RequestHeader("deptId") Long headerDeptId,
                                                       HttpServletResponse response) throws Exception {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        ExcelUtil<CustomerServicePeriodMonthMiniListDTO> util = new ExcelUtil<>(CustomerServicePeriodMonthMiniListDTO.class);
        Integer preMonth = LocalDate.now().minusMonths(1L).getMonthValue();
        String fileName = preMonth + "月";
        if (vo.getType() == 1) {
            fileName += "有效户";
        } else if (vo.getType() == 2) {
            fileName += "重启";
        } else if (vo.getType() == 3) {
            fileName += "移出";
        } else if (vo.getType() == 4) {
            fileName += "新户";
        }
        if (vo.getType() == 1) {
            Field businessDeptNameField = CustomerServicePeriodMonthMiniListDTO.class.getDeclaredField("businessDeptName");
            Excel businessDeptNameFieldAnnotation = businessDeptNameField.getAnnotation(Excel.class);
            CommonExcelUtils.setAnnotationValue(businessDeptNameFieldAnnotation, "name", "账期业务公司");
        } else {
            Field businessDeptNameField = CustomerServicePeriodMonthMiniListDTO.class.getDeclaredField("businessDeptName");
            Excel businessDeptNameFieldAnnotation = businessDeptNameField.getAnnotation(Excel.class);
            CommonExcelUtils.setAnnotationValue(businessDeptNameFieldAnnotation, "name", "业务公司");
        }
        util.exportExcel(response, workBenchService.customerServiceStatisticMiniList(vo, headerDeptId).getRecords(), fileName);
    }

    @GetMapping("/materialDeliverStatistic")
    @ApiOperation("材料交接首页待办统计")
    public Result<MaterialDeliverStatisticDTO> materialDeliverStatistic(@RequestHeader("deptId") Long deptId) {
        return Result.ok(workBenchService.materialDeliverStatistic(deptId));
    }

    @GetMapping("/accountingCashierBankStatistic")
    @ApiOperation("银行流水首页待办统计")
    public Result<AccountingCashierBankStatisticDTO> accountingCashierBankStatistic(@RequestHeader("deptId") Long deptId,
                                                                                    @RequestParam(value = "queryDeptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                                                                    @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id") String deptIds) {
        return Result.ok(workBenchService.accountingCashierBankStatistic(deptId, queryDeptId, deptIds));
    }

    @GetMapping("/accountingCashierInAccountStatistic")
    @ApiOperation("入账首页待办统计")
    public Result<AccountingCashierInAccountStatisticDTO> accountingCashierInAccountStatistic(@RequestHeader("deptId") Long deptId,
                                                                                    @RequestParam(value = "queryDeptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                                                                              @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id") String deptIds) {
        return Result.ok(workBenchService.accountingCashierInAccountStatistic(deptId, queryDeptId, deptIds));
    }

    @GetMapping("/accountingCashierChangeAccountStatistic")
    @ApiOperation("改账首页待办统计")
    public Result<AccountingCashierChangeAccountStatisticDTO> accountingCashierChangeAccountStatistic(@RequestHeader("deptId") Long deptId,
                                                                                    @RequestParam(value = "queryDeptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                                                                                      @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id") String deptIds) {
        return Result.ok(workBenchService.accountingCashierChangeAccountStatistic(deptId, queryDeptId, deptIds));
    }

    @GetMapping("/accountingCashierMiniList")
    @ApiOperation("账务miniList列表（顾问和会计下拉取数来源/bxmCustomer/select/getAccountingCashierMiniListDeptSelectList）")
    public Result<IPage<AccountingCashierMiniListDTO>> accountingCashierMiniList(@RequestHeader("deptId") Long deptId,
                                                                                 AccountingCashierMiniListSearchVO vo) {
        return Result.ok(workBenchService.accountingCashierMiniList(deptId, vo));
    }

    @PostMapping("/accountingCashierMiniListExport")
    @ApiOperation("导出账务miniList列表(同步导出)")
    public void accountingCashierMiniList(HttpServletResponse response, @RequestHeader("deptId") Long deptId,
                                          AccountingCashierMiniListSearchVO vo) {
        List<AccountingCashierMiniListDTO> export = Lists.newArrayList();
        Integer pageNum = 1;
        Integer pageSize = 5000;
        vo.setPageSize(pageSize);
        while (true) {
            vo.setPageNum(pageNum);
            List<AccountingCashierMiniListDTO> l = workBenchService.accountingCashierMiniList(deptId, vo).getRecords();
            if (!ObjectUtils.isEmpty(l)) {
                export.addAll(l);
                pageNum++;
            } else {
                break;
            }
        }
        if (Objects.equals(vo.getAccountingCashierType(), AccountingCashierType.FLOW.getCode())) {
            if (vo.getStatisticType() == 0) {
                ExcelUtil<AccountingCashierBankUnOpenExportDTO> util = new ExcelUtil<>(AccountingCashierBankUnOpenExportDTO.class);
                util.exportExcel(response, export.stream().map(row -> {
                    AccountingCashierBankUnOpenExportDTO dto = new AccountingCashierBankUnOpenExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    return dto;
                }).collect(Collectors.toList()), "银行流水-未开户");
            } else if (vo.getStatisticType() == 1) {
                ExcelUtil<AccountingCashierBankPartialMissAndWaitCreateDTO> util = new ExcelUtil<>(AccountingCashierBankPartialMissAndWaitCreateDTO.class);
                util.exportExcel(response, export.stream().map(row -> {
                    AccountingCashierBankPartialMissAndWaitCreateDTO dto = new AccountingCashierBankPartialMissAndWaitCreateDTO();
                    BeanUtils.copyProperties(row, dto);
                    return dto;
                }).collect(Collectors.toList()), "银行流水-银行部分缺");
            } else if (vo.getStatisticType() == 2 || vo.getStatisticType() == 9 || vo.getStatisticType() == 10 || vo.getStatisticType() == 11) {
                String title;
                if (vo.getStatisticType() == 2) {
                    title = "银行流水-待创建";
                } else if (vo.getStatisticType() == 9) {
                    title = "银行流水-待顾问创建";
                } else if (vo.getStatisticType() == 10) {
                    title = "银行流水-待回单中心创建";
                } else {
                    title = "银行流水-银企待创建";
                }
                ExcelUtil<AccountingCashierBankWaitCreateDTO> util = new ExcelUtil<>(AccountingCashierBankWaitCreateDTO.class);
                util.exportExcel(response, export.stream().map(row -> {
                    AccountingCashierBankWaitCreateDTO dto = new AccountingCashierBankWaitCreateDTO();
                    BeanUtils.copyProperties(row, dto);
                    return dto;
                }).collect(Collectors.toList()), title);
            } else {
                String deliverStatus = getDeliverStatusByStatisticType(vo.getStatisticType());
                ExcelUtil<AccountingCashierBankDeliverExportDTO> util = new ExcelUtil<>(AccountingCashierBankDeliverExportDTO.class);
                util.exportExcel(response, export.stream().map(row -> {
                    AccountingCashierBankDeliverExportDTO dto = new AccountingCashierBankDeliverExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    return dto;
                }).collect(Collectors.toList()), "银行流水-" + deliverStatus);
            }
        } else if (Objects.equals(vo.getAccountingCashierType(), AccountingCashierType.INCOME.getCode())) {
            if (vo.getStatisticType() == 2) {
                ExcelUtil<AccountingCashierInAccountWaitCreateExportDTO> util = new ExcelUtil<>(AccountingCashierInAccountWaitCreateExportDTO.class);
                util.exportExcel(response, export.stream().map(row -> {
                    AccountingCashierInAccountWaitCreateExportDTO dto = new AccountingCashierInAccountWaitCreateExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    return dto;
                }).collect(Collectors.toList()), "入账-待创建");
            } else {
                String deliverStatus = getDeliverStatusByStatisticType(vo.getStatisticType());
                ExcelUtil<AccountingCashierAccountDeliverExportDTO> util = new ExcelUtil<>(AccountingCashierAccountDeliverExportDTO.class);
                util.exportExcel(response, export.stream().map(row -> {
                    AccountingCashierAccountDeliverExportDTO dto = new AccountingCashierAccountDeliverExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    return dto;
                }).collect(Collectors.toList()), "入账-" + deliverStatus);
            }
        } else {
            String deliverStatus = getDeliverStatusByStatisticType(vo.getStatisticType());
            ExcelUtil<AccountingCashierAccountDeliverExportDTO> util = new ExcelUtil<>(AccountingCashierAccountDeliverExportDTO.class);
            util.exportExcel(response, export.stream().map(row -> {
                AccountingCashierAccountDeliverExportDTO dto = new AccountingCashierAccountDeliverExportDTO();
                BeanUtils.copyProperties(row, dto);
                return dto;
            }).collect(Collectors.toList()), "改账-" + deliverStatus);
        }
    }

    @GetMapping("/syncItemPageList")
    @ApiOperation("待申报（按税种）/待扣款（按税种）列表")
    public Result<IPage<SyncItemSearchDTO>> syncItemPageList(SyncItemSearchVO vo,
                                                             @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setHeadDeptId(deptId);
        return Result.ok(workBenchService.syncItemPageList(deptId, vo));
    }

    @PostMapping("/syncItemExportAndUpload")
    @ApiOperation("待申报（按税种）/待扣款（按税种）导出（异步导出）")
    public Result syncItemExport(SyncItemSearchVO vo,
                               @RequestHeader("deptId") Long deptId) {
        String title = (vo.getType() == 1 ? "待申报（按税种）" : "待扣款（按税种）") + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setUserId(SecurityUtils.getUserId());
        vo.setHeadDeptId(deptId);

        if (vo.getType() == 1) {
            exportService.exportCopyAsync(
                    title,
                    vo,
                    deptId,
                    workBenchService::syncItemPageList,
                    DownloadType.SYNC_ITEM,
                    SyncItemReportDTO.class,
                    downloadRecordService
            );
        } else if (vo.getType() == 2) {
            exportService.exportCopyAsync(
                    title,
                    vo,
                    deptId,
                    workBenchService::syncItemPageList,
                    DownloadType.SYNC_ITEM,
                    SyncItemDeductionDTO.class,
                    downloadRecordService
            );
        }
        return Result.ok();
    }

    @GetMapping("/qualityCheckingStatistic")
    @ApiOperation("工作台-质检统计")
    public Result<QualityCheckingStatisticDTO> qualityCheckingStatistic(@RequestHeader("deptId") Long deptId,
                                                                        @RequestParam(value = "queryDeptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                                                        @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id（多选）") String deptIds) {
        return Result.ok(workBenchService.qualityCheckingStatistic(deptId, queryDeptId, deptIds));
    }

    @GetMapping("/qualityExceptionMiniList")
    @ApiOperation("工作台-质检miniList")
    public Result<IPage<QualityExceptionMiniListDTO>> qualityExceptionMiniList(QualityExceptionMiniListSearchVO vo,
                                                                               @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(workBenchService.qualityExceptionMiniList(deptId, vo));
    }

    @PostMapping("/qualityExceptionExportAndUpload")
    @ApiOperation("工作台-质检miniList导出（异步导出）")
    public Result qualityExceptionExport(QualityExceptionMiniListSearchVO vo,
                                         @RequestHeader("deptId") Long deptId) {
        String title = getQualityCheckingMiniListExportTitle(vo.getQualityCheckingType()) + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);

        exportService.exportAsync(
                title,
                vo,
                deptId,
                workBenchService::qualityExceptionMiniList,
                DownloadType.QUALITY_CHECKING_MINILIST,
                QualityExceptionMiniListDTO.class,
                downloadRecordService
        );
        return Result.ok();
    }

    @GetMapping("/valueAddedDeliverStatistic")
    @ApiOperation("工作台-增值交付统计")
    public Result<ValueAddedDeliverStatisticDTO> valueAddedDeliverStatistic(@RequestHeader("deptId") Long deptId,
                                                                              @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id（多选）") String deptIds) {
        return Result.ok(workBenchService.valueAddedDeliverStatistic(deptId, deptIds));
    }

    private String getQualityCheckingMiniListExportTitle(Integer type) {
        if (Objects.equals(QualityCheckingType.ACCOUNT_PROBLEM.getCode(), type) || Objects.equals(QualityCheckingType.RISK_WARNING.getCode(), type)) {
            return "异常（" + QualityCheckingType.getByCode(type).getName() + "）";
        }
        return "";
    }

    private String getDeliverStatusByStatisticType(Integer statisticType) {
        if (statisticType == 3) {
            return "待重提";
        } else if (statisticType == 4) {
            return "待交付";
        } else if (statisticType == 5) {
            return "异常";
        } else if (statisticType == 6) {
            return "有变更";
        } else if (statisticType == 7) {
            return "缺材料";
        } else if (statisticType == 8) {
            return "交付待提交";
        } else {
            return "";
        }
    }
}
