package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 新户流转银行账号对象 c_new_customer_bank_account
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("新户流转银行账号对象")
@Accessors(chain = true)
@TableName("c_new_customer_bank_account")
public class NewCustomerBankAccount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 银行名称 */
    @Excel(name = "银行名称")
    @TableField("bank_name")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /** 银行账号 */
    @Excel(name = "银行账号")
    @TableField("bank_account_number")
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNumber;

    /** 密码 */
    @Excel(name = "密码")
    @TableField("password")
    @ApiModelProperty(value = "密码")
    private String password;

    /** 手机号 */
    @Excel(name = "手机号")
    @TableField("phone_number")
    @ApiModelProperty(value = "手机号")
    private String phoneNumber;

    /** 开户时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "开户时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("account_open_date")
    @ApiModelProperty(value = "开户时间")
    private LocalDate accountOpenDate;

    /** 销户时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "销户时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("account_close_date")
    @ApiModelProperty(value = "销户时间")
    private LocalDate accountCloseDate;

    /** 回单卡账号 */
    @Excel(name = "回单卡账号")
    @TableField("receipt_account_number")
    private String receiptAccountNumber;

    /** 回单卡状态, 1-已托管, 2-未托管 */
    @Excel(name = "回单卡状态, 1-已托管, 2-未托管")
    @TableField("receipt_status")
    @ApiModelProperty(value = "回单卡状态, 1-已托管, 2-未托管")
    private Integer receiptStatus;

    /** 银企直连, 1-已开通, 2-未开通 */
    @Excel(name = "银企直连, 1-已开通, 2-未开通")
    @TableField("bank_direct")
    @ApiModelProperty(value = "银企直连, 1-已开通, 2-未开通")
    private Integer bankDirect;

    /** 备注说明 */
    @Excel(name = "备注说明")
    @TableField("remarks")
    @ApiModelProperty(value = "备注说明")
    private String remarks;

    @Excel(name = "开户行")
    @TableField("deposit_name")
    @ApiModelProperty(value = "开户行")
    private String depositName;

    @TableField("is_put_on_tax_record")
    @ApiModelProperty("是否税局备案，0-否，1-是")
    @Excel(name = "是否税局备案，0-否，1-是")
    private Integer isPutOnTaxRecord;

    @TableField("currency")
    @ApiModelProperty("币种，R-人民币，O-外币")
    @Excel(name = "币种，R-人民币，O-外币")
    private String currency;
}
