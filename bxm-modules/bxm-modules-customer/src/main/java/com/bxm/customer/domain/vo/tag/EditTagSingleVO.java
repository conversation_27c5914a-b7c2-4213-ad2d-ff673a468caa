package com.bxm.customer.domain.vo.tag;

import com.bxm.customer.domain.dto.tag.TagV2DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EditTagSingleVO {

    @ApiModelProperty("业务id")
    private Long businessId;

    @ApiModelProperty("业务类型，1-服务，2-账期")
    private Integer businessType;

    @ApiModelProperty("选择的系统标签")
    private List<TagV2DTO> systemTags;

    @ApiModelProperty("选择的集团标签")
    private List<TagV2DTO> groupTags;
}
