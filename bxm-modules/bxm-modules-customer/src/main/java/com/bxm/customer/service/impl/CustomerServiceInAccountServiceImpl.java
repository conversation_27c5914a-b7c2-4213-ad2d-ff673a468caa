package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.enums.docHandover.DocHandoverStatus;
import com.bxm.common.core.enums.docHandover.WholeLevel;
import com.bxm.common.core.enums.inAccount.InAccountDeliverResult;
import com.bxm.common.core.enums.inAccount.InAccountFileType;
import com.bxm.common.core.enums.inAccount.InAccountRpaExeResult;
import com.bxm.common.core.enums.inAccount.InAccountStatus;
import com.bxm.common.core.enums.repairAccount.RepairAccountDeliverStatus;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteCustomerInAccountMaxPeriodDTO;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.AccountingInfoSourceDTO;
import com.bxm.customer.domain.dto.AccountingTopInfoSourceDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.inAccount.*;
import com.bxm.customer.domain.vo.CommonIdsSearchVO;
import com.bxm.customer.domain.vo.CommonInAccountVO;
import com.bxm.customer.domain.vo.TagSearchVO;
import com.bxm.customer.domain.vo.inAccount.*;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.mapper.*;
import com.bxm.customer.properties.BusinessGroupProperties;
import com.bxm.customer.properties.SpecialTagProperties;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.bxm.thirdpart.api.domain.RpaInAccountVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 入账、入账交付Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Slf4j
@Service
public class CustomerServiceInAccountServiceImpl extends ServiceImpl<CustomerServiceInAccountMapper, CustomerServiceInAccount> implements ICustomerServiceInAccountService {
    private static final String TITLE_BASE = "入账交付单【%s】";

    @Autowired
    private CustomerServiceInAccountMapper customerServiceInAccountMapper;

    @Autowired
    private ICustomerServiceDocHandoverService iCustomerServiceDocHandoverService;

    @Autowired
    private CustomerServiceDocHandoverMapper customerServiceDocHandoverMapper;

    @Autowired
    private ICustomerServiceInAccountFileService iCustomerServiceInAccountFileService;

    @Autowired
    private ICustomerServicePeriodMonthService iCustomerServicePeriodMonthService;

    @Autowired
    private CCustomerServiceMapper cCustomerServiceMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteThirdpartService remoteThirdpartService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private CustomerServiceRepairAccountMapper customerServiceRepairAccountMapper;

    @Autowired
    private ICustomerServiceInAccountUpdateRecordService customerServiceInAccountUpdateRecordService;

    @Autowired
    private BusinessGroupProperties businessGroupProperties;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    /**
     * 查询入账、入账交付
     *
     * @param id 入账、入账交付主键
     * @return 入账、入账交付
     */
    @Override
    public CustomerServiceInAccount selectCustomerServiceInAccountById(Long id) {
        return customerServiceInAccountMapper.selectCustomerServiceInAccountById(id);
    }

    /**
     * 查询入账、入账交付列表
     *
     * @param customerServiceInAccount 入账、入账交付
     * @return 入账、入账交付
     */
    @Override
    public List<CustomerServiceInAccount> selectCustomerServiceInAccountList(CustomerServiceInAccount customerServiceInAccount) {
        return customerServiceInAccountMapper.selectCustomerServiceInAccountList(customerServiceInAccount);
    }

    /**
     * 新增入账、入账交付
     *
     * @param customerServiceInAccount 入账、入账交付
     * @return 结果
     */
    @Override
    public int insertCustomerServiceInAccount(CustomerServiceInAccount customerServiceInAccount) {
        customerServiceInAccount.setCreateTime(DateUtils.getNowDate());
        return customerServiceInAccountMapper.insertCustomerServiceInAccount(customerServiceInAccount);
    }

    /**
     * 修改入账、入账交付
     *
     * @param customerServiceInAccount 入账、入账交付
     * @return 结果
     */
    @Override
    public int updateCustomerServiceInAccount(CustomerServiceInAccount customerServiceInAccount) {
        customerServiceInAccount.setUpdateTime(DateUtils.getNowDate());
        return customerServiceInAccountMapper.updateCustomerServiceInAccount(customerServiceInAccount);
    }

    /**
     * 批量删除入账、入账交付
     *
     * @param ids 需要删除的入账、入账交付主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceInAccountByIds(Long[] ids) {
        return customerServiceInAccountMapper.deleteCustomerServiceInAccountByIds(ids);
    }

    /**
     * 删除入账、入账交付信息
     *
     * @param id 入账、入账交付主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceInAccountById(Long id) {
        return customerServiceInAccountMapper.deleteCustomerServiceInAccountById(id);
    }

    @Override
    public IPage<InAccountDTO> inAccountList(Long deptId, InAccountVO vo) {
        Gson gson = new Gson();

        log.info("inAccountList deptId={} vo={}", deptId, gson.toJson(vo));

        IPage<InAccountDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());

        //参考了 CCustomerServiceServiceImpl customerServicePeriodMonthList
        //UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        Long userId = vo.getUserId() == null ? SecurityUtils.getUserId() : vo.getUserId();
        UserDeptDTO userDept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();
        log.info("inAccountList userDept={} vo={}", gson.toJson(userDept), gson.toJson(vo));
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return result;
        }

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
        log.info("inAccountList deptId={} userDept={} tagSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return result;
        }

        //处理，会计搜索
        CommonIdsSearchVO accountingSearchVO = iCustomerServiceDocHandoverService.accountingSearch(vo.getAccountingEmployee());
        log.info("inAccountList deptId={} userDept={} accountingSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(accountingSearchVO));
        if (accountingSearchVO.getNeedSearch() && accountingSearchVO.getFail()) {
            return result;
        }

        //处理，完整度搜索
        //CommonIdsSearchVO wholeLevelSearchVO = wholeLevelSearch(vo.getWholeLevel());
        CommonIdsSearchVO wholeLevelSearchVO = wholeLevelSearchV2(vo.getWholeLevel());
        log.info("inAccountList deptId={} userDept={} wholeLevelSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(wholeLevelSearchVO));
        if (wholeLevelSearchVO.getNeedSearch() && wholeLevelSearchVO.getFail()) {
            return result;
        }

        // add by lym
        // 部门搜索
        CommonIdsSearchVO deptSearchVO = iCustomerServiceDocHandoverService.deptSearch(vo.getQueryDeptId());
        log.info("inAccountList deptId={} userDept={} deptSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(deptSearchVO));
        if (deptSearchVO.getNeedSearch() && deptSearchVO.getFail()) {
            return result;
        }

        //合并
        CommonIdsSearchVO commonIdsSearchVO = mergeCommonIdsSearchData(Lists.newArrayList(accountingSearchVO, wholeLevelSearchVO, deptSearchVO));
        log.info("inAccountList deptId={} userDept={} commonIdsSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(commonIdsSearchVO));
        if (commonIdsSearchVO.getNeedSearch() && commonIdsSearchVO.getFail()) {
            return result;
        }

        List<Long> customerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            customerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(customerServiceIds)) {
                return result;
            }
        }

        //原始数据
        //List<InAccountDTO> source = customerServiceInAccountMapper.selectInAccountList(result, vo, tagSearchVO, commonIdsSearchVO);
        List<InAccountDTO> source = customerServiceInAccountMapper.selectInAccountListV2(result, vo, tagSearchVO, commonIdsSearchVO, userDept, customerServiceIds);
//        log.info("inAccountList deptId={} userDept={} source={}", deptId, gson.toJson(userDept), gson.toJson(source));

        //处理数据
        if (!ObjectUtils.isEmpty(source)) {
            List<Long> inAccountIds = source.stream().map(InAccountDTO::getId).distinct().collect(Collectors.toList());

            //拿到文件
            Map<Long, Map<Integer, List<CustomerServiceInAccountFile>>> filesMap = iCustomerServiceInAccountFileService.selectMapByFileType(inAccountIds, Lists.newArrayList(InAccountFileType.BASE, InAccountFileType.RPA));

            List<Long> customerServicePeriodMonthIds = source.stream().map(InAccountDTO::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());

            //批量获取会计
            Map<Long, List<AccountingInfoSourceDTO>> accountingInfoSourceMap = iCustomerServiceDocHandoverService.getAccountingInfoSource(
                    customerServicePeriodMonthIds
            );

            //批量获取会计区域
            Map<Long, List<AccountingTopInfoSourceDTO>> accountingTopInfoSourceMap = iCustomerServiceDocHandoverService.getAccountingTopInfoSource(
                    customerServicePeriodMonthIds
            );

            /*
             * 取账期对应的材料交接单做逻辑判断：
             * 无材料交接单：无材料
             * 有材料交接单且有至少一条是有缺失：有缺失
             * 有材料交接单且无有缺失且有至少一条是缺但齐：缺但齐
             * 不符合以上条件：完整
             * 状态不是“无材料”时，提供详情链接，点击打开月度材料详情抽屉
             */

            Map<Long, List<CustomerServiceDocHandover>> docHandoverMap = Maps.newHashMap();
            if (vo.getWholeLevel() == null) {
                //拿到这些入账交付单的对应账期的对应材料交接单
                docHandoverMap = iCustomerServiceDocHandoverService.getMapByPeriodMonthIds(customerServicePeriodMonthIds);
                //log.info("docHandoverMap {}", customerServicePeriodMonthIds.toString());
                //log.info("docHandoverMap {}", docHandoverMap.toString());
            }

            for (InAccountDTO row : source) {
                Integer wholeLevel;

                if (vo.getWholeLevel() == null) {
                    //wholeLevel = handleWholeLevel(docHandoverMap.get(row.getCustomerServicePeriodMonthId()));
                    wholeLevel = handleWholeLevelV2(docHandoverMap.get(row.getCustomerServicePeriodMonthId()));
                } else {
                    wholeLevel = vo.getWholeLevel();
                }
                List<AccountingInfoSourceDTO> accountingInfoSourceDTOS = accountingInfoSourceMap.get(row.getCustomerServicePeriodMonthId());
                List<AccountingTopInfoSourceDTO> accountingTopInfoSourceDTOS = accountingTopInfoSourceMap.get(row.getCustomerServicePeriodMonthId());
                AccountingTopInfoSourceDTO accountingTopInfoSourceDTO = ObjectUtils.isEmpty(accountingTopInfoSourceDTOS) ? null : accountingTopInfoSourceDTOS.get(0);

                List<CommonFileVO> files = iCustomerServiceInAccountFileService.covToCommonFileVO(
                        filesMap.getOrDefault(row.getId(), Maps.newHashMap()).get(InAccountFileType.BASE.getCode())
                );
                List<CommonFileVO> rpaFiles = iCustomerServiceInAccountFileService.covToCommonFileVO(
                        filesMap.getOrDefault(row.getId(), Maps.newHashMap()).get(InAccountFileType.RPA.getCode())
                );
                row.setCustomerServiceAccountingTopDeptName(Objects.isNull(accountingTopInfoSourceDTO) ? "" : accountingTopInfoSourceDTO.getCustomerServiceAccountingTopDeptName());
                row.setPeriodAccountingTopDeptName(Objects.isNull(accountingTopInfoSourceDTO) ? "" : accountingTopInfoSourceDTO.getPeriodAccountingTopDeptName());

                row.setAccountingEmployeeNameFull(
                        CustomerServiceDocHandoverServiceImpl.handleAccountingEmployeeNameFull(accountingInfoSourceMap.get(row.getCustomerServicePeriodMonthId()))
                );
                row.setAccountingDeptId(ObjectUtils.isEmpty(accountingInfoSourceDTOS) ? null : accountingInfoSourceDTOS.get(0).getAccountingDeptId());
                row.setStatusStr(InAccountStatus.getByCode(row.getStatus()).getName());
                row.setWholeLevel(wholeLevel);
                row.setWholeLevelStr(WholeLevel.getByCode(wholeLevel).getName());

                row.setPeriodStr(DateUtils.periodToYeaMonth(row.getPeriod()));

                row.setDeliverResultStr(row.getDeliverResult() == null ? null : InAccountDeliverResult.getByCode(row.getDeliverResult()).getName());
                row.setFiles(files);
                row.setFilesStr(files.size());
                row.setRpaExeResultStr(row.getRpaExeResult() == null ? null : InAccountRpaExeResult.getByCode(row.getRpaExeResult()).getName());
                row.setRpaFiles(rpaFiles);
                row.setRpaFilesStr(rpaFiles.size());
                row.setMajorIncomeTotalStr(row.getMajorIncomeTotal() == null ? "-" : row.getMajorIncomeTotal().stripTrailingZeros().toPlainString());
                row.setMajorCostTotalStr(row.getMajorCostTotal() == null ? "-" : row.getMajorCostTotal().stripTrailingZeros().toPlainString());
                row.setProfitTotalStr(row.getProfitTotal() == null ? "-" : row.getProfitTotal().stripTrailingZeros().toPlainString());
                row.setTaxReportSalaryTotalStr(row.getTaxReportSalaryTotal() == null ? "-" : row.getTaxReportSalaryTotal().stripTrailingZeros().toPlainString());
                row.setBankPaymentInputResultStr(Objects.isNull(row.getBankPaymentInputResult()) ? "" : BankPaymentInputResult.getByCode(row.getBankPaymentInputResult()).getName());
                row.setInAccountResultStr(Objects.isNull(row.getInAccountResult()) ? "" : InAccountResult.getByCode(row.getInAccountResult()).getName());
                row.setCustomerName(row.getPeriodCustomerName());
                row.setCreditCode(row.getPeriodCreditCode());
            }
        }

        //返回数据
        result.setRecords(source);

        return result;
    }

    @Override
    public InAccountUpdateShowDTO getInAccountUpdateShow(Long id) {
        CustomerServiceInAccount customerServiceInAccount = getById(id);
        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付不存在");
        }

        List<CustomerServiceInAccountFile> customerServiceInAccountFiles = iCustomerServiceInAccountFileService.selectByInAccount(id, Lists.newArrayList(InAccountFileType.BASE));

        return InAccountUpdateShowDTO.builder()
                .customerName(customerServiceInAccount.getCustomerName())
                .customerServiceId(customerServiceInAccount.getCustomerServiceId())
                .customerServicePeriodMonthId(customerServiceInAccount.getCustomerServicePeriodMonthId())
                .endTime(customerServiceInAccount.getEndTime())
                .files(iCustomerServiceInAccountFileService.covToCommonFileVO(customerServiceInAccountFiles))
                .id(customerServiceInAccount.getId())
                .inTime(customerServiceInAccount.getInTime())
                .majorCostTotal(customerServiceInAccount.getMajorCostTotal())
                .majorIncomeTotal(customerServiceInAccount.getMajorIncomeTotal())
                .period(customerServiceInAccount.getPeriod())
                .profitTotal(customerServiceInAccount.getProfitTotal())
                .remark(customerServiceInAccount.getRemark())
                .status(customerServiceInAccount.getStatus())
                .statusStr(InAccountStatus.getByCode(customerServiceInAccount.getStatus()).getName())
                .typeStr("入账")

                .bankPaymentInputTime(customerServiceInAccount.getBankPaymentInputTime())

                .build();
    }

    @Override
    public InAccountUpdateShowV2DTO getInAccountUpdateShowV2(Long id) {
        CustomerServiceInAccount customerServiceInAccount = getById(id);
        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付不存在");
        }

        List<CustomerServiceInAccountFile> customerServiceInAccountFiles = iCustomerServiceInAccountFileService.selectByInAccount(id, Lists.newArrayList(InAccountFileType.BASE));

        return InAccountUpdateShowV2DTO.builder()
                .customerName(customerServiceInAccount.getCustomerName())
                .customerServiceId(customerServiceInAccount.getCustomerServiceId())
                .customerServicePeriodMonthId(customerServiceInAccount.getCustomerServicePeriodMonthId())
                .endTime(customerServiceInAccount.getEndTime())
                .files(iCustomerServiceInAccountFileService.covToCommonFileVO(customerServiceInAccountFiles))
                .id(customerServiceInAccount.getId())
                .inTime(customerServiceInAccount.getInTime())
                .majorCostTotal(customerServiceInAccount.getMajorCostTotal())
                .majorIncomeTotal(customerServiceInAccount.getMajorIncomeTotal())
                .period(customerServiceInAccount.getPeriod())
                .profitTotal(customerServiceInAccount.getProfitTotal())
                .remark(customerServiceInAccount.getRemark())
                .status(customerServiceInAccount.getStatus())
                .statusStr(InAccountStatus.getByCode(customerServiceInAccount.getStatus()).getName())
                .typeStr("入账")

                .bankPaymentInputTime(customerServiceInAccount.getBankPaymentInputTime())

                .deliverResult(customerServiceInAccount.getDeliverResult())
                .deliverResultStr(customerServiceInAccount.getDeliverResult() == null ? null : InAccountDeliverResult.getByCode(customerServiceInAccount.getDeliverResult()).getName())
                .taxReportCount(customerServiceInAccount.getTaxReportCount())
                .taxReportSalaryTotal(customerServiceInAccount.getTaxReportSalaryTotal())
                .bankPaymentInputResult(customerServiceInAccount.getBankPaymentInputResult())
                .inAccountResult(customerServiceInAccount.getInAccountResult())

                .build();
    }

    @Override
    @Transactional
    public void updateInAccount(Long deptId, UpdateInAccountVO vo) {
        CustomerServiceInAccount customerServiceInAccount = getById(vo.getId());
        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付不存在");
        }
        if (!InAccountStatus.canUpdate(customerServiceInAccount.getStatus())) {
            throw new ServiceException("当前状态的入账交付不可编辑");
        }

        if (customerServiceInAccount.getInTime() != null) {
            if (vo.getInTime() == null) {
                throw new ServiceException("入账时间 不可改为空");
            }
        }

        if (customerServiceInAccount.getEndTime() != null) {
            if (vo.getEndTime() == null) {
                throw new ServiceException("结账时间 不可改为空");
            }
        }

        LocalDate today = LocalDate.now();
        boolean needCheckTime = false;//是否需要校验入账时间或结账时间

        if (vo.getInTime() != null) {
            if (vo.getInTime().isAfter(today)) {
                throw new ServiceException("入账时间 不可晚于今日");
            }

            //校验一：如果有填写入账时间，需要校验该客户服务之前的账期是否还有入账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
            needCheckTime = true;
        }

        if (vo.getEndTime() != null) {
            if (vo.getEndTime().isAfter(today)) {
                throw new ServiceException("结账时间 不可晚于今日");
            }

            //校验三：如果有填写结账时间，且没有前置未结账的入账交付单，且同交付单的入账时间为空，报错，原因：入账还未完整不能结账
            if (vo.getInTime() == null) {
                throw new ServiceException("入账还未完整不能结账");
            }

            //校验二：如果有填写结账时间，需要校验该客户服务之前的账期是否还有入账时间或结账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
            needCheckTime = true;
        }

        if (needCheckTime) {
            //获取之前的入账交付单
            List<CustomerServiceInAccount> customerServiceInAccounts = customerServiceInAccountMapper.selectNotTime(customerServiceInAccount.getCustomerServiceId(), customerServiceInAccount.getPeriod());

            if (!ObjectUtils.isEmpty(customerServiceInAccounts)) {
                if (vo.getInTime() != null) {
                    //校验一：如果有填写入账时间，需要校验该客户服务之前的账期是否还有入账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
                    if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null)) {
                        throw new ServiceException("前置账期未完成入账");
                    }
                }

                if (vo.getEndTime() != null) {
                    //校验二：如果有填写结账时间，需要校验该客户服务之前的账期是否还有入账时间或结账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
                    if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null || row.getEndTime() == null)) {
                        throw new ServiceException("前置账期未完成入账");
                    }
                }
            }
        }

        //获取操作人员信息
        Long userId = Objects.isNull(vo.getUserId()) ? SecurityUtils.getUserId() : vo.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();

        CustomerServiceInAccount updateEntry = new CustomerServiceInAccount();
        updateEntry.setId(vo.getId());
        updateEntry.setUpdateTime(LocalDateTime.now());

        if (vo.getInTime() != null || vo.getEndTime() != null) {
            Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
            SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

            if (vo.getInTime() != null) {
                updateEntry.setInEmployeeDeptId(deptId);
                updateEntry.setInEmployeeDeptName(sysDept.getDeptName());
                updateEntry.setInEmployeeId(employeeId);
                updateEntry.setInEmployeeName(setOperName);
                updateEntry.setInTime(vo.getInTime());
            }
            if (vo.getEndTime() != null) {
                updateEntry.setEndEmployeeDeptId(deptId);
                updateEntry.setEndEmployeeDeptName(sysDept.getDeptName());
                updateEntry.setEndEmployeeId(employeeId);
                updateEntry.setEndEmployeeName(setOperName);
                updateEntry.setEndTime(vo.getEndTime());
            }
        }

        /*updateEntry.setMajorCostTotal();
        updateEntry.setMajorIncomeTotal();
        updateEntry.setProfitTotal();*/
        updateEntry.setRemark(vo.getRemark());

        /*
         * 未入账/已入账未结账/已入账已结账
         * 逻辑状态：
         * 入账时间和结账时间都为空的：未入账
         * 有入账时间无结账时间：已入账未结账
         * 二者皆有：已入账已结账
         */
        updateEntry.setStatus(handleStatus(updateEntry.getInTime(), updateEntry.getEndTime()));

        //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
        maybeUpdateRepairAccountDeliverStatus(customerServiceInAccount);

        update(updateEntry, new UpdateWrapper<CustomerServiceInAccount>()
                .lambda()
                .eq(CustomerServiceInAccount::getId, vo.getId())

                .set(CustomerServiceInAccount::getMajorCostTotal, vo.getMajorCostTotal())
                .set(CustomerServiceInAccount::getMajorIncomeTotal, vo.getMajorIncomeTotal())
                .set(CustomerServiceInAccount::getProfitTotal, vo.getProfitTotal())
        );

        if (Objects.isNull(vo.getDealFiles()) || vo.getDealFiles()) {
            //先删除原来的文件
            iCustomerServiceInAccountFileService.deleteByInAccount(vo.getId(), Lists.newArrayList(InAccountFileType.BASE));
            //再存附件
            iCustomerServiceInAccountFileService.saveFile(vo.getId(), vo.getFiles(), InAccountFileType.BASE, String.valueOf(vo.getId()));
        }

        //凭票入账、备注、附件
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("入账时间", vo.getInTime());
        map.put("结账时间", vo.getEndTime());
        map.put("主营收入累计", vo.getMajorIncomeTotal());
        map.put("主营成本累计", vo.getMajorCostTotal());
        map.put("利润累计", vo.getProfitTotal());
        map.put("备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(updateEntry.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑入账交付")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark("保存入账交付结果")
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public Integer updateInAccountV2(Long deptId, UpdateInAccountV2VO vo) {
        log.info("updateInAccountV2Param vo={}", new Gson().toJson(vo));
        CustomerServiceInAccount customerServiceInAccount = getById(vo.getId());
        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付不存在");
        }
        if (!InAccountStatus.canUpdate(customerServiceInAccount.getStatus())) {
            throw new ServiceException("当前状态的入账交付不可编辑");
        }

        if (vo.getBankPaymentInputTime() == null || vo.getInTime() == null) {
            if (vo.getEndTime() != null) {
                throw new ServiceException("当银行流水日期和入账日期都有的时候，才会有结账时间");
            }
        } else {
            if (vo.getEndTime() == null) {
                throw new ServiceException("当银行流水日期和入账日期都有的时候，需要有结账时间");
            }

            LocalDate max;
            if (vo.getBankPaymentInputTime().isAfter(vo.getInTime())) {
                max = vo.getBankPaymentInputTime();
            } else {
                max = vo.getInTime();
            }

            if (!Objects.equals(max, vo.getEndTime())) {
                throw new ServiceException("当银行流水日期和入账日期都有的时候，取最晚日期为结账日期");
            }
        }

        if (customerServiceInAccount.getInTime() != null) {
            if (vo.getInTime() == null) {
                throw new ServiceException("入账时间 不可改为空");
            }
        }

        if (customerServiceInAccount.getEndTime() != null) {
            if (vo.getEndTime() == null) {
                throw new ServiceException("结账时间 不可改为空");
            }
        }

        LocalDate today = LocalDate.now();
        boolean needCheckTime = false;//是否需要校验入账时间或结账时间

        if (vo.getInTime() != null) {
            if (vo.getInTime().isAfter(today)) {
                throw new ServiceException("入账时间 不可晚于今日");
            }

            //校验一：如果有填写入账时间，需要校验该客户服务之前的账期是否还有入账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
            needCheckTime = true;
        }

        if (vo.getEndTime() != null) {
            if (vo.getEndTime().isAfter(today)) {
                throw new ServiceException("结账时间 不可晚于今日");
            }

            //校验三：如果有填写结账时间，且没有前置未结账的入账交付单，且同交付单的入账时间为空，报错，原因：入账还未完整不能结账
            if (vo.getInTime() == null) {
                throw new ServiceException("入账还未完整不能结账");
            }

            //校验二：如果有填写结账时间，需要校验该客户服务之前的账期是否还有入账时间或结账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
            needCheckTime = true;
        }

        /*if (needCheckTime) {
            //获取之前的入账交付单
            List<CustomerServiceInAccount> customerServiceInAccounts = customerServiceInAccountMapper.selectNotTime(customerServiceInAccount.getCustomerServiceId(), customerServiceInAccount.getPeriod());

            if (!ObjectUtils.isEmpty(customerServiceInAccounts)) {
                if (vo.getInTime() != null) {
                    //校验一：如果有填写入账时间，需要校验该客户服务之前的账期是否还有入账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
                    if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null)) {
                        throw new ServiceException("前置账期未完成入账");
                    }
                }

                if (vo.getEndTime() != null) {
                    //校验二：如果有填写结账时间，需要校验该客户服务之前的账期是否还有入账时间或结账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
                    if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null || row.getEndTime() == null)) {
                        throw new ServiceException("前置账期未完成入账");
                    }
                }
            }
        }*/

        //获取操作人员信息
        Long userId = Objects.isNull(vo.getUserId()) ? SecurityUtils.getUserId() : vo.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        //String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        String setOperName = ObjectUtils.isEmpty(employees) ? "" : employees.get(0).getEmployeeName();

        CustomerServiceInAccount updateEntry = new CustomerServiceInAccount();
        updateEntry.setId(vo.getId());
        updateEntry.setUpdateTime(LocalDateTime.now());

        if (vo.getInTime() != null || vo.getEndTime() != null) {
            Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
            SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

            if (vo.getInTime() != null) {
                updateEntry.setInEmployeeDeptId(deptId);
                updateEntry.setInEmployeeDeptName(sysDept.getDeptName());
                updateEntry.setInEmployeeId(employeeId);
                updateEntry.setInEmployeeName(setOperName);
                updateEntry.setInTime(vo.getInTime());
            }
            if (vo.getEndTime() != null) {
                updateEntry.setEndEmployeeDeptId(deptId);
                updateEntry.setEndEmployeeDeptName(sysDept.getDeptName());
                updateEntry.setEndEmployeeId(employeeId);
                updateEntry.setEndEmployeeName(setOperName);
                updateEntry.setEndTime(vo.getEndTime());
            }
        }

        /*updateEntry.setMajorCostTotal();
        updateEntry.setMajorIncomeTotal();
        updateEntry.setProfitTotal();*/
        updateEntry.setRemark(vo.getRemark());

        /*
         * 未入账/已入账未结账/已入账已结账
         * 逻辑状态：
         * 入账时间和结账时间都为空的：未入账
         * 有入账时间无结账时间：已入账未结账
         * 二者皆有：已入账已结账
         */
        updateEntry.setStatus(handleStatus(updateEntry.getInTime(), updateEntry.getEndTime()));

        //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
        maybeUpdateRepairAccountDeliverStatus(customerServiceInAccount);

        update(updateEntry, new UpdateWrapper<CustomerServiceInAccount>()
                .lambda()
                .eq(CustomerServiceInAccount::getId, vo.getId())

                .set(CustomerServiceInAccount::getMajorCostTotal, vo.getMajorCostTotal())
                .set(CustomerServiceInAccount::getMajorIncomeTotal, vo.getMajorIncomeTotal())
                .set(CustomerServiceInAccount::getProfitTotal, vo.getProfitTotal())

                .set(CustomerServiceInAccount::getBankPaymentInputTime, vo.getBankPaymentInputTime())
        );

        if (Objects.isNull(vo.getDealFiles()) || vo.getDealFiles()) {
            //先删除原来的文件
            iCustomerServiceInAccountFileService.deleteByInAccount(vo.getId(), Lists.newArrayList(InAccountFileType.BASE));
            //再存附件
            iCustomerServiceInAccountFileService.saveFile(vo.getId(), vo.getFiles(), InAccountFileType.BASE, String.valueOf(vo.getId()));
        }

        //凭票入账、备注、附件
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("入账时间", vo.getInTime());
        map.put("结账时间", vo.getEndTime());
        map.put("银行流水录入日期", vo.getBankPaymentInputTime());
        map.put("主营收入累计", vo.getMajorIncomeTotal());
        map.put("主营成本累计", vo.getMajorCostTotal());
        map.put("利润累计", vo.getProfitTotal());
        map.put("备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(updateEntry.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑入账交付")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark("保存入账交付结果")
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }

        return vo.getIndex();
    }

    @Override
    public Integer updateInAccountV2Inner(Long deptId, UpdateInAccountV2VO vo) {
        log.info("updateInAccountV2InnerParam vo={}", new Gson().toJson(vo));

        //禅道581：上传的时候，如果是空，就是不处理。
        if (vo.getBankPaymentInputTime() == null
                && vo.getInTime() == null
                && vo.getMajorIncomeTotal() == null
                && vo.getMajorCostTotal() == null
                && vo.getProfitTotal() == null
                && StringUtils.isEmpty(vo.getRemark())
        ) {
            return vo.getIndex();
        }

        CustomerServiceInAccount customerServiceInAccount = getById(vo.getId());
        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付不存在");
        }
        if (!InAccountStatus.canUpdate(customerServiceInAccount.getStatus())) {
            throw new ServiceException("当前状态的入账交付不可编辑");
        }

        /*if (vo.getBankPaymentInputTime() == null || vo.getInTime() == null) {
            if (vo.getEndTime() != null) {
                throw new ServiceException("当银行流水日期和入账日期都有的时候，才会有结账时间");
            }
        } else {
            if (vo.getEndTime() == null) {
                throw new ServiceException("当银行流水日期和入账日期都有的时候，需要有结账时间");
            }

            LocalDate max;
            if (vo.getBankPaymentInputTime().isAfter(vo.getInTime())) {
                max = vo.getBankPaymentInputTime();
            } else {
                max = vo.getInTime();
            }

            if (!Objects.equals(max, vo.getEndTime())) {
                throw new ServiceException("当银行流水日期和入账日期都有的时候，取最晚日期为结账日期");
            }
        }*/

        /*if (customerServiceInAccount.getInTime() != null) {
            if (vo.getInTime() == null) {
                throw new ServiceException("入账时间 不可改为空");
            }
        }

        if (customerServiceInAccount.getEndTime() != null) {
            if (vo.getEndTime() == null) {
                throw new ServiceException("结账时间 不可改为空");
            }
        }*/

        LocalDate today = LocalDate.now();
        boolean needCheckTime = false;//是否需要校验入账时间或结账时间

        if (vo.getInTime() != null) {
            if (vo.getInTime().isAfter(today)) {
                throw new ServiceException("入账时间 不可晚于今日");
            }

            //校验一：如果有填写入账时间，需要校验该客户服务之前的账期是否还有入账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
            needCheckTime = true;
        }

        if (vo.getEndTime() != null) {
            if (vo.getEndTime().isAfter(today)) {
                throw new ServiceException("结账时间 不可晚于今日");
            }

            //校验三：如果有填写结账时间，且没有前置未结账的入账交付单，且同交付单的入账时间为空，报错，原因：入账还未完整不能结账
            if (vo.getInTime() == null) {
                throw new ServiceException("入账还未完整不能结账");
            }

            //校验二：如果有填写结账时间，需要校验该客户服务之前的账期是否还有入账时间或结账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
            needCheckTime = true;
        }

        /*if (needCheckTime) {
            //获取之前的入账交付单
            List<CustomerServiceInAccount> customerServiceInAccounts = customerServiceInAccountMapper.selectNotTime(customerServiceInAccount.getCustomerServiceId(), customerServiceInAccount.getPeriod());

            if (!ObjectUtils.isEmpty(customerServiceInAccounts)) {
                if (vo.getInTime() != null) {
                    //校验一：如果有填写入账时间，需要校验该客户服务之前的账期是否还有入账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
                    if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null)) {
                        throw new ServiceException("前置账期未完成入账");
                    }
                }

                if (vo.getEndTime() != null) {
                    //校验二：如果有填写结账时间，需要校验该客户服务之前的账期是否还有入账时间或结账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
                    if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null || row.getEndTime() == null)) {
                        throw new ServiceException("前置账期未完成入账");
                    }
                }
            }
        }*/

        //获取操作人员信息
        Long userId = Objects.isNull(vo.getUserId()) ? SecurityUtils.getUserId() : vo.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        //String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        String setOperName = ObjectUtils.isEmpty(employees) ? "" : employees.get(0).getEmployeeName();

        CustomerServiceInAccount updateEntry = new CustomerServiceInAccount();
        updateEntry.setId(vo.getId());
        updateEntry.setUpdateTime(LocalDateTime.now());

        if (vo.getInTime() != null || vo.getEndTime() != null) {
            Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
            SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

            if (vo.getInTime() != null) {
                updateEntry.setInEmployeeDeptId(deptId);
                updateEntry.setInEmployeeDeptName(sysDept.getDeptName());
                updateEntry.setInEmployeeId(employeeId);
                updateEntry.setInEmployeeName(setOperName);
                updateEntry.setInTime(vo.getInTime());
            }
            if (vo.getEndTime() != null) {
                updateEntry.setEndEmployeeDeptId(deptId);
                updateEntry.setEndEmployeeDeptName(sysDept.getDeptName());
                updateEntry.setEndEmployeeId(employeeId);
                updateEntry.setEndEmployeeName(setOperName);
                updateEntry.setEndTime(vo.getEndTime());
            }
        }

        updateEntry.setMajorCostTotal(vo.getMajorCostTotal());
        updateEntry.setMajorIncomeTotal(vo.getMajorIncomeTotal());
        updateEntry.setProfitTotal(vo.getProfitTotal());
        updateEntry.setRemark(vo.getRemark());
        updateEntry.setBankPaymentInputTime(vo.getBankPaymentInputTime());

        /*
         * 未入账/已入账未结账/已入账已结账
         * 逻辑状态：
         * 入账时间和结账时间都为空的：未入账
         * 有入账时间无结账时间：已入账未结账
         * 二者皆有：已入账已结账
         */
        updateEntry.setStatus(handleStatus(updateEntry.getInTime(), updateEntry.getEndTime()));

        //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
        maybeUpdateRepairAccountDeliverStatus(customerServiceInAccount);

        /*update(updateEntry, new UpdateWrapper<CustomerServiceInAccount>()
                .lambda()
                .eq(CustomerServiceInAccount::getId, vo.getId())

                .set(CustomerServiceInAccount::getMajorCostTotal, vo.getMajorCostTotal())
                .set(CustomerServiceInAccount::getMajorIncomeTotal, vo.getMajorIncomeTotal())
                .set(CustomerServiceInAccount::getProfitTotal, vo.getProfitTotal())

                .set(CustomerServiceInAccount::getBankPaymentInputTime, vo.getBankPaymentInputTime())
        );*/

        updateById(updateEntry);

        if (Objects.isNull(vo.getDealFiles()) || vo.getDealFiles()) {
            //先删除原来的文件
            iCustomerServiceInAccountFileService.deleteByInAccount(vo.getId(), Lists.newArrayList(InAccountFileType.BASE));
            //再存附件
            iCustomerServiceInAccountFileService.saveFile(vo.getId(), vo.getFiles(), InAccountFileType.BASE, String.valueOf(vo.getId()));
        }

        //凭票入账、备注、附件
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("入账时间", vo.getInTime());
        map.put("结账时间", vo.getEndTime());
        map.put("银行流水录入日期", vo.getBankPaymentInputTime());
        map.put("主营收入累计", vo.getMajorIncomeTotal());
        map.put("主营成本累计", vo.getMajorCostTotal());
        map.put("利润累计", vo.getProfitTotal());
        map.put("备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(updateEntry.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑入账交付")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark("保存入账交付结果")
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }

        return vo.getIndex();
    }

    @Override
    public Integer updateInAccountV2_refactoring(Long deptId, UpdateInAccountV2VO vo) {
        log.info("updateInAccountV2_refactoring vo={}", new Gson().toJson(vo));
        CustomerServiceInAccount customerServiceInAccount = getById(vo.getId());

        checkUpdateInAccountParamForWeb(vo, customerServiceInAccount);

        LocalDate endTime = getEndTimeByLogic(vo.getBankPaymentInputTime(), vo.getInTime());

        //获取操作人员信息
        Long userId = SecurityUtils.getUserId();//WEB端调用的，一定会有这个
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        CustomerServiceInAccount updateEntry = new CustomerServiceInAccount();
        updateEntry.setId(vo.getId());
        updateEntry.setUpdateTime(LocalDateTime.now());

        if (vo.getInTime() != null) {
            //入账时间不可改为空的

            updateEntry.setInEmployeeDeptId(deptId);
            updateEntry.setInEmployeeDeptName(operateUserInfoDTO.getSysDept().getDeptName());
            updateEntry.setInEmployeeId(operateUserInfoDTO.getEmployeeId());
            updateEntry.setInEmployeeName(operateUserInfoDTO.getOperName());
            updateEntry.setInTime(vo.getInTime());
        }
        if (endTime != null) {
            //入账时间和银行流水时间不可改为空的，继而也能得出结账时间不可改为空的
            //结账时间不是前端传过来的，是后端计算得出来的，所以用 endTime

            updateEntry.setEndEmployeeDeptId(deptId);
            updateEntry.setEndEmployeeDeptName(operateUserInfoDTO.getSysDept().getDeptName());
            updateEntry.setEndEmployeeId(operateUserInfoDTO.getEmployeeId());
            updateEntry.setEndEmployeeName(operateUserInfoDTO.getOperName());
            updateEntry.setEndTime(endTime);
        }

        //1、未入账未结账：没有入账时间和结账时间
        //2、已入账未结账：有入账时间没有结账时间
        //3、已入账已结账：有入账时间有结账时间
        updateEntry.setStatus(handleStatus(updateEntry.getInTime(), endTime));

        //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
        maybeUpdateRepairAccountDeliverStatus(customerServiceInAccount);

        update(updateEntry, new UpdateWrapper<CustomerServiceInAccount>()
                .lambda()
                .eq(CustomerServiceInAccount::getId, vo.getId())

                .set(CustomerServiceInAccount::getRemark, vo.getRemark())

                .set(CustomerServiceInAccount::getMajorCostTotal, vo.getMajorCostTotal())
                .set(CustomerServiceInAccount::getMajorIncomeTotal, vo.getMajorIncomeTotal())
                .set(CustomerServiceInAccount::getProfitTotal, vo.getProfitTotal())

                .set(CustomerServiceInAccount::getBankPaymentInputTime, vo.getBankPaymentInputTime())
        );

        //先删除原来的文件
        iCustomerServiceInAccountFileService.deleteByInAccount(vo.getId(), Lists.newArrayList(InAccountFileType.BASE));
        //再存附件
        iCustomerServiceInAccountFileService.saveFile(vo.getId(), vo.getFiles(), InAccountFileType.BASE, String.valueOf(vo.getId()));

        //记录日志
        saveLogUpdateInAccount(deptId, vo, userId, operateUserInfoDTO.getOperName());

        return vo.getIndex();
    }


    @Override
    public Integer updateInAccountV2Inner_refactoring(Long deptId, UpdateInAccountV2VO vo) {
        log.info("updateInAccountV2Inner_refactoring vo={}", new Gson().toJson(vo));

        //禅道581：上传的时候，如果是空，就是不处理。
        if (vo.getBankPaymentInputTime() == null
                && vo.getInTime() == null
                && vo.getMajorIncomeTotal() == null
                && vo.getMajorCostTotal() == null
                && vo.getProfitTotal() == null
                && StringUtils.isEmpty(vo.getRemark())
        ) {
            log.info("updateInAccountV2Inner_refactoring case1：所有值都没有，不处理，不记操作记录 vo={}", new Gson().toJson(vo));
            return vo.getIndex();
        }

        CustomerServiceInAccount customerServiceInAccount = getById(vo.getId());

        checkUpdateInAccountParamForBatchDeliverInner(vo, customerServiceInAccount);

        LocalDate bankPaymentInputTime = vo.getBankPaymentInputTime() == null ? customerServiceInAccount.getBankPaymentInputTime() : vo.getBankPaymentInputTime();
        LocalDate inTime = vo.getInTime() == null ? customerServiceInAccount.getInTime() : vo.getInTime();
        LocalDate endTime = getEndTimeByLogic(bankPaymentInputTime, inTime);

        //获取操作人员信息

        Long userId = vo.getUserId();//内部用的，一定会有这个
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? "" : employees.get(0).getEmployeeName();
        Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

        CustomerServiceInAccount updateEntry = new CustomerServiceInAccount();
        updateEntry.setId(vo.getId());
        updateEntry.setUpdateTime(LocalDateTime.now());

        if (vo.getInTime() != null) {
            updateEntry.setInEmployeeDeptId(deptId);
            updateEntry.setInEmployeeDeptName(sysDept.getDeptName());
            updateEntry.setInEmployeeId(employeeId);
            updateEntry.setInEmployeeName(setOperName);
            updateEntry.setInTime(vo.getInTime());
        }
        if (endTime != null) {
            updateEntry.setEndEmployeeDeptId(deptId);
            updateEntry.setEndEmployeeDeptName(sysDept.getDeptName());
            updateEntry.setEndEmployeeId(employeeId);
            updateEntry.setEndEmployeeName(setOperName);
            updateEntry.setEndTime(endTime);
        }

        updateEntry.setBankPaymentInputTime(vo.getBankPaymentInputTime());
        updateEntry.setMajorIncomeTotal(vo.getMajorIncomeTotal());
        updateEntry.setMajorCostTotal(vo.getMajorCostTotal());
        updateEntry.setProfitTotal(vo.getProfitTotal());
        updateEntry.setRemark(vo.getRemark());

        // 1、未入账未结账：没有入账时间和结账时间
        // 2、已入账未结账：有入账时间没有结账时间
        // 3、已入账已结账：有入账时间有结账时间
        updateEntry.setStatus(handleStatus(updateEntry.getInTime(), endTime));

        //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
        maybeUpdateRepairAccountDeliverStatus(customerServiceInAccount);

        updateById(updateEntry);

        if (Objects.isNull(vo.getDealFiles()) || vo.getDealFiles()) {
            //先删除原来的文件
            iCustomerServiceInAccountFileService.deleteByInAccount(vo.getId(), Lists.newArrayList(InAccountFileType.BASE));
            //再存附件
            iCustomerServiceInAccountFileService.saveFile(vo.getId(), vo.getFiles(), InAccountFileType.BASE, String.valueOf(vo.getId()));
        }

        //记录日志
        saveLogUpdateInAccount(deptId, vo, userId, setOperName);

        return vo.getIndex();
    }

    //入账编辑-WEB端API-校验
    private static void checkUpdateInAccountParamForWeb(UpdateInAccountV2VO vo, CustomerServiceInAccount customerServiceInAccount) {
        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付不存在");
        }
//        if (!InAccountStatus.canUpdate(customerServiceInAccount.getStatus())) {
//            throw new ServiceException("当前状态的入账交付不可编辑");
//        }

//        if (customerServiceInAccount.getBankPaymentInputTime() != null) {
//            /*if (vo.getBankPaymentInputTime() == null) {
//                throw new ServiceException("银行流水录入日期 不可改为空");
//            }*/
//
//            if (!Objects.equals(customerServiceInAccount.getBankPaymentInputTime(), vo.getBankPaymentInputTime())) {
//                throw new ServiceException("银行流水录入日期 不可改");
//            }
//        }
//
//        if (customerServiceInAccount.getInTime() != null) {
//            /*if (vo.getInTime() == null) {
//                throw new ServiceException("入账时间 不可改为空");
//            }*/
//
//            if (!Objects.equals(customerServiceInAccount.getInTime(), vo.getInTime())) {
//                throw new ServiceException("入账时间 不可改");
//            }
//        }

//        LocalDate today = LocalDate.now();
//
//        if (vo.getBankPaymentInputTime() != null) {
//            if (vo.getBankPaymentInputTime().isAfter(today)) {
//                throw new ServiceException("银行流水录入日期 不可晚于今日");
//            }
//        }
//
//        if (vo.getInTime() != null) {
//            if (vo.getInTime().isAfter(today)) {
//                throw new ServiceException("入账时间 不可晚于今日");
//            }
//        }
    }

    //入账编辑-批量交付内部API-校验
    private static void checkUpdateInAccountParamForBatchDeliverInner(UpdateInAccountV2VO vo, CustomerServiceInAccount customerServiceInAccount) {
        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付不存在");
        }
//        if (!InAccountStatus.canUpdate(customerServiceInAccount.getStatus())) {
//            throw new ServiceException("当前状态的入账交付不可编辑");
//        }

//        if (customerServiceInAccount.getBankPaymentInputTime() != null) {
//            if (vo.getBankPaymentInputTime() != null) {
//                if (!Objects.equals(customerServiceInAccount.getBankPaymentInputTime(), vo.getBankPaymentInputTime())) {
//                    throw new ServiceException("银行流水录入日期 不可改");
//                }
//            }
//        }
//
//        if (customerServiceInAccount.getInTime() != null) {
//            if (vo.getInTime() != null) {
//                if (!Objects.equals(customerServiceInAccount.getInTime(), vo.getInTime())) {
//                    throw new ServiceException("银行流水录入日期 不可改");
//                }
//            }
//        }
//
//        LocalDate today = LocalDate.now();
//
//        if (vo.getBankPaymentInputTime() != null) {
//            if (vo.getBankPaymentInputTime().isAfter(today)) {
//                throw new ServiceException("银行流水录入日期 不可晚于今日");
//            }
//        }
//
//        if (vo.getInTime() != null) {
//            if (vo.getInTime().isAfter(today)) {
//                throw new ServiceException("入账时间 不可晚于今日");
//            }
//        }
    }

    //逻辑得出结账时间
    private static LocalDate getEndTimeByLogic(LocalDate bankPaymentInputTime, LocalDate inTime) {
        LocalDate endTime = null;
        if (bankPaymentInputTime != null && inTime != null) {
            if (bankPaymentInputTime.isAfter(inTime)) {
                endTime = bankPaymentInputTime;
            } else {
                endTime = inTime;
            }
        }
        return endTime;
    }

    //入账编辑-写日志
    private void saveLogUpdateInAccount(Long deptId, UpdateInAccountV2VO vo, Long userId, String setOperName) {
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("入账时间", vo.getInTime());
        map.put("结账时间", vo.getEndTime());
        map.put("银行流水录入日期", vo.getBankPaymentInputTime());
        map.put("主营收入累计", vo.getMajorIncomeTotal());
        map.put("主营成本累计", vo.getMajorCostTotal());
        map.put("利润累计", vo.getProfitTotal());
        map.put("备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(vo.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑入账交付")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark("保存入账交付结果")
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

//    @Override
//    @Transactional
//    public void updateInAccountV3(Long deptId, UpdateInAccountV3VO vo) {
//        CustomerServiceInAccount customerServiceInAccount = getById(vo.getId());
//        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
//            throw new ServiceException("入账交付不存在");
//        }
//        if (!InAccountStatus.canUpdate(customerServiceInAccount.getStatus())) {
//            throw new ServiceException("当前状态的入账交付不可编辑");
//        }
//
//        //校验参数
//        checkUpdateInAccountParam(vo, customerServiceInAccount);
//
//        //获取操作人员信息
//        Long userId = Objects.isNull(vo.getUserId()) ? SecurityUtils.getUserId() : vo.getUserId();
//        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
//        String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
//
//        CustomerServiceInAccount updateEntry = new CustomerServiceInAccount();
//        updateEntry.setId(vo.getId());
//        updateEntry.setUpdateTime(LocalDateTime.now());
//
//        if (vo.getInTime() != null || vo.getEndTime() != null) {
//            Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
//            SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
//
//            if (vo.getInTime() != null) {
//                updateEntry.setInEmployeeDeptId(deptId);
//                updateEntry.setInEmployeeDeptName(sysDept.getDeptName());
//                updateEntry.setInEmployeeId(employeeId);
//                updateEntry.setInEmployeeName(setOperName);
//                updateEntry.setInTime(vo.getInTime());
//            }
//            if (vo.getEndTime() != null) {
//                updateEntry.setEndEmployeeDeptId(deptId);
//                updateEntry.setEndEmployeeDeptName(sysDept.getDeptName());
//                updateEntry.setEndEmployeeId(employeeId);
//                updateEntry.setEndEmployeeName(setOperName);
//                updateEntry.setEndTime(vo.getEndTime());
//            }
//        }
//
//        updateEntry.setRemark(vo.getRemark());
//
//
//         * 未入账/已入账未结账/已入账已结账
//         * 逻辑状态：
//         * 入账时间和结账时间都为空的：未入账
//         * 有入账时间无结账时间：已入账未结账
//         * 二者皆有：已入账已结账
//
//        updateEntry.setStatus(handleStatus(updateEntry.getInTime(), updateEntry.getEndTime()));
//
//        //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
//        maybeUpdateRepairAccountDeliverStatus(customerServiceInAccount);
//
//        update(updateEntry, new UpdateWrapper<CustomerServiceInAccount>()
//                .lambda()
//                .eq(CustomerServiceInAccount::getId, vo.getId())
//
//                .set(CustomerServiceInAccount::getDeliverResult, vo.getDeliverResult())
//
//                .set(CustomerServiceInAccount::getMajorCostTotal, vo.getMajorCostTotal())
//                .set(CustomerServiceInAccount::getMajorIncomeTotal, vo.getMajorIncomeTotal())
//                .set(CustomerServiceInAccount::getProfitTotal, vo.getProfitTotal())
//
//                .set(CustomerServiceInAccount::getTaxReportCount, vo.getTaxReportCount())
//                .set(CustomerServiceInAccount::getTaxReportSalaryTotal, vo.getTaxReportSalaryTotal())
//
//                .set(CustomerServiceInAccount::getBankPaymentInputTime, vo.getBankPaymentInputTime())
//        );
//
//        if (Objects.isNull(vo.getDealFiles()) || vo.getDealFiles()) {
//            //先删除原来的文件
//            iCustomerServiceInAccountFileService.deleteByInAccount(vo.getId(), Lists.newArrayList(InAccountFileType.BASE));
//            //再存附件
//            iCustomerServiceInAccountFileService.saveFile(vo.getId(), vo.getFiles(), InAccountFileType.BASE, String.valueOf(vo.getId()));
//        }
//
//        //凭票入账、备注、附件
//        Map<String, Object> map = Maps.newLinkedHashMap();
//        map.put("入账时间", vo.getInTime());
//        map.put("结账时间", vo.getEndTime());
//        map.put("交付结果", vo.getDeliverResult());
//        map.put("银行流水录入日期", vo.getBankPaymentInputTime());
//        map.put("本年累计主营收入", vo.getMajorIncomeTotal());
//        map.put("本年累计主营成本", vo.getMajorCostTotal());
//        map.put("本年累计会计利润", vo.getProfitTotal());
//        map.put("个税申报人数", vo.getTaxReportCount());
//        map.put("本年个税申报工资总额", vo.getTaxReportSalaryTotal());
//        map.put("账务备注", vo.getRemark());
//        String operContent = JSONObject.toJSONString(map);
//        try {
//            //Long userId = SecurityUtils.getUserId();
//            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
//
//            asyncLogService.saveBusinessLog(
//                    new BusinessLogDTO()
//                            .setBusinessId(updateEntry.getId())
//                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
//                            .setDeptId(deptId)
//                            .setOperType("编辑入账交付")
//                            .setOperName(setOperName)
//                            .setOperContent(operContent)
//                            .setOperRemark("保存入账交付结果")
//                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
//                            .setOperUserId(userId)
//            );
//        } catch (Exception e) {
//            log.error("新增业务日志失败:{}", e.getMessage());
//            throw new ServiceException("操作记录写入失败，请稍后重试");
//        }
//    }

    @Override
    @Transactional
    public Integer updateInAccountV3(Long deptId, UpdateInAccountV3VO vo) {
        log.info("updateInAccountV2_refactoring vo={}", new Gson().toJson(vo));
        CustomerServiceInAccount customerServiceInAccount = getById(vo.getId());
        checkUpdateInAccountParamForWebV3V2(vo, customerServiceInAccount);

        //LocalDate endTime = getEndTimeByLogic(vo.getBankPaymentInputTime(), vo.getInTime());

        //获取操作人员信息
        Long userId = SecurityUtils.getUserId();//WEB端调用的，一定会有这个
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        CustomerServiceInAccount updateEntry = new CustomerServiceInAccount();
        updateEntry.setId(vo.getId());
        updateEntry.setUpdateTime(LocalDateTime.now());
        updateEntry.setBankPaymentInputResult(vo.getBankPaymentInputResult());
        updateEntry.setInAccountResult(vo.getInAccountResult());
        if (Objects.isNull(customerServiceInAccount.getBankPaymentInputTime()) && !Objects.isNull(vo.getBankPaymentInputResult()) && BankPaymentInputResult.setBankPaymentInputTimeResult().contains(vo.getBankPaymentInputResult())) {
            vo.setBankPaymentInputTime(LocalDate.now());
        }
        if (Objects.isNull(customerServiceInAccount.getInTime()) && !Objects.isNull(vo.getInAccountResult()) && InAccountResult.setInTimeResult().contains(vo.getInAccountResult())) {
            vo.setInTime(LocalDate.now());
        }

//        if (Objects.equals(vo.getDeliverResult(), InAccountDeliverResult.NOT_NEED_DELIVER.getCode())) {
//            //如果交付结果=无需交付时，将操作时间写为结账/完结时间
//            endTime = LocalDate.now();
//        } else {
//            endTime = getEndTimeByLogic(vo.getBankPaymentInputTime(), vo.getInTime());
//        }
//
        if (vo.getBankPaymentInputTime() != null) {
            updateEntry.setBankPaymentInputTime(vo.getBankPaymentInputTime());
        }
        // 结账时间更新
        LocalDate endTime = null;
        LocalDate bankInputTime = vo.getBankPaymentInputTime() != null ? vo.getBankPaymentInputTime() : customerServiceInAccount.getBankPaymentInputTime();
        LocalDate inTime = vo.getInTime() != null ? vo.getInTime() : customerServiceInAccount.getInTime();
        if (bankInputTime != null && inTime != null) {
            endTime = getEndTimeByLogic(bankInputTime, inTime);
        }

        if (vo.getInTime() != null) {
            //入账时间不可改为空的

            updateEntry.setInEmployeeDeptId(deptId);
            updateEntry.setInEmployeeDeptName(operateUserInfoDTO.getSysDept().getDeptName());
            updateEntry.setInEmployeeId(operateUserInfoDTO.getEmployeeId());
            updateEntry.setInEmployeeName(operateUserInfoDTO.getOperName());
            updateEntry.setInTime(vo.getInTime());
        }
        if (endTime != null) {
            //入账时间和银行流水时间不可改为空的，继而也能得出结账时间不可改为空的
            //结账时间不是前端传过来的，是后端计算得出来的，所以用 endTime

            updateEntry.setEndEmployeeDeptId(deptId);
            updateEntry.setEndEmployeeDeptName(operateUserInfoDTO.getSysDept().getDeptName());
            updateEntry.setEndEmployeeId(operateUserInfoDTO.getEmployeeId());
            updateEntry.setEndEmployeeName(operateUserInfoDTO.getOperName());
            updateEntry.setEndTime(endTime);
        }

//        //禅道 657：设置为“异常”或“无需交付”的时候，入账状态不修改；（后端）
//        if (!InAccountDeliverResult.unNormal(vo.getDeliverResult())) {
//
//            //1、未入账未结账：没有入账时间和结账时间
//            //2、已入账未结账：有入账时间没有结账时间
//            //3、已入账已结账：有入账时间有结账时间
//            updateEntry.setStatus(handleStatus(updateEntry.getInTime(), endTime));
//        }
        // 状态修改，只有入账时间，状态写已入账未结账 有银行流水时间和入账时间，状态写已入账已结账 都没有 未入账未结账
        updateEntry.setStatus(handleStatus(inTime, endTime));

        if (!Objects.isNull(vo.getBankPaymentInputResult()) && Lists.newArrayList(BankPaymentInputResult.NORMAL.getCode(), BankPaymentInputResult.PART_MISSING.getCode()).contains(vo.getBankPaymentInputResult())) {
            if (Objects.isNull(customerServiceInAccount.getFirstBankPaymentInputTime()) || Objects.isNull(customerServiceInAccount.getFirstBankPaymentInputResult())) {
                updateEntry.setFirstBankPaymentInputTime(LocalDate.now());
                updateEntry.setFirstBankPaymentInputResult(vo.getBankPaymentInputResult());
            }
            updateEntry.setLastBankPaymentInputTime(LocalDate.now());
            updateEntry.setLastBankPaymentInputResult(vo.getBankPaymentInputResult());
        }
        if (!Objects.isNull(vo.getInAccountResult()) && Objects.equals(vo.getInAccountResult(), InAccountResult.NORMAL.getCode())) {
            if (Objects.isNull(customerServiceInAccount.getFirstInAccountTime()) || Objects.isNull(customerServiceInAccount.getFirstInAccountResult())) {
                updateEntry.setFirstInAccountTime(LocalDate.now());
                updateEntry.setFirstInAccountResult(vo.getInAccountResult());
            }
            updateEntry.setLastInAccountTime(LocalDate.now());
            updateEntry.setLastInAccountResult(vo.getInAccountResult());
        }

        update(updateEntry, new UpdateWrapper<CustomerServiceInAccount>()
                .lambda()
                .eq(CustomerServiceInAccount::getId, vo.getId())
                // 入账结果作为交付结果
                .set(CustomerServiceInAccount::getDeliverResult, Objects.isNull(vo.getInAccountResult()) ? null : InAccountDeliverResult.convertFromInAccountResult(vo.getInAccountResult()).getCode())

                .set(CustomerServiceInAccount::getRemark, vo.getRemark())

                .set(CustomerServiceInAccount::getMajorCostTotal, vo.getMajorCostTotal())
                .set(CustomerServiceInAccount::getMajorIncomeTotal, vo.getMajorIncomeTotal())
                .set(CustomerServiceInAccount::getProfitTotal, vo.getProfitTotal())
                .set(CustomerServiceInAccount::getTaxReportCount, vo.getTaxReportCount())
                .set(CustomerServiceInAccount::getTaxReportSalaryTotal, vo.getTaxReportSalaryTotal())

//                .set(CustomerServiceInAccount::getBankPaymentInputTime, vo.getBankPaymentInputTime())
        );

        //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
        maybeUpdateRepairAccountDeliverStatus(customerServiceInAccount);

        //先删除原来的文件
        iCustomerServiceInAccountFileService.deleteByInAccount(vo.getId(), Lists.newArrayList(InAccountFileType.BASE));
        //再存附件
        iCustomerServiceInAccountFileService.saveFile(vo.getId(), vo.getFiles(), InAccountFileType.BASE, String.valueOf(vo.getId()));

        //记录日志
//        saveLogUpdateInAccountV3(customerServiceInAccount, deptId, vo, userId, operateUserInfoDTO.getOperName());
        saveLogUpdateInAccountV4(customerServiceInAccount, bankInputTime, inTime, endTime, deptId, vo, userId, operateUserInfoDTO.getOperName());

        // 添加当天入账编辑记录
        customerServiceInAccountUpdateRecordService.createInAccountUpdateRecord(customerServiceInAccount.getCustomerServiceId());

        return vo.getIndex();
    }

    @Override
    public Integer updateInAccountV3Inner(Long deptId, UpdateInAccountV3VO vo) {
        log.info("updateInAccountV3Inner vo={}", new Gson().toJson(vo));

        //仿照V2 的 禅道581：上传的时候，如果是空，就是不处理。
        if (StringUtils.isEmpty(vo.getBankPaymentInputResultStr())
                && StringUtils.isEmpty(vo.getInAccountResultStr())
                && vo.getMajorIncomeTotal() == null
                && vo.getMajorCostTotal() == null
                && vo.getProfitTotal() == null
                && vo.getTaxReportCount() == null
                && vo.getTaxReportSalaryTotal() == null
                && StringUtils.isEmpty(vo.getRemark())
        ) {
            log.info("updateInAccountV3Inner case1：所有值都没有，不处理，不记操作记录 vo={}", new Gson().toJson(vo));
            return vo.getIndex();
        }

        Integer inAccountResult = StringUtils.isEmpty(vo.getInAccountResultStr()) ? null : InAccountResult.getByName(vo.getInAccountResultStr()).getCode();
        Integer bankPaymentInputResult = StringUtils.isEmpty(vo.getBankPaymentInputResultStr()) ? null : BankPaymentInputResult.getByName(vo.getBankPaymentInputResultStr()).getCode();
        vo.setInAccountResult(inAccountResult);
        vo.setBankPaymentInputResult(bankPaymentInputResult);
        CustomerServiceInAccount customerServiceInAccount = getById(vo.getId());

        checkUpdateInAccountParamForBatchDeliverInnerV3V2(vo, customerServiceInAccount);

        /*LocalDate bankPaymentInputTime = vo.getBankPaymentInputTime() == null ? customerServiceInAccount.getBankPaymentInputTime() : vo.getBankPaymentInputTime();
        LocalDate inTime = vo.getInTime() == null ? customerServiceInAccount.getInTime() : vo.getInTime();
        LocalDate endTime = getEndTimeByLogic(bankPaymentInputTime, inTime);*/

//        LocalDate endTime;
//        if (Objects.equals(InAccountDeliverResult.getByName(vo.getDeliverResultStr()).getCode(), InAccountDeliverResult.NOT_NEED_DELIVER.getCode())) {
//            //如果交付结果=无需交付时，将操作时间写为结账/完结时间
//            endTime = LocalDate.now();
//        } else {
//            LocalDate bankPaymentInputTime = vo.getBankPaymentInputTime() == null ? customerServiceInAccount.getBankPaymentInputTime() : vo.getBankPaymentInputTime();
//            LocalDate inTime = vo.getInTime() == null ? customerServiceInAccount.getInTime() : vo.getInTime();
//            endTime = getEndTimeByLogic(bankPaymentInputTime, inTime);
//        }

        //获取操作人员信息

        Long userId = vo.getUserId();//内部用的，一定会有这个
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? "" : employees.get(0).getEmployeeName();
        Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

        CustomerServiceInAccount updateEntry = new CustomerServiceInAccount();
        updateEntry.setId(vo.getId());
        updateEntry.setUpdateTime(LocalDateTime.now());
        updateEntry.setBankPaymentInputResult(vo.getBankPaymentInputResult());
        updateEntry.setInAccountResult(inAccountResult);
        if (Objects.isNull(customerServiceInAccount.getBankPaymentInputTime()) && !Objects.isNull(bankPaymentInputResult) && BankPaymentInputResult.setBankPaymentInputTimeResult().contains(bankPaymentInputResult)) {
            vo.setBankPaymentInputTime(LocalDate.now());
        }
        if (Objects.isNull(customerServiceInAccount.getInTime()) && !Objects.isNull(inAccountResult) && InAccountResult.setInTimeResult().contains(inAccountResult)) {
            vo.setInTime(LocalDate.now());
        }

        if (!Objects.isNull(vo.getBankPaymentInputResult()) && Lists.newArrayList(BankPaymentInputResult.NORMAL.getCode(), BankPaymentInputResult.PART_MISSING.getCode()).contains(vo.getBankPaymentInputResult())) {
            if (Objects.isNull(customerServiceInAccount.getFirstBankPaymentInputTime()) || Objects.isNull(customerServiceInAccount.getFirstBankPaymentInputResult())) {
                updateEntry.setFirstBankPaymentInputTime(LocalDate.now());
                updateEntry.setFirstBankPaymentInputResult(vo.getBankPaymentInputResult());
            }
            updateEntry.setLastBankPaymentInputTime(LocalDate.now());
            updateEntry.setLastBankPaymentInputResult(vo.getBankPaymentInputResult());
        }
        if (!Objects.isNull(vo.getInAccountResult()) && Objects.equals(vo.getInAccountResult(), InAccountResult.NORMAL.getCode())) {
            if (Objects.isNull(customerServiceInAccount.getFirstInAccountTime()) || Objects.isNull(customerServiceInAccount.getFirstInAccountResult())) {
                updateEntry.setFirstInAccountTime(LocalDate.now());
                updateEntry.setFirstInAccountResult(vo.getInAccountResult());
            }
            updateEntry.setLastInAccountTime(LocalDate.now());
            updateEntry.setLastInAccountResult(vo.getInAccountResult());
        }

        LocalDate endTime = null;
        LocalDate bankInputTime = vo.getBankPaymentInputTime() != null ? vo.getBankPaymentInputTime() : customerServiceInAccount.getBankPaymentInputTime();
        LocalDate inTime = vo.getInTime() != null ? vo.getInTime() : customerServiceInAccount.getInTime();
        if (bankInputTime != null && inTime != null) {
            endTime = getEndTimeByLogic(bankInputTime, inTime);
        }

        if (vo.getBankPaymentInputTime() != null) {
            updateEntry.setBankPaymentInputTime(vo.getBankPaymentInputTime());
        }

        if (vo.getInTime() != null) {
            updateEntry.setInEmployeeDeptId(deptId);
            updateEntry.setInEmployeeDeptName(sysDept.getDeptName());
            updateEntry.setInEmployeeId(employeeId);
            updateEntry.setInEmployeeName(setOperName);
            updateEntry.setInTime(vo.getInTime());
        }
        if (endTime != null) {
            updateEntry.setEndEmployeeDeptId(deptId);
            updateEntry.setEndEmployeeDeptName(sysDept.getDeptName());
            updateEntry.setEndEmployeeId(employeeId);
            updateEntry.setEndEmployeeName(setOperName);
            updateEntry.setEndTime(endTime);
        }

        updateEntry.setStatus(handleStatus(inTime, endTime));

        Integer deliverResult = Objects.isNull(inAccountResult) ? null : InAccountDeliverResult.convertFromInAccountResult(inAccountResult).getCode();

        updateEntry.setDeliverResult(deliverResult);
        updateEntry.setMajorIncomeTotal(vo.getMajorIncomeTotal());
        updateEntry.setMajorCostTotal(vo.getMajorCostTotal());
        updateEntry.setProfitTotal(vo.getProfitTotal());
        updateEntry.setTaxReportCount(vo.getTaxReportCount());
        updateEntry.setTaxReportSalaryTotal(vo.getTaxReportSalaryTotal());
        updateEntry.setRemark(vo.getRemark());

        //禅道 657：设置为“异常”或“无需交付”的时候，入账状态不修改；（后端）
//        if (!InAccountDeliverResult.unNormal(deliverResult)) {
//            // 1、未入账未结账：没有入账时间和结账时间
//            // 2、已入账未结账：有入账时间没有结账时间
//            // 3、已入账已结账：有入账时间有结账时间
//            updateEntry.setStatus(handleStatus(updateEntry.getInTime(), endTime));
//        }


        updateById(updateEntry);

        //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
        maybeUpdateRepairAccountDeliverStatus(customerServiceInAccount);

        if (Objects.isNull(vo.getDealFiles()) || vo.getDealFiles()) {
            //先删除原来的文件
            iCustomerServiceInAccountFileService.deleteByInAccount(vo.getId(), Lists.newArrayList(InAccountFileType.BASE));
            //再存附件
            iCustomerServiceInAccountFileService.saveFile(vo.getId(), vo.getFiles(), InAccountFileType.BASE, String.valueOf(vo.getId()));
        }
        if (!Objects.isNull(vo.getDealFiles()) && !vo.getDealFiles()) {
            if (!ObjectUtils.isEmpty(vo.getFiles())) {
                iCustomerServiceInAccountFileService.saveFile(vo.getId(), vo.getFiles(), InAccountFileType.BASE, String.valueOf(vo.getId()));
            }
        }

        //记录日志
        vo.setDeliverResult(deliverResult);
        vo.setBankPaymentInputResult(bankPaymentInputResult);
        vo.setInAccountResult(inAccountResult);
        saveLogUpdateInAccountV4_inner(customerServiceInAccount, bankInputTime, inTime, endTime, deptId, vo, userId, setOperName);

        // 添加当天入账编辑记录
        customerServiceInAccountUpdateRecordService.createInAccountUpdateRecord(customerServiceInAccount.getCustomerServiceId());

        return vo.getIndex();
    }

    private void checkUpdateInAccountParamForWebV3(UpdateInAccountV3VO vo, CustomerServiceInAccount customerServiceInAccount) {
        //老参数校验，和上个版本一样的校验逻辑
        UpdateInAccountV2VO voV2 = new UpdateInAccountV2VO();
        BeanUtils.copyProperties(vo, voV2);
        checkUpdateInAccountParamForWeb(voV2, customerServiceInAccount);

        //新参数校验
        if (vo.getDeliverResult() == null) {
            throw new ServiceException("交付结果不可为空");
        }
    }

    //V3的V2版本
    private void checkUpdateInAccountParamForWebV3V2(UpdateInAccountV3VO vo, CustomerServiceInAccount customerServiceInAccount) {
        //老参数校验，和上个版本一样的校验逻辑
        UpdateInAccountV2VO voV2 = new UpdateInAccountV2VO();
        BeanUtils.copyProperties(vo, voV2);
        checkUpdateInAccountParamForWeb(voV2, customerServiceInAccount);

        //新参数校验
//        if (vo.getDeliverResult() == null) {
//            throw new ServiceException("交付结果不可为空");
//        }
//
//        if (InAccountDeliverResult.unNormal(vo.getDeliverResult())) {
//            if (StringUtils.isEmpty(vo.getRemark())) {
//                throw new ServiceException("当选择“异常”或“无需交付”时，备注必填");
//            }
//        }
//        if (vo.getBankPaymentInputResult() == null && vo.getInAccountResult() == null) {
//            throw new ServiceException("入账结果和银行流水录入结果不能都为空");
//        }

        if (!Objects.isNull(vo.getInAccountResult()) && InAccountResult.unNormal(vo.getInAccountResult())) {
            if (StringUtils.isEmpty(vo.getRemark())) {
                throw new ServiceException("当选择“异常”或“无需交付”时，备注必填");
            }
        }
    }

    private void checkUpdateInAccountParamForBatchDeliverInnerV3(UpdateInAccountV3VO vo, CustomerServiceInAccount customerServiceInAccount) {
        //老参数校验，和上个版本一样的校验逻辑
        UpdateInAccountV2VO voV2 = new UpdateInAccountV2VO();
        BeanUtils.copyProperties(vo, voV2);
        checkUpdateInAccountParamForBatchDeliverInner(voV2, customerServiceInAccount);

        InAccountDeliverResult inAccountDeliverResult = InAccountDeliverResult.getByName(vo.getDeliverResultStr());
        if (inAccountDeliverResult == null || Objects.equals(inAccountDeliverResult, InAccountDeliverResult.UN_KNOW)) {
            throw new ServiceException("交付结果 不合规，只能是: " + Arrays.stream(InAccountDeliverResult.values()).filter(r -> !Objects.equals(r, InAccountDeliverResult.UN_KNOW)).map(InAccountDeliverResult::getName).collect(Collectors.joining("、")));
        }
    }

    //V3的V2版本
    private void checkUpdateInAccountParamForBatchDeliverInnerV3V2(UpdateInAccountV3VO vo, CustomerServiceInAccount customerServiceInAccount) {
        //老参数校验，和上个版本一样的校验逻辑
        UpdateInAccountV2VO voV2 = new UpdateInAccountV2VO();
        BeanUtils.copyProperties(vo, voV2);
        checkUpdateInAccountParamForBatchDeliverInner(voV2, customerServiceInAccount);

//        InAccountDeliverResult inAccountDeliverResult = InAccountDeliverResult.getByName(vo.getDeliverResultStr());
//        if (inAccountDeliverResult == null || Objects.equals(inAccountDeliverResult, InAccountDeliverResult.UN_KNOW)) {
//            throw new ServiceException("交付结果 不合规，只能是: " + Arrays.stream(InAccountDeliverResult.values()).filter(r -> !Objects.equals(r, InAccountDeliverResult.UN_KNOW)).map(InAccountDeliverResult::getName).collect(Collectors.joining("、")));
//        }

        BankPaymentInputResult bankPaymentInputResult = BankPaymentInputResult.getByName(vo.getBankPaymentInputResultStr());
        if (!StringUtils.isEmpty(vo.getBankPaymentInputResultStr()) && (bankPaymentInputResult == null || Objects.equals(bankPaymentInputResult, BankPaymentInputResult.UN_KNOW))) {
            throw new ServiceException("银行流水录入结果 不合规，只能是: " + Arrays.stream(BankPaymentInputResult.values()).filter(r -> !Objects.equals(r, BankPaymentInputResult.UN_KNOW)).map(BankPaymentInputResult::getName).collect(Collectors.joining("、")));
        }

        InAccountResult inAccountResult = InAccountResult.getByName(vo.getInAccountResultStr());
        if (!StringUtils.isEmpty(vo.getInAccountResultStr()) && (inAccountResult == null || Objects.equals(inAccountResult, InAccountResult.UN_KNOWN))) {
            throw new ServiceException("入账结果 不合规，只能是: " + Arrays.stream(InAccountResult.values()).filter(r -> !Objects.equals(r, InAccountResult.UN_KNOWN)).map(InAccountResult::getName).collect(Collectors.joining("、")));
        }

        if (InAccountResult.unNormal(inAccountResult.getCode())) {
            if (StringUtils.isEmpty(vo.getRemark())) {
                throw new ServiceException("当选择“异常”或“无需交付”时，备注必填");
            }
        }
    }

    private void saveLogUpdateInAccountV3(CustomerServiceInAccount customerServiceInAccount, Long deptId, UpdateInAccountV3VO vo, Long userId, String operName) {
        //禅道 657   批量交付和编辑交付时，没有修改的值（包括不可以修改的值）不需要写操作记录。

        Map<String, Object> map = Maps.newHashMap();
        if (!Objects.equals(customerServiceInAccount.getInTime(), vo.getInTime())) {
            map.put("入账时间", vo.getInTime());
        }
        //map.put("结账时间", vo.getEndTime());//背后逻辑修改的，不用写到操作日志
        if (!Objects.equals(customerServiceInAccount.getDeliverResult(), vo.getDeliverResult())) {
            map.put("交付结果", vo.getDeliverResult() == null ? "" : InAccountDeliverResult.getByCode(vo.getDeliverResult()).getName());
        }
        if (!Objects.equals(customerServiceInAccount.getBankPaymentInputTime(), vo.getBankPaymentInputTime())) {
            map.put("银行流水录入日期", vo.getBankPaymentInputTime());
        }
        if (!Objects.equals(customerServiceInAccount.getMajorIncomeTotal(), vo.getMajorIncomeTotal())) {
            map.put("本年累计主营收入", vo.getMajorIncomeTotal());
        }
        if (!Objects.equals(customerServiceInAccount.getMajorCostTotal(), vo.getMajorCostTotal())) {
            map.put("本年累计主营成本", vo.getMajorCostTotal());
        }
        if (!Objects.equals(customerServiceInAccount.getProfitTotal(), vo.getProfitTotal())) {
            map.put("本年累计会计利润", vo.getProfitTotal());
        }
        if (!Objects.equals(customerServiceInAccount.getTaxReportCount(), vo.getTaxReportCount())) {
            map.put("个税申报人数", vo.getTaxReportCount());
        }
        if (!Objects.equals(customerServiceInAccount.getTaxReportSalaryTotal(), vo.getTaxReportSalaryTotal())) {
            map.put("本年个税申报工资总额", vo.getTaxReportSalaryTotal());
        }
        //map.put("账务备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(vo.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑入账交付")
                            .setOperName(operName)
                            .setOperContent(operContent)
                            //.setOperRemark(vo.getRemark() == null ? "" : vo.getRemark())
                            .setOperRemark(
                                    Objects.equals(customerServiceInAccount.getRemark(), vo.getRemark())
                                            ? ""
                                            : (vo.getRemark() == null ? "" : vo.getRemark())
                            )
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void saveLogUpdateInAccountV3_inner(CustomerServiceInAccount customerServiceInAccount, Long deptId, UpdateInAccountV3VO vo, Long userId, String operName) {
        //禅道 657   批量交付和编辑交付时，没有修改的值（包括不可以修改的值）不需要写操作记录。

        Map<String, Object> map = Maps.newHashMap();
        if (vo.getInTime() != null && !Objects.equals(customerServiceInAccount.getInTime(), vo.getInTime())) {
            map.put("入账时间", vo.getInTime());
        }
        //map.put("结账时间", vo.getEndTime());//背后逻辑修改的，不用写到操作日志
        if (vo.getDeliverResult() != null && !Objects.equals(customerServiceInAccount.getDeliverResult(), vo.getDeliverResult())) {
            map.put("交付结果", vo.getDeliverResult() == null ? "" : InAccountDeliverResult.getByCode(vo.getDeliverResult()).getName());
        }
        if (vo.getBankPaymentInputTime() != null && !Objects.equals(customerServiceInAccount.getBankPaymentInputTime(), vo.getBankPaymentInputTime())) {
            map.put("银行流水录入日期", vo.getBankPaymentInputTime());
        }
        if (vo.getMajorIncomeTotal() != null && !Objects.equals(customerServiceInAccount.getMajorIncomeTotal(), vo.getMajorIncomeTotal())) {
            map.put("本年累计主营收入", vo.getMajorIncomeTotal());
        }
        if (vo.getMajorCostTotal() != null && !Objects.equals(customerServiceInAccount.getMajorCostTotal(), vo.getMajorCostTotal())) {
            map.put("本年累计主营成本", vo.getMajorCostTotal());
        }
        if (vo.getProfitTotal() != null && !Objects.equals(customerServiceInAccount.getProfitTotal(), vo.getProfitTotal())) {
            map.put("本年累计会计利润", vo.getProfitTotal());
        }
        if (vo.getTaxReportCount() != null && !Objects.equals(customerServiceInAccount.getTaxReportCount(), vo.getTaxReportCount())) {
            map.put("个税申报人数", vo.getTaxReportCount());
        }
        if (vo.getTaxReportSalaryTotal() != null && !Objects.equals(customerServiceInAccount.getTaxReportSalaryTotal(), vo.getTaxReportSalaryTotal())) {
            map.put("本年个税申报工资总额", vo.getTaxReportSalaryTotal());
        }
        //map.put("账务备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(vo.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑入账交付")
                            .setOperName(operName)
                            .setOperContent(operContent)
                            //.setOperRemark(vo.getRemark() == null ? "" : vo.getRemark())
                            .setOperRemark(
                                    (StringUtils.isEmpty(customerServiceInAccount.getRemark()) || Objects.equals(customerServiceInAccount.getRemark(), vo.getRemark()))
                                            ? ""
                                            : (vo.getRemark() == null ? "" : vo.getRemark()))
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void saveLogUpdateInAccountV4_inner(CustomerServiceInAccount customerServiceInAccount, LocalDate bankPaymentInputTime, LocalDate inTime, LocalDate endTime, Long deptId, UpdateInAccountV3VO vo, Long userId, String operName) {
        //禅道 657   批量交付和编辑交付时，没有修改的值（包括不可以修改的值）不需要写操作记录。

        Map<String, Object> map = Maps.newHashMap();
        if (!Objects.isNull(vo.getBankPaymentInputResult())) {
            map.put("银行流水录入结果", BankPaymentInputResult.getByCode(vo.getBankPaymentInputResult()).getName());
        }
        if (!Objects.isNull(bankPaymentInputTime)) {
            map.put("银行流水录入时间", bankPaymentInputTime);
        }
        if (!Objects.isNull(vo.getInAccountResult())) {
            map.put("入账结果", InAccountResult.getByCode(vo.getInAccountResult()).getName());
        }
        if (!Objects.isNull(inTime)) {
            map.put("入账时间", inTime);
        }
        if (!Objects.isNull(endTime)) {
            map.put("结账时间", endTime);
        }
        if (vo.getMajorIncomeTotal() != null && !Objects.equals(customerServiceInAccount.getMajorIncomeTotal(), vo.getMajorIncomeTotal())) {
            map.put("本年累计主营收入", vo.getMajorIncomeTotal());
        }
        if (vo.getMajorCostTotal() != null && !Objects.equals(customerServiceInAccount.getMajorCostTotal(), vo.getMajorCostTotal())) {
            map.put("本年累计主营成本", vo.getMajorCostTotal());
        }
        if (vo.getProfitTotal() != null && !Objects.equals(customerServiceInAccount.getProfitTotal(), vo.getProfitTotal())) {
            map.put("本年累计会计利润", vo.getProfitTotal());
        }
        if (vo.getTaxReportCount() != null && !Objects.equals(customerServiceInAccount.getTaxReportCount(), vo.getTaxReportCount())) {
            map.put("个税申报人数", vo.getTaxReportCount());
        }
        if (vo.getTaxReportSalaryTotal() != null && !Objects.equals(customerServiceInAccount.getTaxReportSalaryTotal(), vo.getTaxReportSalaryTotal())) {
            map.put("本年个税申报工资总额", vo.getTaxReportSalaryTotal());
        }
        //map.put("账务备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(vo.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑入账交付")
                            .setOperName(operName)
                            .setOperContent(operContent)
                            //.setOperRemark(vo.getRemark() == null ? "" : vo.getRemark())
                            .setOperRemark(
                                    (StringUtils.isEmpty(customerServiceInAccount.getRemark()) || Objects.equals(customerServiceInAccount.getRemark(), vo.getRemark()))
                                            ? ""
                                            : (vo.getRemark() == null ? "" : vo.getRemark()))
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void saveLogUpdateInAccountV4(CustomerServiceInAccount customerServiceInAccount, LocalDate bankPaymentInputTime, LocalDate inTime, LocalDate endTime, Long deptId, UpdateInAccountV3VO vo, Long userId, String operName) {
        Map<String, Object> map = Maps.newHashMap();
        if (!Objects.isNull(vo.getBankPaymentInputResult())) {
            map.put("银行流水录入结果", BankPaymentInputResult.getByCode(vo.getBankPaymentInputResult()).getName());
        }
        if (!Objects.isNull(bankPaymentInputTime)) {
            map.put("银行流水录入时间", bankPaymentInputTime);
        }
        if (!Objects.isNull(vo.getInAccountResult())) {
            map.put("入账结果", InAccountResult.getByCode(vo.getInAccountResult()).getName());
        }
        if (!Objects.isNull(inTime)) {
            map.put("入账时间", inTime);
        }
        if (!Objects.isNull(endTime)) {
            map.put("结账时间", endTime);
        }
        if (vo.getMajorIncomeTotal() != null && !Objects.equals(customerServiceInAccount.getMajorIncomeTotal(), vo.getMajorIncomeTotal())) {
            map.put("本年累计主营收入", vo.getMajorIncomeTotal());
        }
        if (vo.getMajorCostTotal() != null && !Objects.equals(customerServiceInAccount.getMajorCostTotal(), vo.getMajorCostTotal())) {
            map.put("本年累计主营成本", vo.getMajorCostTotal());
        }
        if (vo.getProfitTotal() != null && !Objects.equals(customerServiceInAccount.getProfitTotal(), vo.getProfitTotal())) {
            map.put("本年累计会计利润", vo.getProfitTotal());
        }
        if (vo.getTaxReportCount() != null && !Objects.equals(customerServiceInAccount.getTaxReportCount(), vo.getTaxReportCount())) {
            map.put("个税申报人数", vo.getTaxReportCount());
        }
        if (vo.getTaxReportSalaryTotal() != null && !Objects.equals(customerServiceInAccount.getTaxReportSalaryTotal(), vo.getTaxReportSalaryTotal())) {
            map.put("本年个税申报工资总额", vo.getTaxReportSalaryTotal());
        }
        //map.put("账务备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(vo.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑入账交付")
                            .setOperName(operName)
                            .setOperContent(operContent)
                            //.setOperRemark(vo.getRemark() == null ? "" : vo.getRemark())
                            .setOperRemark(
                                    (StringUtils.isEmpty(customerServiceInAccount.getRemark()) || Objects.equals(customerServiceInAccount.getRemark(), vo.getRemark()))
                                            ? ""
                                            : (vo.getRemark() == null ? "" : vo.getRemark()))
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void checkUpdateInAccountParam(UpdateInAccountV3VO vo, CustomerServiceInAccount customerServiceInAccount) {
        if (StringUtils.isEmpty(vo.getRemark()) && ObjectUtils.isEmpty(vo.getFiles())) {
            throw new ServiceException("账务备注和附件至少填写一个");
        }

        checkDataParam(vo.getMajorIncomeTotal(), vo.getMajorCostTotal(), vo.getProfitTotal(), vo.getTaxReportCount(), vo.getTaxReportSalaryTotal());

        if (vo.getBankPaymentInputTime() == null || vo.getInTime() == null) {
            if (vo.getEndTime() != null) {
                throw new ServiceException("当银行流水日期和入账日期都有的时候，才会有结账时间");
            }
        } else {
            if (vo.getEndTime() == null) {
                throw new ServiceException("当银行流水日期和入账日期都有的时候，需要有结账时间");
            }

            LocalDate max;
            if (vo.getBankPaymentInputTime().isAfter(vo.getInTime())) {
                max = vo.getBankPaymentInputTime();
            } else {
                max = vo.getInTime();
            }

            if (!Objects.equals(max, vo.getEndTime())) {
                throw new ServiceException("当银行流水日期和入账日期都有的时候，取最晚日期为结账日期");
            }
        }

        if (customerServiceInAccount.getInTime() != null) {
            if (vo.getInTime() == null) {
                throw new ServiceException("入账时间 不可改为空");
            }
        }

        if (customerServiceInAccount.getEndTime() != null) {
            if (vo.getEndTime() == null) {
                throw new ServiceException("结账时间 不可改为空");
            }
        }

        LocalDate today = LocalDate.now();
        boolean needCheckTime = false;//是否需要校验入账时间或结账时间

        if (vo.getInTime() != null) {
            if (vo.getInTime().isAfter(today)) {
                throw new ServiceException("入账时间 不可晚于今日");
            }

            //校验一：如果有填写入账时间，需要校验该客户服务之前的账期是否还有入账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
            needCheckTime = true;
        }

        if (vo.getEndTime() != null) {
            if (vo.getEndTime().isAfter(today)) {
                throw new ServiceException("结账时间 不可晚于今日");
            }

            //校验三：如果有填写结账时间，且没有前置未结账的入账交付单，且同交付单的入账时间为空，报错，原因：入账还未完整不能结账
            if (vo.getInTime() == null) {
                throw new ServiceException("入账还未完整不能结账");
            }

            //校验二：如果有填写结账时间，需要校验该客户服务之前的账期是否还有入账时间或结账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
            needCheckTime = true;
        }

        /*if (needCheckTime) {
            //获取之前的入账交付单
            List<CustomerServiceInAccount> customerServiceInAccounts = customerServiceInAccountMapper.selectNotTime(customerServiceInAccount.getCustomerServiceId(), customerServiceInAccount.getPeriod());

            if (!ObjectUtils.isEmpty(customerServiceInAccounts)) {
                if (vo.getInTime() != null) {
                    //校验一：如果有填写入账时间，需要校验该客户服务之前的账期是否还有入账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
                    if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null)) {
                        throw new ServiceException("前置账期未完成入账");
                    }
                }

                if (vo.getEndTime() != null) {
                    //校验二：如果有填写结账时间，需要校验该客户服务之前的账期是否还有入账时间或结账时间为空的入账交付单，有则报错，原因：前置账期未完成入账
                    if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null || row.getEndTime() == null)) {
                        throw new ServiceException("前置账期未完成入账");
                    }
                }
            }
        }*/
    }

    private void checkDataParam(BigDecimal majorIncomeTotal, BigDecimal majorCostTotal, BigDecimal profitTotal, Integer taxReportCount, BigDecimal taxReportSalaryTotal) {
        List<Object> items = Lists.newArrayList(
                majorIncomeTotal,
                majorCostTotal,
                profitTotal,
                taxReportCount,
                taxReportSalaryTotal
        );
        items = items.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (!(items.size() == 0 || items.size() == 5)) {
            throw new ServiceException("数据要么全空要么全写");
        }
    }

    @Override
    public InAccountDetailDTO getInAccountDetail(Long id) {
        CustomerServiceInAccount customerServiceInAccount = getById(id);
        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付不存在");
        }

        Long customerServicePeriodMonthId = customerServiceInAccount.getCustomerServicePeriodMonthId();

        //拿到这些入账交付单的对应账期的对应材料交接单
        List<CustomerServiceDocHandover> customerServiceDocHandovers = iCustomerServiceDocHandoverService.getMapByPeriodMonthIds(
                Lists.newArrayList(customerServicePeriodMonthId)
        ).get(customerServicePeriodMonthId);

        //int wholeLevel = handleWholeLevel(customerServiceDocHandovers);
        int wholeLevel = handleWholeLevelV2(customerServiceDocHandovers);

        //拿到文件
        Map<Integer, List<CustomerServiceInAccountFile>> filesMap = iCustomerServiceInAccountFileService.selectMapByInAccount(id, Lists.newArrayList(InAccountFileType.BASE, InAccountFileType.RPA));
        List<CommonFileVO> files = iCustomerServiceInAccountFileService.covToCommonFileVO(
                filesMap.getOrDefault(InAccountFileType.BASE.getCode(), Lists.newArrayList())
        );
        List<CommonFileVO> rpaFiles = iCustomerServiceInAccountFileService.covToCommonFileVO(
                filesMap.getOrDefault(InAccountFileType.RPA.getCode(), Lists.newArrayList())
        );

        return InAccountDetailDTO.builder()
                .customerName(customerServiceInAccount.getCustomerName())
                .customerServiceId(customerServiceInAccount.getCustomerServiceId())
                .customerServicePeriodMonthId(customerServicePeriodMonthId)
                .endTime(customerServiceInAccount.getEndTime())
                .id(customerServiceInAccount.getId())
                .inTime(customerServiceInAccount.getInTime())
                .majorCostTotal(customerServiceInAccount.getMajorCostTotal())
                .majorIncomeTotal(customerServiceInAccount.getMajorIncomeTotal())
                .period(customerServiceInAccount.getPeriod())
                .profitTotal(customerServiceInAccount.getProfitTotal())
                .serviceType(customerServiceInAccount.getServiceType())
                .serviceTypeStr(ServiceType.getByCode(customerServiceInAccount.getServiceType()).getName())
                .status(customerServiceInAccount.getStatus())
                .statusStr(InAccountStatus.getByCode(customerServiceInAccount.getStatus()).getName())
                .wholeLevel(wholeLevel)
                .wholeLevelStr(WholeLevel.getByCode(wholeLevel).getName())

                .remark(customerServiceInAccount.getRemark())
                .files(files)
                .filesStr(files.size())

                .bankPaymentInputTime(customerServiceInAccount.getBankPaymentInputTime())

                .deliverResult(customerServiceInAccount.getDeliverResult())
                .deliverResultStr(customerServiceInAccount.getDeliverResult() == null ? null : InAccountDeliverResult.getByCode(customerServiceInAccount.getDeliverResult()).getName())
                .taxReportCount(customerServiceInAccount.getTaxReportCount())
                .taxReportSalaryTotal(customerServiceInAccount.getTaxReportSalaryTotal())

                .rpaExeResult(customerServiceInAccount.getRpaExeResult())
                .rpaExeResultStr(customerServiceInAccount.getRpaExeResult() == null ? null : InAccountRpaExeResult.getByCode(customerServiceInAccount.getRpaExeResult()).getName())
                .tableStatusBalance(customerServiceInAccount.getTableStatusBalance())
                .rpaSearchTime(customerServiceInAccount.getRpaSearchTime())

                .rpaRemark(customerServiceInAccount.getRpaRemark())
                .rpaFiles(rpaFiles)
                .rpaFilesStr(rpaFiles.size())

                .inAccountResult(customerServiceInAccount.getInAccountResult())
                .inAccountResultStr(customerServiceInAccount.getInAccountResult() == null ? "-" : InAccountResult.getByCode(customerServiceInAccount.getInAccountResult()).getName())

                .bankPaymentInputResult(customerServiceInAccount.getBankPaymentInputResult())
                .bankPaymentInputResultStr(customerServiceInAccount.getBankPaymentInputResult() == null ? "-" : BankPaymentInputResult.getByCode(customerServiceInAccount.getBankPaymentInputResult()).getName())
                .build();
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceInAccount> inAccountInBatch(Long deptId, BatchOperateAccountInVO vo) {
        List<CustomerServiceInAccount> total = baseMapper.selectBatchIds(vo.getIds());
        if (ObjectUtils.isEmpty(total)) {
            throw new ServiceException("入账交付不存在");
        }

        Integer operateType = vo.getOperateType();
        if (!Objects.equals(operateType, 1) && !Objects.equals(operateType, 2)) {
            throw new ServiceException("只允许入账或结账操作");
        }
        String operateTypeStr = Objects.equals(operateType, 1) ? "入账" : "结账";

        LocalDate time = vo.getTime();

        if (time.isAfter(LocalDate.now())) {
            throw new ServiceException(operateTypeStr + "时间 不可晚于今日");
        }

        List<CustomerServiceInAccount> success = Lists.newArrayList();
        List<CustomerServiceInAccount> fail = Lists.newArrayList();
        //List<CustomerServiceInAccount> updateEntryList = Lists.newArrayList();

        //获取操作人员信息
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

        Map<Long, List<CustomerServiceInAccount>> sourceMap = total.stream().collect(Collectors.groupingBy(CustomerServiceInAccount::getCustomerServiceId));

        for (Map.Entry<Long, List<CustomerServiceInAccount>> entry : sourceMap.entrySet()) {
            Long customerServiceId = entry.getKey();
            List<CustomerServiceInAccount> value = entry.getValue();

            value.sort(Comparator.comparing(CustomerServiceInAccount::getPeriod));

            //获取之前的入账交付单
            List<CustomerServiceInAccount> customerServiceInAccounts = customerServiceInAccountMapper.selectNotTimeBatchWithout(
                    customerServiceId,
                    value.get(value.size() - 1).getPeriod(),
                    value.stream().map(CustomerServiceInAccount::getId).collect(Collectors.toList())
            );

            if (Objects.equals(operateType, 1)) {
                if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null)) {
                    fail.addAll(value);
                    continue;
                }
            } else {
                if (customerServiceInAccounts.stream().anyMatch(row -> row.getInTime() == null || row.getEndTime() == null)
                        || value.stream().anyMatch(row -> row.getInTime() == null)
                ) {
                    fail.addAll(value);
                    continue;
                }
            }

            for (CustomerServiceInAccount customerServiceInAccount : value) {
                customerServiceInAccount.setUpdateTime(LocalDateTime.now());

                if (Objects.equals(operateType, 1)) {
                    customerServiceInAccount.setInEmployeeDeptId(deptId);
                    customerServiceInAccount.setInEmployeeDeptName(sysDept.getDeptName());
                    customerServiceInAccount.setInEmployeeId(employeeId);
                    customerServiceInAccount.setInEmployeeName(setOperName);
                    customerServiceInAccount.setInTime(time);
                } else {
                    customerServiceInAccount.setEndEmployeeDeptId(deptId);
                    customerServiceInAccount.setEndEmployeeDeptName(sysDept.getDeptName());
                    customerServiceInAccount.setEndEmployeeId(employeeId);
                    customerServiceInAccount.setEndEmployeeName(setOperName);
                    customerServiceInAccount.setEndTime(time);
                }

                customerServiceInAccount.setStatus(
                        handleStatus(customerServiceInAccount.getInTime(), customerServiceInAccount.getEndTime())
                );

                //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
                maybeUpdateRepairAccountDeliverStatus(customerServiceInAccount);

                success.add(customerServiceInAccount);
            }
        }

        if (!ObjectUtils.isEmpty(success)) {
            updateBatchById(success);
        }

        TCommonOperateDTO<CustomerServiceInAccount> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        //插入操作日志
        String countStr = (!ObjectUtils.isEmpty(vo.getIds()) && vo.getIds().size() > 1 ? "批量" : "");
        success.forEach(row -> {
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                                .setDeptId(deptId)
                                .setOperType(countStr + operateTypeStr)
                                .setOperContent(String.format(operateTypeStr + "时间:%s", time))
                                .setOperName(setOperName)
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });

        return result;
    }

    @Override
    public InAccountRpaUpdateShowDTO getInAccountRpaUpdateShow(Long id) {
        CustomerServiceInAccount customerServiceInAccount = getById(id);
        if (customerServiceInAccount == null || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付不存在");
        }

        List<CustomerServiceInAccountFile> customerServiceInAccountFiles = iCustomerServiceInAccountFileService.selectByInAccount(id, Lists.newArrayList(InAccountFileType.RPA));

        return InAccountRpaUpdateShowDTO.builder()
                .customerName(customerServiceInAccount.getCustomerName())
                .customerServiceId(customerServiceInAccount.getCustomerServiceId())
                .customerServicePeriodMonthId(customerServiceInAccount.getCustomerServicePeriodMonthId())
                .id(customerServiceInAccount.getId())
                .majorCostTotal(customerServiceInAccount.getMajorCostTotal())
                .majorIncomeTotal(customerServiceInAccount.getMajorIncomeTotal())
                .period(customerServiceInAccount.getPeriod())
                .profitTotal(customerServiceInAccount.getProfitTotal())
                .rpaExeResult(customerServiceInAccount.getRpaExeResult())
                .rpaExeResultStr(customerServiceInAccount.getRpaExeResult() == null ? null : InAccountRpaExeResult.getByCode(customerServiceInAccount.getRpaExeResult()).getName())
                .rpaFiles(iCustomerServiceInAccountFileService.covToCommonFileVO(customerServiceInAccountFiles))
                .rpaRemark(customerServiceInAccount.getRpaRemark())
                .rpaSearchTime(customerServiceInAccount.getRpaSearchTime())
                .status(customerServiceInAccount.getStatus())
                .statusStr(customerServiceInAccount.getStatus() == null ? null : InAccountStatus.getByCode(customerServiceInAccount.getStatus()).getName())
                .tableStatusBalance(customerServiceInAccount.getTableStatusBalance())
                .taxReportCount(customerServiceInAccount.getTaxReportCount())
                .taxReportSalaryTotal(customerServiceInAccount.getTaxReportSalaryTotal())
                .typeStr("入账")
                .build();
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceInAccount> inAccountRpaUpdateBatch(Long deptId, BatchOperateInAccountRpaUpdateVO vo) {
        log.info("inAccountRpaUpdateBatch vo={}", new Gson().toJson(vo));

        List<CustomerServiceInAccount> total = baseMapper.selectBatchIds(vo.getIds());
        if (ObjectUtils.isEmpty(total)) {
            throw new ServiceException("入账交付不存在");
        }

        checkDataParam(vo.getMajorIncomeTotal(), vo.getMajorCostTotal(), vo.getProfitTotal(), vo.getTaxReportCount(), vo.getTaxReportSalaryTotal());

        List<CustomerServiceInAccount> success = total;
        List<CustomerServiceInAccount> fail = Lists.newArrayList();
        //List<CustomerServiceInAccount> updateEntryList = Lists.newArrayList();

        //获取操作人员信息
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        if (!ObjectUtils.isEmpty(success)) {
            List<CommonFileVO> rpaFiles = vo.getRpaFiles();

            success.forEach(r -> {
                r.setRpaExeResult(vo.getRpaExeResult());

                r.setMajorIncomeTotal(vo.getMajorIncomeTotal());
                r.setMajorCostTotal(vo.getMajorCostTotal());
                r.setProfitTotal(vo.getProfitTotal());
                r.setTaxReportCount(vo.getTaxReportCount());
                r.setTaxReportSalaryTotal(vo.getTaxReportSalaryTotal());

                r.setTableStatusBalance(vo.getTableStatusBalance());
                r.setRpaSearchTime(vo.getRpaSearchTime());
                r.setRpaRemark(vo.getRpaRemark());

                //先删除原来的文件
                iCustomerServiceInAccountFileService.deleteByInAccount(r.getId(), Lists.newArrayList(InAccountFileType.RPA));
                //再存附件
                if (!ObjectUtils.isEmpty(rpaFiles)) {
                    iCustomerServiceInAccountFileService.saveFile(r.getId(), rpaFiles, InAccountFileType.RPA, String.valueOf(r.getId()));
                }
            });

            //批量更新
            updateBatchById(success);

            success.forEach(row -> {
                try {
                    //插入操作日志
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("RPA执行结果", vo.getRpaExeResult() == null ? "" : InAccountRpaExeResult.getByCode(vo.getRpaExeResult()).getName());
                    map.put("本年累计主营收入", vo.getMajorIncomeTotal());
                    map.put("本年累计主营成本", vo.getMajorCostTotal());
                    map.put("本年累计会计利润", vo.getProfitTotal());
                    map.put("个税申报人数", vo.getTaxReportCount());
                    map.put("本年个税申报工资总额", vo.getTaxReportSalaryTotal());
                    map.put("报表是否平衡", vo.getTableStatusBalance());
                    map.put("RPA查询时间", vo.getRpaSearchTime() == null ? "" : vo.getRpaSearchTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    //map.put("RPA备注", vo.getRpaRemark() == null ? "" : vo.getRpaRemark());
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                                    .setDeptId(deptId)
                                    .setOperType("RPA更新")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark(vo.getRpaRemark() == null ? "" : vo.getRpaRemark())
                                    .setOperImages(ObjectUtils.isEmpty(rpaFiles) ? "" : JSONObject.toJSONString(rpaFiles))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        TCommonOperateDTO<CustomerServiceInAccount> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);
        return result;
    }

    @Override
    public Integer inAccountRpaUpdateInner(Long deptId, OperateInAccountRpaUpdateVO vo) {
        log.info("inAccountRpaUpdateInner vo={}", new Gson().toJson(vo));

        //CustomerServiceInAccount customerServiceInAccount = getById(vo.getId());

        //checkDataParam(vo.getMajorIncomeTotal(), vo.getMajorCostTotal(), vo.getProfitTotal(), vo.getTaxReportCount(), vo.getTaxReportSalaryTotal());

        InAccountRpaExeResult inAccountRpaExeResult = InAccountRpaExeResult.getByName(vo.getRpaExeResultStr());
        if (inAccountRpaExeResult == null || Objects.equals(inAccountRpaExeResult, InAccountRpaExeResult.UN_KNOW)) {
            throw new ServiceException(
                    "RPA执行结果 不合规，只能是: "
                            + Arrays.stream(InAccountRpaExeResult.values()).filter(r -> !Objects.equals(r, InAccountRpaExeResult.UN_KNOW)).map(InAccountRpaExeResult::getName).collect(Collectors.joining("、"))
            );
        }

        //获取操作人员信息
        Long userId = vo.getUserId();//内部用的，一定会有这个
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? "" : employees.get(0).getEmployeeName();
        //Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        //SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();


        List<CommonFileVO> rpaFiles = vo.getRpaFiles();

        CustomerServiceInAccount updateEntry = new CustomerServiceInAccount();
        updateEntry.setId(vo.getId());
        updateEntry.setUpdateTime(LocalDateTime.now());

        updateEntry.setRpaExeResult(inAccountRpaExeResult.getCode());

        updateEntry.setMajorIncomeTotal(vo.getMajorIncomeTotal());
        updateEntry.setMajorCostTotal(vo.getMajorCostTotal());
        updateEntry.setProfitTotal(vo.getProfitTotal());
        updateEntry.setTaxReportCount(vo.getTaxReportCount());
        updateEntry.setTaxReportSalaryTotal(vo.getTaxReportSalaryTotal());

        updateEntry.setTableStatusBalance(vo.getTableStatusBalance());
        updateEntry.setRpaSearchTime(vo.getRpaSearchTime());
        updateEntry.setRpaRemark(vo.getRpaRemark());

        //批量更新
        updateById(updateEntry);

        if (Objects.isNull(vo.getDealFiles()) || vo.getDealFiles()) {
            //先删除原来的文件
            iCustomerServiceInAccountFileService.deleteByInAccount(vo.getId(), Lists.newArrayList(InAccountFileType.RPA));
            //再存附件
            if (!ObjectUtils.isEmpty(rpaFiles)) {
                iCustomerServiceInAccountFileService.saveFile(vo.getId(), rpaFiles, InAccountFileType.RPA, String.valueOf(vo.getId()));
            }
        }
        if (!Objects.isNull(vo.getDealFiles()) && !vo.getDealFiles()) {
            if (!ObjectUtils.isEmpty(rpaFiles)) {
                iCustomerServiceInAccountFileService.saveFile(vo.getId(), rpaFiles, InAccountFileType.RPA, String.valueOf(vo.getId()));
            }
        }

        try {
            //插入操作日志
            Map<String, Object> map = Maps.newHashMap();
            //map.put("RPA执行结果", vo.getRpaExeResult() == null ? "" : InAccountRpaExeResult.getByCode(vo.getRpaExeResult()).getName());
            map.put("RPA执行结果", vo.getRpaExeResultStr());
            map.put("本年累计主营收入", vo.getMajorIncomeTotal());
            map.put("本年累计主营成本", vo.getMajorCostTotal());
            map.put("本年累计会计利润", vo.getProfitTotal());
            map.put("个税申报人数", vo.getTaxReportCount());
            map.put("本年个税申报工资总额", vo.getTaxReportSalaryTotal());
            map.put("报表是否平衡", vo.getTableStatusBalance());
            map.put("RPA查询时间", vo.getRpaSearchTime() == null ? "" : vo.getRpaSearchTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            //map.put("RPA备注", vo.getRpaRemark());
            String operContent = JSONObject.toJSONString(map);

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(vo.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(deptId)
                            .setOperType("RPA更新")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark(vo.getRpaRemark() == null ? "" : vo.getRpaRemark())
                            .setOperImages(ObjectUtils.isEmpty(rpaFiles) ? "" : JSONObject.toJSONString(rpaFiles))
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }

        return vo.getIndex();
    }

    @Override
    public List<CustomerServiceInAccount> selectLastInAccountByCustomerIds(List<Long> customerServiceIds) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return Collections.emptyList();
        }
        Map<String, LocalDate> dateMap = DateUtils.getStartEndByDateType(DateUtils.THIS_YEAR);
        Integer startPeriod = Integer.parseInt(DateUtils.localDateToStr(dateMap.get("startDate"), DateUtils.YYYYMM));
        Integer endPeriod = Integer.parseInt(DateUtils.localDateToStr(dateMap.get("endDate"), DateUtils.YYYYMM));
        List<CustomerServiceInAccount> inAccountList = list(new QueryWrapper<CustomerServiceInAccount>().eq("is_del", Boolean.FALSE)
                .isNotNull("end_time").in("customer_service_id", customerServiceIds)
                .le("period", endPeriod).ge("period", startPeriod).groupBy("customer_service_id")
                .select("customer_service_id as customerServiceId", "max(period) as period"));
        if (ObjectUtils.isEmpty(inAccountList)) {
            return Collections.emptyList();
        }
        return baseMapper.selectByInAccountList(inAccountList);
    }

    @Override
    public List<CustomerServiceInAccount> selectBatchByCustomerId(List<Long> customerServiceIds) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServiceInAccount>().in(CustomerServiceInAccount::getCustomerServiceId, customerServiceIds)
                .eq(CustomerServiceInAccount::getIsDel, false));
    }

    @Override
    public void addInAccountFromPeriod(CCustomerService cCustomerService, CustomerServicePeriodMonth customerServicePeriodMonth) {
        if (cCustomerService == null) {
            throw new ServiceException("客户服务 必传");
        }
        if (customerServicePeriodMonth == null) {
            throw new ServiceException("账期 必传");
        }
        if (!Objects.equals(cCustomerService.getId(), customerServicePeriodMonth.getCustomerServiceId())) {
            throw new ServiceException("数据不一致");
        }

        CustomerServiceInAccount newEntry = covCustomerServiceInAccountByCustomerAndPeriod(cCustomerService, customerServicePeriodMonth);

        save(newEntry);
    }

    @Override
    public void softDeleteCustomerServiceInAccountFromRepairAccount(Long customerServiceId, List<Integer> periods) {
        remove(
                new LambdaQueryWrapper<CustomerServiceInAccount>()
                        .eq(CustomerServiceInAccount::getCustomerServiceId, customerServiceId)
                        .in(CustomerServiceInAccount::getPeriod, periods)
        );
    }

    /*@Override
    public void addInAccountForHistory() {
        List<CustomerServicePeriodMonth> source = iCustomerServicePeriodMonthService.getNoInAccount();
        log.info("addInAccountForHistory 需要处理的数据有{}条", source.size());

        if (!ObjectUtils.isEmpty(source)) {
            List<Long> cIds = source.stream().map(CustomerServicePeriodMonth::getCustomerServiceId).distinct().collect(Collectors.toList());
            //List<CCustomerService> customerServices = icCustomerServiceService.listByIds(cIds);
            List<CCustomerService> customerServices = cCustomerServiceMapper.selectBatchIds(cIds);
            Map<Long, CCustomerService> cCustomerServiceMap = customerServices.stream().collect(Collectors.toMap(CCustomerService::getId, r -> r));

            for (CustomerServicePeriodMonth row : source) {
                try {
                    addInAccountFromPeriod(cCustomerServiceMap.get(row.getCustomerServiceId()), row);
                } catch (Exception ex) {
                    log.error("addInAccountForHistory row={}, e={}", row.toString(), ex.getMessage());
                }
            }
        }
    }*/

    @Override
    public void addInAccountForHistory() {
        List<CustomerServicePeriodMonth> source = iCustomerServicePeriodMonthService.getNoInAccount();
        log.info("addInAccountForHistory 需要处理的数据有{}条", source.size());

        if (!ObjectUtils.isEmpty(source)) {
            for (CustomerServicePeriodMonth row : source) {
                CCustomerService cCustomerService = cCustomerServiceMapper.selectById(row.getCustomerServiceId());

                try {
                    save(
                            covCustomerServiceInAccountByCustomerAndPeriod(cCustomerService, row)
                    );
                } catch (Exception ex) {
                    log.error("addInAccountForHistory row={}, e={}", row.toString(), ex.getMessage());
                }
            }
        }
    }

    @Override
    public void addInAccountForHistoryByCustomerService(Long customerServiceId) {
        List<CustomerServicePeriodMonth> source = iCustomerServicePeriodMonthService.getNoInAccountByCustomerService(customerServiceId);
        log.info("addInAccountForHistoryByCustomerService 需要处理的数据有{}条", source.size());

        if (!ObjectUtils.isEmpty(source)) {
            CCustomerService cCustomerService = cCustomerServiceMapper.selectById(customerServiceId);

            for (CustomerServicePeriodMonth row : source) {
                try {
                    save(
                            covCustomerServiceInAccountByCustomerAndPeriod(cCustomerService, row)
                    );
                } catch (Exception ex) {
                    log.error("addInAccountForHistory row={}, e={}", row.toString(), ex.getMessage());
                }
            }
        }
    }

    @Override
    public void addInAccountForHistoryByCustomerServiceV2(List<CCustomerService> customerServices) {
        if (!ObjectUtils.isEmpty(customerServices)) {
            List<CustomerServicePeriodMonth> source = iCustomerServicePeriodMonthService.getNoInAccountByCustomerServiceBatch(
                    customerServices.stream().map(CCustomerService::getId).distinct().collect(Collectors.toList())
            );
            log.info("addInAccountForHistoryByCustomerServiceV2 需要处理的数据有{}条", source.size());

            if (!ObjectUtils.isEmpty(source)) {
                List<CustomerServiceInAccount> needSave = Lists.newArrayList();

                Map<Long, CCustomerService> map = customerServices.stream().collect(Collectors.toMap(CCustomerService::getId, r -> r));

                for (CustomerServicePeriodMonth row : source) {
                    CCustomerService cCustomerService = map.get(row.getCustomerServiceId());

                    needSave.add(
                            covCustomerServiceInAccountByCustomerAndPeriod(cCustomerService, row)
                    );
                }

                if (!ObjectUtils.isEmpty(needSave)) {
                    saveBatch(needSave);
                }
            }
        }
    }

    @Override
    public CustomerServiceInAccount getByPeriodId(Long customerServicePeriodMonthId) {
        if (Objects.isNull(customerServicePeriodMonthId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<CustomerServiceInAccount>().eq(CustomerServiceInAccount::getCustomerServicePeriodMonthId, customerServicePeriodMonthId)
                .eq(CustomerServiceInAccount::getIsDel, false), false);
    }

    /*
     * 账期材料完整度，是根据账期下关联的多个材料交接单逻辑计算的，逻辑和状态展示顺序如下：
     *
     * 未提交材料：无材料交接单
     * 待核验：有至少一条交接单是待核验
     * 无材料：所有交接单都是已核验，且都是无材料
     * 有缺失：所有交接单都是已核验，且有一条是有缺失
     * 缺但齐：所有交接单都是已核验，且没有交接单是已缺失，且有一条是缺但齐
     * 已完整：所有交接单都是已核验，且所有交接单都是已完整
     */
    //处理 完整度搜索
    //得到账期ID
    @Override
    public CommonIdsSearchVO wholeLevelSearchV2(Integer wholeLevel) {
        boolean needSearch = true;//是否需要搜索
        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
        List<Long> ids = null;

        if (wholeLevel == null) {
            needSearch = false;
        } else {
            if (Objects.equals(wholeLevel, WholeLevel.UN_SUBMIT_DATA.getCode())) {
                //未提交材料：无材料交接单
                ids = customerServiceDocHandoverMapper.searchWholeLevel_UN_SUBMIT_DATA();
            } else if (Objects.equals(wholeLevel, WholeLevel.WAIT_CHECK.getCode())) {
                //待核验：有至少一条交接单是待核验
                ids = customerServiceDocHandoverMapper.searchWholeLevel_WAIT_CHECK();
            } else if (Objects.equals(wholeLevel, WholeLevel.LACK_AND_COMPLETED_NEED_ADD.getCode())
                    || Objects.equals(wholeLevel, WholeLevel.LACK_AND_COMPLETED.getCode())
                    || Objects.equals(wholeLevel, WholeLevel.COMPLETED.getCode())
            ) {
                //有缺失：所有交接单都是已核验，且有一条是有缺失
                //缺但齐：所有交接单都是已核验，且没有交接单是已缺失，且有一条是缺但齐
                //已完整：所有交接单都是已核验，且所有交接单都是已完整

                ids = customerServiceDocHandoverMapper.searchWholeLevelTotalV2(wholeLevel);
            }
            log.info("wholeLevelSearchV2 idsCount={}", ObjectUtils.isEmpty(ids) ? 0 : ids.size());

            if (ObjectUtils.isEmpty(ids)) {
                fail = true;
            }
        }

        return CommonIdsSearchVO.builder()
                .needSearch(needSearch)
                .fail(fail)
                .ids(ids)
                .build();
    }

    @Override
    public List<CustomerServiceInAccount> getByPeriodIdList(List<Long> periodIds) {
        if (ObjectUtils.isEmpty(periodIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServiceInAccount>().in(CustomerServiceInAccount::getCustomerServicePeriodMonthId, periodIds)
                .eq(CustomerServiceInAccount::getIsDel, false));
    }

    @Override
    @Transactional
    public void updateByCommonNotice(OpenApiData openApiData, CommonInAccountVO commonInAccountVO, CustomerServiceInAccount customerServiceInAccount) {
        Map<String, String> operContent = new HashMap<>();
        CustomerServiceInAccount update = new CustomerServiceInAccount();
        update.setId(customerServiceInAccount.getId());
        if (!StringUtils.isEmpty(commonInAccountVO.getEmployees())) {
            update.setTaxReportCount(Integer.parseInt(commonInAccountVO.getEmployees()));
            operContent.put("个税申报人数", commonInAccountVO.getEmployees());
        }
        if (!StringUtils.isEmpty(commonInAccountVO.getTotalTax())) {
            update.setTaxReportSalaryTotal(new BigDecimal(commonInAccountVO.getTotalTax()));
            operContent.put("本年个税申报工资总额", commonInAccountVO.getTotalTax());
        }
        if (!StringUtils.isEmpty(commonInAccountVO.getNetProfit())) {
            update.setProfitTotal(new BigDecimal(commonInAccountVO.getNetProfit()));
            operContent.put("本年累计会计利润", commonInAccountVO.getNetProfit());
        }
        if (!StringUtils.isEmpty(commonInAccountVO.getOperatingCosts())) {
            update.setMajorCostTotal(new BigDecimal(commonInAccountVO.getOperatingCosts()));
            operContent.put("本年累计主营成本", commonInAccountVO.getOperatingCosts());
        }
        if (!StringUtils.isEmpty(commonInAccountVO.getOperatingRevenue())) {
            update.setMajorIncomeTotal(new BigDecimal(commonInAccountVO.getOperatingRevenue()));
            operContent.put("本年累计主营收入", commonInAccountVO.getOperatingRevenue());
        }
        updateById(update);
        try {
            SysUser user = remoteUserService.getUserByNickName(openApiData.getCreateBy(), openApiData.getDeptId(), SecurityConstants.INNER).getDataThrowException(false);
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(customerServiceInAccount.getId())
                            .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                            .setDeptId(null)
                            .setOperType("系统利润同步02")
                            .setOperName(openApiData.getCreateBy())
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setOperUserId(Objects.isNull(user) ? null : user.getUserId())
                            .setCreateBy(Constants.YSB_CREATE_BY));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void updateByCommonNoticeV2(OpenApiData openApiData, CommonInAccountVO commonInAccountVO, CustomerServiceInAccount customerServiceInAccount) {
        Map<String, String> operContent = new HashMap<>();
        CustomerServiceInAccount update = new CustomerServiceInAccount();
        update.setId(customerServiceInAccount.getId());
        update.setRpaSearchTime(LocalDateTime.now());
        if (StringUtils.isEmpty(commonInAccountVO.getEmployees()) || StringUtils.isEmpty(commonInAccountVO.getTotalTax()) || StringUtils.isEmpty(commonInAccountVO.getNetProfit())
                || StringUtils.isEmpty(commonInAccountVO.getOperatingCosts()) || StringUtils.isEmpty(commonInAccountVO.getOperatingRevenue())) {
            // 只要有一个值是空值，不处理交付单的值，并rpa执行结果设置为（异常），将回传数字写入备注中。key:value，key:value，key:value，key:value，key:value
            update.setRpaExeResult(InAccountRpaExeResult.F.getCode());
            String rpaRemark = "个税申报人数：" + (StringUtils.isEmpty(commonInAccountVO.getEmployees()) ? "null" : commonInAccountVO.getEmployees()) + "，" +
                    "本年个税申报工资总额：" + (StringUtils.isEmpty(commonInAccountVO.getTotalTax()) ? "null" : commonInAccountVO.getTotalTax()) + "，" +
                    "本年累计会计利润：" + (StringUtils.isEmpty(commonInAccountVO.getNetProfit()) ? "null" : commonInAccountVO.getNetProfit()) + "，" +
                    "本年累计主营成本：" + (StringUtils.isEmpty(commonInAccountVO.getOperatingCosts()) ? "null" : commonInAccountVO.getOperatingCosts()) + "，" +
                    "本年累计主营收入：" + (StringUtils.isEmpty(commonInAccountVO.getOperatingRevenue()) ? "null" : commonInAccountVO.getOperatingRevenue());
            update.setRpaRemark(rpaRemark);
            updateById(update);
            operContent.put("RPA执行结果", "失败");
            try {
                SysUser user = remoteUserService.getUserByNickName(openApiData.getCreateBy(), openApiData.getDeptId(), SecurityConstants.INNER).getDataThrowException(false);
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(customerServiceInAccount.getId())
                                .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                                .setDeptId(null)
                                .setOperType(openApiData.getOperType() == 1 ? "发起取数请求" : "定时取数")
                                .setOperName(openApiData.getCreateBy())
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setOperRemark(rpaRemark)
                                .setOperUserId(Objects.isNull(user) ? null : user.getUserId())
                                .setCreateBy(Constants.YSB_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        } else {
            operContent.put("RPA执行结果", "成功");
            if (!StringUtils.isEmpty(commonInAccountVO.getEmployees())) {
                update.setTaxReportCount(Integer.parseInt(commonInAccountVO.getEmployees()));
                operContent.put("个税申报人数", commonInAccountVO.getEmployees());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getTotalTax())) {
                update.setTaxReportSalaryTotal(new BigDecimal(commonInAccountVO.getTotalTax()));
                operContent.put("本年个税申报工资总额", commonInAccountVO.getTotalTax());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getNetProfit())) {
                update.setProfitTotal(new BigDecimal(commonInAccountVO.getNetProfit()));
                operContent.put("本年累计会计利润", commonInAccountVO.getNetProfit());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getOperatingCosts())) {
                update.setMajorCostTotal(new BigDecimal(commonInAccountVO.getOperatingCosts()));
                operContent.put("本年累计主营成本", commonInAccountVO.getOperatingCosts());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getOperatingRevenue())) {
                update.setMajorIncomeTotal(new BigDecimal(commonInAccountVO.getOperatingRevenue()));
                operContent.put("本年累计主营收入", commonInAccountVO.getOperatingRevenue());
            }
            update.setRpaExeResult(InAccountRpaExeResult.S.getCode());
            update.setRpaRemark("");
            updateById(update);
            try {
                SysUser user = remoteUserService.getUserByNickName(openApiData.getCreateBy(), openApiData.getDeptId(), SecurityConstants.INNER).getDataThrowException(false);
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(customerServiceInAccount.getId())
                                .setBusinessType(BusinessLogBusinessType.IN_ACCOUNT.getCode())
                                .setDeptId(null)
                                .setOperType(openApiData.getOperType() == 1 ? "发起取数请求" : "定时取数")
                                .setOperName(openApiData.getCreateBy())
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setOperUserId(Objects.isNull(user) ? null : user.getUserId())
                                .setCreateBy(Constants.YSB_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    public void rpaInAccountTask() {
        Integer yesterday = Integer.parseInt(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        List<Long> needRpaCustomerServiceIds = customerServiceInAccountUpdateRecordService.list(new LambdaQueryWrapper<CustomerServiceInAccountUpdateRecord>()
                .eq(CustomerServiceInAccountUpdateRecord::getOperateDate, yesterday).select(CustomerServiceInAccountUpdateRecord::getCustomerServiceId))
                .stream().map(CustomerServiceInAccountUpdateRecord::getCustomerServiceId).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(needRpaCustomerServiceIds)) {
            return;
        }
        List<CustomerInAccountMaxPeriodDTO> customerMaxPeriodList = baseMapper.selectCustomerInAccountMaxPeriod(needRpaCustomerServiceIds);
        if (ObjectUtils.isEmpty(customerMaxPeriodList)) {
            return;
        }
        Map<Long, CCustomerService> customerServiceMap = cCustomerServiceMapper.selectBatchIds(customerMaxPeriodList.stream().map(CustomerInAccountMaxPeriodDTO::getCustomerServiceId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
        Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerMaxPeriodList.stream().map(CustomerInAccountMaxPeriodDTO::getCustomerServiceId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
        customerMaxPeriodList.forEach(row -> {
            CCustomerService customerService = customerServiceMap.get(row.getCustomerServiceId());
            if (!Objects.isNull(customerService) && !Objects.isNull(customerService.getBusinessDeptId())) {
                Long businessDeptId = customerService.getBusinessDeptId();
                String groupId = businessGroupProperties.getMap().get(businessDeptId);
                if (!StringUtils.isEmpty(groupId)) {
                    GroupMap groupMap = GroupMap.getByGroupId(groupId);
                    if (!Objects.isNull(groupMap)) {
                        String platType = customerServiceTagMap.getOrDefault(row.getCustomerServiceId(), Lists.newArrayList()).stream().map(TagDTO::getId).collect(Collectors.toList())
                                        .contains(specialTagProperties.getYq()) ? "亿企赢" : "易捷账";
                        remoteThirdpartService.rpaInAccount(RpaInAccountVO.builder()
                                .creditCode(customerService.getCreditCode())
                                .groupId(groupId)
                                .groupName(groupMap.getGroupName())
                                .operator("系统")
                                .operType(2)
                                .platType(platType)
                                .period(row.getMaxPeriod())
                                .taxNumber(customerService.getTaxNumber())
                                .customerName(customerService.getCustomerName())
                                .customerServiceId(customerService.getId())
                                .build(), SecurityConstants.INNER);
                    }
                }
            }
        });
    }

    @Override
    public List<RemoteCustomerInAccountMaxPeriodDTO> getCustomerInAccountMaxPeriod(List<Long> customerServiceIds) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return Collections.emptyList();
        }
        List<CustomerInAccountMaxPeriodDTO> customerMaxPeriodList = baseMapper.selectCustomerInAccountMaxPeriod(customerServiceIds);
        if (ObjectUtils.isEmpty(customerMaxPeriodList)) {
            return Collections.emptyList();
        }
        Map<Long, CCustomerService> customerServiceMap = cCustomerServiceMapper.selectBatchIds(customerMaxPeriodList.stream().map(CustomerInAccountMaxPeriodDTO::getCustomerServiceId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
        Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerMaxPeriodList.stream().map(CustomerInAccountMaxPeriodDTO::getCustomerServiceId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
        return customerMaxPeriodList.stream().map(row -> {
            CCustomerService customerService = customerServiceMap.get(row.getCustomerServiceId());
            Long businessDeptId = customerService.getBusinessDeptId();
            String groupId = businessGroupProperties.getMap().get(businessDeptId);
            GroupMap groupMap = StringUtils.isEmpty(groupId) ? null : GroupMap.getByGroupId(groupId);
            String platType = customerServiceTagMap.getOrDefault(row.getCustomerServiceId(), Lists.newArrayList()).stream().map(TagDTO::getId).collect(Collectors.toList())
                    .contains(specialTagProperties.getYq()) ? "亿企赢" : "易捷账";
            return new RemoteCustomerInAccountMaxPeriodDTO().setPeriod(row.getMaxPeriod())
                    .setGroupId(groupId)
                    .setGroupName(Objects.isNull(groupMap) ? "" : groupMap.getGroupName())
                    .setId(row.getCustomerServiceId())
                    .setPlatType(platType);
        }).collect(Collectors.toList());
    }

    @Override
    public boolean checkInAccountIsEnd(Long customerServiceId, Integer period) {
        return count(new LambdaQueryWrapper<CustomerServiceInAccount>()
                .eq(CustomerServiceInAccount::getCustomerServiceId, customerServiceId)
                .eq(CustomerServiceInAccount::getPeriod, period)
                .eq(CustomerServiceInAccount::getIsDel, false)
                .eq(CustomerServiceInAccount::getStatus, InAccountStatus.DONE.getCode())) > 0;
    }

    @Override
    @Transactional
    public void urgeInAccount(Long inAccountId, Long userId, Long deptId) {
        CustomerServiceInAccount customerServiceInAccount = getById(inAccountId);
        if (Objects.isNull(customerServiceInAccount) || customerServiceInAccount.getIsDel()) {
            throw new ServiceException("入账交付单不存在");
        }
        CCustomerService customerService = cCustomerServiceMapper.selectById(customerServiceInAccount.getCustomerServiceId());
        if (Objects.isNull(customerService)) {
            throw new ServiceException("客户不存在");
        }
        // 查询该客户当前没有入账时间的所有账期id
        List<Long> noInTimePeriodIds = baseMapper.selectNoInTimePeriodIds(customerService.getId());
        if (ObjectUtils.isEmpty(noInTimePeriodIds)) {
            throw new ServiceException("该客户无需催账");
        }
        List<CustomerServicePeriodMonth> periodMonthList = customerServicePeriodMonthMapper.selectBatchIds(noInTimePeriodIds);
        if (ObjectUtils.isEmpty(periodMonthList)) {
            throw new ServiceException("账期不存在");
        }
        List<Long> accountingDeptIdList = periodMonthList.stream().map(CustomerServicePeriodMonth::getAccountingDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (!Objects.isNull(customerService.getAccountingDeptId()) && !accountingDeptIdList.contains(customerService.getAccountingDeptId())) {
            accountingDeptIdList.add(customerService.getAccountingDeptId());
        }
        if (ObjectUtils.isEmpty(accountingDeptIdList)) {
            throw new ServiceException("账期会计小组为空");
        }
        List<SysEmployee> employeeList = remoteEmployeeService.getBatchEmployeeByDeptIds(accountingDeptIdList).getDataThrowException();
        if (ObjectUtils.isEmpty(employeeList)) {
            throw new ServiceException("账期会计为空");
        }
        if (redisService.incrementWithTimeToLive("inAccount:urge:" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ":" + customerService.getId(), 60 * 60 * 24, TimeUnit.SECONDS) > 1) {
            throw new ServiceException("一个客户一天只能催账一次");
        }
        Map<Long, String> deptNameMap = remoteDeptService.getByDeptIds(accountingDeptIdList).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);
        employeeList.forEach(employee ->
            asyncService.asyncSendMessage(employee.getUserId(), employee.getEmployeeId(), employee.getEmployeeName(), employee.getDeptId(), deptNameMap.getOrDefault(employee.getDeptId(), ""),
                    String.format("%s 催账了，请尽快完成", customerService.getCustomerName()),
                    operateUserInfoDTO.getOperName(), 1)
        );
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(customerService.getId())
                            .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                            .setDeptId(deptId)
                            .setOperType("催账通知")
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setOperUserId(operateUserInfoDTO.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public List<RemoteCustomerInAccountMaxPeriodDTO> getCustomerInAccountGroupRelation(List<Long> customerServiceIds) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return Collections.emptyList();
        }
        Map<Long, CCustomerService> customerServiceMap = cCustomerServiceMapper.selectBatchIds(customerServiceIds)
                .stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
        Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServiceIds, TagBusinessType.CUSTOMER_SERVICE);
        return customerServiceIds.stream().map(row -> {
            CCustomerService customerService = customerServiceMap.get(row);
            Long businessDeptId = customerService.getBusinessDeptId();
            String groupId = businessGroupProperties.getMap().get(businessDeptId);
            GroupMap groupMap = StringUtils.isEmpty(groupId) ? null : GroupMap.getByGroupId(groupId);
            String platType = customerServiceTagMap.getOrDefault(row, Lists.newArrayList()).stream().map(TagDTO::getId).collect(Collectors.toList())
                    .contains(specialTagProperties.getYq()) ? "亿企赢" : "易捷账";
            return new RemoteCustomerInAccountMaxPeriodDTO()
                    .setGroupId(groupId)
                    .setGroupName(Objects.isNull(groupMap) ? "" : groupMap.getGroupName())
                    .setId(row)
                    .setPlatType(platType);
        }).collect(Collectors.toList());
    }

    //合并多个搜索
    public static CommonIdsSearchVO mergeCommonIdsSearchData(List<CommonIdsSearchVO> commonIdsSearchVOS) {
        CommonIdsSearchVO result = CommonIdsSearchVO.builder()
                .fail(false)
                .ids(Lists.newArrayList())
                .needSearch(false)
                .build();

        if (ObjectUtils.isEmpty(commonIdsSearchVOS)) {
            return result;
        }

        int index = 0;
        for (CommonIdsSearchVO commonIdsSearchVO : commonIdsSearchVOS) {
            if (!commonIdsSearchVO.getNeedSearch()) {
                continue;
            }

            if (commonIdsSearchVO.getFail()) {
                result.setFail(true);
                break;
            }

            result.setNeedSearch(true);

            //取交集
            if (index == 0) {
                result.getIds().addAll(commonIdsSearchVO.getIds());
            } else {
                result.getIds().retainAll(commonIdsSearchVO.getIds());
            }

            index++;
        }

        return result;
    }

    //处理 完整度搜索
    /*
     * 取账期对应的材料交接单做逻辑判断：
     * 无材料交接单：无材料
     * 有材料交接单且有至少一条是有缺失：有缺失
     * 有材料交接单且无有缺失且有至少一条是缺但齐：缺但齐
     * 不符合以上条件：完整
     * 状态不是“无材料”时，提供详情链接，点击打开月度材料详情抽屉
     */
    public CommonIdsSearchVO wholeLevelSearch(Integer wholeLevel) {
        boolean needSearch = true;//是否需要搜索
        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
        List<Long> ids = null;

        if (wholeLevel == null) {
            needSearch = false;
        } else {
            ids = customerServiceDocHandoverMapper.searchWholeLevelTotal(wholeLevel);

            if (ObjectUtils.isEmpty(ids)) {
                fail = true;
            }
        }

        return CommonIdsSearchVO.builder()
                .needSearch(needSearch)
                .fail(fail)
                .ids(ids)
                .build();
    }

    //处理完整度
    public static Integer handleWholeLevel(List<CustomerServiceDocHandover> source) {
        /*int wholeLevel;

        if (ObjectUtils.isEmpty(source)) {
            wholeLevel = WholeLevel.NOT_DATA.getCode();
        } else {
            if (source.stream().anyMatch(ddd -> ddd.getWholeLevel() == null)
                    || source.stream().anyMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.LACK_AND_COMPLETED_NEED_ADD.getCode()))) {
                wholeLevel = WholeLevel.LACK_AND_COMPLETED_NEED_ADD.getCode();
            } else {
                if (source.stream().anyMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.LACK_AND_COMPLETED.getCode()))) {
                    wholeLevel = WholeLevel.LACK_AND_COMPLETED.getCode();
                } else {
                    wholeLevel = WholeLevel.COMPLETED.getCode();
                }
            }
        }

        return wholeLevel;*/
        return null;
    }

    //处理完整度
    public static Integer handleWholeLevelV2(List<CustomerServiceDocHandover> source) {
        return handleWholeLevelV3(source);

        /*int wholeLevel;

        if (ObjectUtils.isEmpty(source)) {
            //未提交材料：无材料交接单
            wholeLevel = WholeLevel.UN_SUBMIT_DATA.getCode();
        } else {
            if (source.stream().allMatch(CustomerServiceDocHandoverServiceImpl::checkAllContentUnHas)) {
                //无材料：有材料交接单，且所有单据的所有材料都是空
                wholeLevel = WholeLevel.SUBMIT_NO_DATA.getCode();
            } else if (
                    (source.stream().anyMatch(ddd -> ddd.getWholeLevel() == null) || source.stream().anyMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.LACK_AND_COMPLETED_NEED_ADD.getCode())))
                            && source.stream().noneMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.LACK_AND_COMPLETED.getCode()))) {
                //有缺失：有材料交接单，且至少一个交接单是有缺失，且没有“缺但齐”
                wholeLevel = WholeLevel.LACK_AND_COMPLETED_NEED_ADD.getCode();
            } else if (source.stream().anyMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.LACK_AND_COMPLETED.getCode()))
                    && source.stream().noneMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.LACK_AND_COMPLETED_NEED_ADD.getCode()))
            ) {
                //缺但齐：有材料交接单，且至少一个交接单的结果是缺但齐，且没有“有缺失”
                wholeLevel = WholeLevel.LACK_AND_COMPLETED.getCode();
            } else if (source.stream().anyMatch(ddd -> !CustomerServiceDocHandoverServiceImpl.checkAllContentUnHas(ddd) && Objects.equals(ddd.getWholeLevel(), WholeLevel.COMPLETED.getCode()))) {
                //已完整：有材料交接单，且至少一个交接单的材料模块不是空，且结果是已完整。
                wholeLevel = WholeLevel.COMPLETED.getCode();
            } else {
                wholeLevel = WholeLevel.UN_KNOW.getCode();
            }
        }

        return wholeLevel;*/
    }

    //处理完整度

    /*
     * 材料完整度分2种：
     *
     * 1、材料交接单的完整度，在核验之后才有，状态为：无材料、有缺失、缺但齐、已完整；
     *
     * 2、账期材料完整度，是根据账期下关联的多个材料交接单逻辑计算的，逻辑和状态展示顺序如下：
     *
     *
     *
     * 未提交材料：无材料交接单
     * 待核验：有至少一条交接单是待核验
     * 无材料：所有交接单都是已核验，且都是无材料
     * 有缺失：所有交接单都是已核验，且有一条是有缺失
     * 缺但齐：所有交接单都是已核验，且没有交接单是已缺失，且有一条是缺但齐
     * 已完整：所有交接单都是已核验，且所有交接单都是已完整
     *
     *
     * 会用到材料交接单的完整度的只有：
     *
     * 【材料交接】列表、详情
     *
     * 【月度材料详情】抽屉的材料交接单列表
     *
     *
     *
     * 以下场景里的都是用账期材料完整度：
     *
     * 【月度汇总】列表、月度汇总列表搜索-材料完整度
     *
     * 【入账】列表，入账列表搜索-材料完整度
     *
     * 【服务详情】抽屉-账期明细列表
     */
    public static Integer handleWholeLevelV3(List<CustomerServiceDocHandover> source) {
        int wholeLevel;

        if (ObjectUtils.isEmpty(source)) {
            //未提交材料：无材料交接单
            wholeLevel = WholeLevel.UN_SUBMIT_DATA.getCode();
        } else {
            if (source.stream().anyMatch(ddd -> Objects.equals(ddd.getStatus(), DocHandoverStatus.NEED_VERIFICATION.getCode()))) {
                //待核验：有至少一条交接单是待核验
                wholeLevel = WholeLevel.WAIT_CHECK.getCode();
            } else if (source.stream().allMatch(ddd -> Objects.equals(ddd.getStatus(), DocHandoverStatus.ALREADY_VERIFICATION.getCode()))
                    && source.stream().allMatch(CustomerServiceDocHandoverServiceImpl::checkAllContentUnHas)
            ) {
                //无材料：所有交接单都是已核验，且都是无材料
                wholeLevel = WholeLevel.SUBMIT_NO_DATA.getCode();
            } else if (source.stream().allMatch(ddd -> Objects.equals(ddd.getStatus(), DocHandoverStatus.ALREADY_VERIFICATION.getCode()))
                    && source.stream().anyMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.LACK_AND_COMPLETED_NEED_ADD.getCode()))
            ) {
                //有缺失：所有交接单都是已核验，且有一条是有缺失
                wholeLevel = WholeLevel.LACK_AND_COMPLETED_NEED_ADD.getCode();
            } else if (source.stream().allMatch(ddd -> Objects.equals(ddd.getStatus(), DocHandoverStatus.ALREADY_VERIFICATION.getCode()))
                    && source.stream().noneMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.LACK_AND_COMPLETED_NEED_ADD.getCode()))
                    && source.stream().anyMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.LACK_AND_COMPLETED.getCode()))
            ) {
                //缺但齐：所有交接单都是已核验，且没有交接单是已缺失，且有一条是缺但齐
                wholeLevel = WholeLevel.LACK_AND_COMPLETED.getCode();
            } else if (source.stream().allMatch(ddd -> Objects.equals(ddd.getStatus(), DocHandoverStatus.ALREADY_VERIFICATION.getCode()))
                    && source.stream().allMatch(ddd -> Objects.equals(ddd.getWholeLevel(), WholeLevel.COMPLETED.getCode()))
            ) {
                //已完整：所有交接单都是已核验，且所有交接单都是已完整
                wholeLevel = WholeLevel.COMPLETED.getCode();
            } else {
                wholeLevel = WholeLevel.UN_KNOW.getCode();
            }
        }

        return wholeLevel;
    }

    //处理状态
    private Integer handleStatus(LocalDate inTime, LocalDate endTime) {
        int status;

        if (inTime == null && endTime == null) {
            status = InAccountStatus.UN_IN.getCode();
        } else {
            if (inTime == null) {
                status = InAccountStatus.UN_KNOW.getCode();
            } else {
                if (endTime == null) {
                    status = InAccountStatus.IN_UN_DO.getCode();
                } else {
                    status = InAccountStatus.DONE.getCode();
                }
            }
        }

        return status;
    }

    private CustomerServiceInAccount covCustomerServiceInAccountByCustomerAndPeriod(CCustomerService cCustomerService, CustomerServicePeriodMonth customerServicePeriodMonth) {
        CustomerServiceInAccount newEntry = new CustomerServiceInAccount();
        newEntry.setIsDel(Boolean.FALSE);
        newEntry.setStatus(InAccountStatus.UN_IN.getCode());
        newEntry.setTitle(String.format(TITLE_BASE, customerServicePeriodMonth.getPeriod()));

        newEntry.setCustomerServiceId(cCustomerService.getId());
        newEntry.setCustomerName(cCustomerService.getCustomerName());
        newEntry.setCreditCode(cCustomerService.getCreditCode());
        newEntry.setServiceNumber(cCustomerService.getServiceNumber());
        newEntry.setServiceType(cCustomerService.getServiceType());
        newEntry.setTaxType(cCustomerService.getTaxType());

        newEntry.setCustomerServicePeriodMonthId(customerServicePeriodMonth.getId());
        newEntry.setPeriod(customerServicePeriodMonth.getPeriod());

        return newEntry;
    }

    //如果这个入账交付单是由补账生成的，可能需要更新补账的交付状态
    private void maybeUpdateRepairAccountDeliverStatus(CustomerServiceInAccount customerServiceInAccount) {
        CustomerServicePeriodMonth customerServicePeriodMonth = iCustomerServicePeriodMonthService.getById(customerServiceInAccount.getCustomerServicePeriodMonthId());
        if (customerServicePeriodMonth != null
                && Objects.equals(customerServicePeriodMonth.getAddFromType(), CustomerServiceRepairAccountServiceImpl.ADD_FROM_TYPE) && customerServicePeriodMonth.getAddFromId() != null) {

            List<CustomerServicePeriodMonth> addFromPeriods = iCustomerServicePeriodMonthService.list(
                    new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                            .eq(CustomerServicePeriodMonth::getAddFromType, CustomerServiceRepairAccountServiceImpl.ADD_FROM_TYPE)
                            .eq(CustomerServicePeriodMonth::getAddFromId, customerServicePeriodMonth.getAddFromId())
            );

            if (!ObjectUtils.isEmpty(addFromPeriods)) {
                List<CustomerServiceInAccount> hisInAccounts = list(
                        new LambdaQueryWrapper<CustomerServiceInAccount>()
                                .in(CustomerServiceInAccount::getCustomerServicePeriodMonthId, addFromPeriods.stream().map(CustomerServicePeriodMonth::getId).distinct().collect(Collectors.toList()))
                );

                /*
                 * 当补账还是待分派时，交付状态=待交付
                 * 当补账是已分派，且所有关联的账期的入账交付单都是未入账未结账时，交付状态=待交付
                 * 当补账是已分派，且所有关联的账期的入账交付单都是已入账已结账时，交付状态=已完成
                 * 其他状态为交付中
                 */

                /*
                 * 阿苏：
                 * 提交：触发交付状态=待交付
                 * 分派：轮一遍入账交付单，待交付/已完成
                 * 入账交付单编辑保存：轮一遍入账交付单，待交付/交付中/已完成
                 */

                RepairAccountDeliverStatus repairAccountDeliverStatus = null;
                if (hisInAccounts.stream().allMatch(r -> Objects.equals(r.getStatus(), InAccountStatus.UN_IN.getCode()))) {
                    //待交付
                    repairAccountDeliverStatus = RepairAccountDeliverStatus.NEED_DELIVER;
                } else if (hisInAccounts.stream().allMatch(r -> Objects.equals(r.getStatus(), InAccountStatus.DONE.getCode()))) {
                    //已完成
                    repairAccountDeliverStatus = RepairAccountDeliverStatus.DONE;
                } else {
                    //交付中
                    repairAccountDeliverStatus = RepairAccountDeliverStatus.ING;
                }

                //更新交付状态
                CustomerServiceRepairAccount customerServiceRepairAccount = new CustomerServiceRepairAccount();
                customerServiceRepairAccount.setId(customerServicePeriodMonth.getAddFromId());
                customerServiceRepairAccount.setDeliverStatus(repairAccountDeliverStatus.getCode());
                customerServiceRepairAccountMapper.updateById(
                        customerServiceRepairAccount
                );
            }
        }
    }
}
