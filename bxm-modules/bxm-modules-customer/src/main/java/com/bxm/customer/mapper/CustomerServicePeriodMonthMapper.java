package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodDTO;
import com.bxm.customer.api.domain.vo.RemoteCreditCodePeriodVO;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.dto.*;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierWaitCreateStatisticDTO;
import com.bxm.customer.domain.dto.accoutingCashier.BankPaymentResultCountDTO;
import com.bxm.customer.domain.dto.inAccount.CustomerInAccountMaxPeriodDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderPeriodUploadDTO;
import com.bxm.customer.domain.dto.workBench.CustomerServicePeriodMonthMiniListDTO;
import com.bxm.customer.domain.dto.workBench.CustomerServicePeriodMonthSimpleDTO;
import com.bxm.customer.domain.dto.workBench.InAccountMiniListDTO;
import com.bxm.customer.domain.dto.workBench.ManagerCommonCountDTO;
import com.bxm.customer.domain.vo.CustomerServicePeriodMonthSearchVO;
import com.bxm.customer.domain.vo.workBench.CustomerServicePeriodMonthMiniListSearchVO;
import com.bxm.customer.domain.vo.workBench.CustomerServicePeriodMonthNoAccountingNoAdvisorVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 客户服务月度账期Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@Mapper
public interface CustomerServicePeriodMonthMapper extends BaseMapper<CustomerServicePeriodMonth> {
    /**
     * 查询客户服务月度账期
     *
     * @param id 客户服务月度账期主键
     * @return 客户服务月度账期
     */
    public CustomerServicePeriodMonth selectCustomerServicePeriodMonthById(Long id);

    /**
     * 查询客户服务月度账期列表
     *
     * @param customerServicePeriodMonth 客户服务月度账期
     * @return 客户服务月度账期集合
     */
    public List<CustomerServicePeriodMonth> selectCustomerServicePeriodMonthList(CustomerServicePeriodMonth customerServicePeriodMonth);

    /**
     * 新增客户服务月度账期
     *
     * @param customerServicePeriodMonth 客户服务月度账期
     * @return 结果
     */
    public int insertCustomerServicePeriodMonth(CustomerServicePeriodMonth customerServicePeriodMonth);

    /**
     * 修改客户服务月度账期
     *
     * @param customerServicePeriodMonth 客户服务月度账期
     * @return 结果
     */
    public int updateCustomerServicePeriodMonth(CustomerServicePeriodMonth customerServicePeriodMonth);

    /**
     * 删除客户服务月度账期
     *
     * @param id 客户服务月度账期主键
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthById(Long id);

    /**
     * 批量删除客户服务月度账期
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthByIds(Long[] ids);

    Long selectCountBySql(@Param("monthConditionSql") String monthConditionSql);

    List<CustomerServicePeriodMonth> selectDataBySql(@Param("monthConditionSql") String monthConditionSql);

    List<CustomerServicePeriodMonthDTO> customerServicePeriodMonthList(
            IPage<CustomerServicePeriodMonthDTO> result,
            @Param("tagIncludeFlag") Integer tagIncludeFlag,
            @Param("periodMonthIdsForTag") List<Long> periodMonthIdsForTag,
            @Param("searchIds") List<Long> searchIds,
            @Param("vo") CustomerServicePeriodMonthSearchVO vo
    );

    List<CustomerServicePeriodMonthDTO> customerServicePeriodMonthListWithServiceStatus(
            IPage<CustomerServicePeriodMonthDTO> result,
            @Param("tagIncludeFlag") Integer tagIncludeFlag,
            @Param("periodMonthIdsForTag") List<Long> periodMonthIdsForTag,
            @Param("searchIds") List<Long> searchIds,
            @Param("vo") CustomerServicePeriodMonthSearchVO vo
    );

    List<CustomerServicePeriodMonthDTO> listWithServiceStatusInAuth(
            IPage<CustomerServicePeriodMonthDTO> result,
            @Param("tagIncludeFlag") Integer tagIncludeFlag,
            @Param("periodMonthIdsForTag") List<Long> periodMonthIdsForTag,
            @Param("searchIds") List<Long> searchIds,
            @Param("vo") CustomerServicePeriodMonthSearchVO vo,
            @Param("userDept") UserDeptDTO userDept,
            @Param("customerServiceIds") List<Long> customerServiceIds);

    List<CustomerServicePeriodMonthDTO> listWithServiceStatusInAuthV2(
            IPage<CustomerServicePeriodMonthDTO> result,
            @Param("tagIncludeFlag") Integer tagIncludeFlag,
            @Param("periodMonthIdsForTag") List<Long> periodMonthIdsForTag,
            @Param("searchIds") List<Long> searchIds,
            @Param("vo") CustomerServicePeriodMonthSearchVO vo,
            @Param("userDept") UserDeptDTO userDept,
            @Param("customerServiceIds") List<Long> customerServiceIds,
            @Param("customerServiceTagIncludeFlag") Integer customerServiceTagIncludeFlag,
            @Param("customerServiceIdForTag") List<Long> customerServiceIdForTag);

    List<CustomerServicePeriodMonthDTO> listWithServiceStatusInAuthV3(
            IPage<CustomerServicePeriodMonthDTO> result,
            @Param("tagIncludeFlag") Integer tagIncludeFlag,
            @Param("periodMonthIdsForTag") List<Long> periodMonthIdsForTag,
            @Param("vo") CustomerServicePeriodMonthSearchVO vo,
            @Param("userDept") UserDeptDTO userDept,
            @Param("customerServiceIds") List<Long> customerServiceIds,
            @Param("customerServiceTagIncludeFlag") Integer customerServiceTagIncludeFlag,
            @Param("customerServiceIdForTag") List<Long> customerServiceIdForTag);

    List<CommonDeptCountDTO> accountingDeptCountWithServiceStatusInAuth(@Param("tagIncludeFlag") Integer tagIncludeFlag,
                                                                        @Param("periodMonthIdsForTag") List<Long> periodMonthIdsForTag,
                                                                        @Param("searchIds") List<Long> searchIds,
                                                                        @Param("vo") CustomerServicePeriodMonthSearchVO vo,
                                                                        @Param("userDept") UserDeptDTO userDept);

    List<CommonDeptCountDTO> advisorDeptCountWithServiceStatusInAuth(@Param("tagIncludeFlag") Integer tagIncludeFlag,
                                                                     @Param("periodMonthIdsForTag") List<Long> periodMonthIdsForTag,
                                                                     @Param("searchIds") List<Long> searchIds,
                                                                     @Param("vo") CustomerServicePeriodMonthSearchVO vo,
                                                                     @Param("userDept") UserDeptDTO userDept);

    void savePeriodMonthTagRelationByCustomerIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                 @Param("startPeriod") Integer startPeriod,
                                                 @Param("endPeriod") Integer endPeriod);

    List<Long> searchAccounting(@Param("accountingEmployee") String accountingEmployee);

    List<Long> searchAdvisor(@Param("advisorEmployee") String advisorEmployee);

    List<AccountingInfoSourceDTO> selectAccountingInfoByIds(@Param("ids") List<Long> ids);

    List<AccountingTopInfoSourceDTO> selectAccountingTopInfoByIds(@Param("ids") List<Long> ids);

    List<AdvisorInfoSourceDTO> selectAdvisorInfoByIds(@Param("ids") List<Long> ids);

    void savePeriodMonthAdvisorEmployeeByCustomerIds(@Param("customerServiceIds") List<Long> customerServiceIds);

    void savePeriodMonthAccountingEmployeeByCustomerIds(@Param("customerServiceIds") List<Long> customerServiceIds);

    void savePeriodMonthTaxCheckByExistsTaxCheckCustomerIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                            @Param("startPeriod") Integer startPeriod,
                                                            @Param("endPeriod") Integer endPeriod);

    void savePeriodMonthTaxCheckByNotExistsTaxCheckSmallCustomerIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                                    @Param("startPeriod") Integer startPeriod,
                                                                    @Param("endPeriod") Integer endPeriod);

    void savePeriodMonthTaxCheckByNotExistsTaxCheckCommlyCustomerIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                                     @Param("startPeriod") Integer startPeriod,
                                                                     @Param("endPeriod") Integer endPeriod);

    void deletePeriodMonthTagRelationByPeriodIds(@Param("periodIds") List<Long> periodIds);

    void deletePeriodMonthTaxCheckByPeriodIds(@Param("periodIds") List<Long> periodIds);

    @Select("select min(period) from c_customer_service_period_month where customer_service_id = #{customerServiceId}")
    @ResultType(Integer.class)
    Integer selectMinPeriod(@Param("customerServiceId") Long customerServiceId);

    List<CustomerServicePeriodMonth> selectPeriodMonthByYearAndUserDept(@Param("year") Integer year,
                                                                        @Param("statisticTaxType") Integer statisticTaxType,
                                                                        @Param("userDeptDTO") UserDeptDTO userDeptDTO, @Param("queryDeptIds") List<Long> queryDeptIds);

    List<PeriodCountDTO> selectPeriodMonthCountByYearAndUserDept(@Param("year") Integer year,
                                                                 @Param("statisticTaxType") Integer statisticTaxType,
                                                                 @Param("userDeptDTO") UserDeptDTO userDeptDTO, @Param("queryDeptIds") List<Long> queryDeptIds);


    List<CustomerServicePeriodMonth> selectNoInAccount();

    List<CustomerServicePeriodMonth> selectNoInAccountByCustomerService(@Param("customerServiceId") Long customerServiceId);

    List<CustomerServicePeriodMonth> selectNoInAccountByCustomerServiceBatch(@Param("customerServiceIds") List<Long> customerServiceIds);

    List<CustomerServicePeriodMonth> getCustomerPeriodByListCreditCodeAndPeriod(@Param("voList") List<RemoteCreditCodePeriodVO> voList);

    List<Long> searchByDeptIds(@Param("deptIds") List<Long> deptIds);

    void saveNewPeriod(@Param("nowPeriod") Integer nowPeriod, @Param("year") Integer year);

    List<Long> selectPeriodIdsBatchByCreditCodeAndPeriod(@Param("dataList") List<SettlementOrderPeriodUploadDTO> dataList, @Param("isSupplement") Boolean isSupplement);

    List<CustomerServicePeriodMonth> selectBatchByCreditCodeAndPeriod(@Param("dataList") List<SettlementOrderPeriodUploadDTO> dataList);

    void updateSettlementStatusBySettlementOrderDatas(@Param("settlementOrderId") Long settlementOrderId, @Param("settlementStatus") Integer settlementStatus);

    List<ManagerCommonCountDTO> selectValidCountByDeptTypeAndPeriods(@Param("statisticType") Integer statisticType,
                                                                     @Param("periods") List<Integer> periods);
    List<ManagerCommonCountDTO> selectValidCountByPeriods(@Param("statisticType") Integer statisticType,
                                                          @Param("periods") List<Integer> periods,
                                                          @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                          @Param("queryDeptIds") List<Long> queryDeptIds);

    List<ManagerCommonCountDTO> selectOperCountByDeptTypeAndTime(@Param("statisticType") Integer statisticType, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("period") Integer period, @Param("operType") Integer operType);

    List<ManagerCommonCountDTO> selectOperCountByTime(@Param("statisticType") Integer statisticType, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("period") Integer period, @Param("operType") Integer operType,
                                                      @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                      @Param("queryDeptIds") List<Long> queryDeptIds);

    List<ManagerCommonCountDTO> selectNewPeriodCountByDeptTypeAndPeriod(@Param("statisticType") Integer statisticType, @Param("period") Integer period);

    List<ManagerCommonCountDTO> selectNewPeriodCountByPeriod(@Param("statisticType") Integer statisticType, @Param("period") Integer period,
                                                             @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                             @Param("queryDeptIds") List<Long> queryDeptIds);

    List<CustomerServicePeriodMonthMiniListDTO> selectValidListByPeriod(IPage<CustomerServicePeriodMonthMiniListDTO> result,
                                                                        @Param("statisticType") Integer statisticType,
                                                                        @Param("period") Integer period,
                                                                        @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                        @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                        @Param("tagIncludeFlag") Integer tagIncludeFlag,
                                                                        @Param("ids") List<Long> ids,
                                                                        @Param("vo") CustomerServicePeriodMonthMiniListSearchVO vo);

    List<CustomerServicePeriodMonthMiniListDTO> selectOperListByOperTypeAndTime(IPage<CustomerServicePeriodMonthMiniListDTO> result,
                                                                                @Param("statisticType") Integer statisticType,
                                                                                @Param("startTime") String startTime,
                                                                                @Param("endTime") String endTime,
                                                                                @Param("period") Integer period,
                                                                                @Param("operType") Integer operType,
                                                                                @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                                @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                                @Param("tagIncludeFlag") Integer tagIncludeFlag,
                                                                                @Param("ids") List<Long> ids,
                                                                                @Param("vo") CustomerServicePeriodMonthMiniListSearchVO vo);

    List<CustomerServicePeriodMonthMiniListDTO> selectNewPeriodListByPeriod(IPage<CustomerServicePeriodMonthMiniListDTO> result,
                                                                            @Param("statisticType") Integer statisticType,
                                                                            @Param("period") Integer period,
                                                                            @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                            @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                            @Param("tagIncludeFlag") Integer tagIncludeFlag,
                                                                            @Param("ids") List<Long> ids,
                                                                            @Param("vo") CustomerServicePeriodMonthMiniListSearchVO vo);

    void updatePrepayStatusBySettlementOrderDatas(@Param("settlementOrderId") Long settlementOrderId, @Param("prepayStatus") Integer prepayStatus);

    Integer selectMinPeriodByCustomerServiceIds(@Param("customerServiceIds") List<Long> customerServiceIds);

    void updateDeliverStatus(@Param("nowPeriod") Integer nowPeriod);

    void updateDeliverStatusById(@Param("customerServicePeriodMonthId") Long customerServicePeriodMonthId);

    void updateDeliverStatusByIds(@Param("customerServicePeriodMonthIds") List<Long> customerServicePeriodMonthIds);

    Long selectBankPaymentResultCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                      @Param("queryDeptIds") List<Long> queryDeptIds,
                                      @Param("bankPaymentResult") Integer bankPaymentResult);

//    List<BankPaymentResultCountDTO> selectBankPaymentResultDataCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
//                                                                 @Param("queryDeptIds") List<Long> queryDeptIds,
//                                                                 @Param("bankPaymentResultList") List<Integer> bankPaymentResultList);

    Long selectBankWaitCreateCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                 @Param("queryDeptIds") List<Long> queryDeptIds,
                                   @Param("nowPeriod") Integer nowPeriod);

    List<AccountingCashierWaitCreateStatisticDTO> selectBankWaitCreateList(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                            @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                            @Param("nowPeriod") Integer nowPeriod);

    Long selectBankPatrialMisssCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                        @Param("queryDeptIds") List<Long> queryDeptIds,
                                     @Param("nowPeriod") Integer nowPeriod);

    Long selectInAccountWaitCreateCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                        @Param("queryDeptIds") List<Long> queryDeptIds,
                                        @Param("nowPeriod") Integer nowPeriod);

    List<InAccountMiniListDTO> inAccountMiniList(IPage<InAccountMiniListDTO> result,
                                                 @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                 @Param("queryDeptIds") List<Long> queryDeptIds,
                                                 @Param("customerName") String customerName,
                                                 @Param("miniListType") Integer miniListType,
                                                 @Param("period") Integer period,
                                                 @Param("statisticTaxType") Integer statisticTaxType);

    void updateDeliverStatusByCustomerServiceId(@Param("customerServiceId") Long customerServiceId);

    void updateDeliverStatusByCustomerServiceIds(@Param("customerServiceIds") List<Long> customerServiceIds);

    void updateBankPaymentResult(@Param("nowPeriod") Integer nowPeriod);

    void updateInEndAccountStatus(@Param("nowPeriod") Integer nowPeriod);

    void updateDeliverStatusAll();

    void updateNewInAccountStatus(@Param("period") Integer period);

    void updateExceptionInAccountStatus(@Param("period") Integer period);

    List<CustomerServicePeriodMonth> selectBatchByCustomerServiceIdAndPeriod(@Param("customerPeriodList") List<CustomerInAccountMaxPeriodDTO> customerPeriodList);

    List<CustomerServicePeriodMonth> selectNoIncomePeriod(@Param("period") Integer period);

    Long selectNoAdvisorCount(@Param("userDept") UserDeptDTO dept);

    Long selectNoAccountingCount(@Param("userDept") UserDeptDTO dept);

    List<CustomerServicePeriodMonthSimpleDTO> periodNoAccountingNoAdvisorPageList(IPage<CustomerServicePeriodMonthSimpleDTO> result,
                                                                                  @Param("userDept") UserDeptDTO userDeptDTO,
                                                                                  @Param("vo") CustomerServicePeriodMonthNoAccountingNoAdvisorVO vo,
                                                                                  @Param("batchSearchCustomerServiceIds") List<Long> batchSearchCustomerServiceIds);
}
