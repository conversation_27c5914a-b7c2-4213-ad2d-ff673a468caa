package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.BankDirectStatus;
import com.bxm.common.core.enums.BankReceiptStatus;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.enums.customerService.Currency;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteCustomerBankAccountDTO;
import com.bxm.customer.api.domain.vo.RemoteCustomerBankAccountNumberSearchVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerBankAccountVO;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServiceBankAccount;
import com.bxm.customer.domain.CustomerServiceCashierAccounting;
import com.bxm.customer.domain.NewCustomerBankAccount;
import com.bxm.customer.domain.dto.CustomerServiceBankAccountDTO;
import com.bxm.customer.mapper.CustomerServiceBankAccountMapper;
import com.bxm.customer.mapper.CustomerServiceCashierAccountingMapper;
import com.bxm.customer.mapper.NewCustomerBankAccountMapper;
import com.bxm.customer.service.ICCustomerServiceService;
import com.bxm.customer.service.ICustomerServiceBankAccountService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysEmployee;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 服务银行账号Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
@Slf4j
public class CustomerServiceBankAccountServiceImpl extends ServiceImpl<CustomerServiceBankAccountMapper, CustomerServiceBankAccount> implements ICustomerServiceBankAccountService
{
    @Autowired
    private CustomerServiceBankAccountMapper customerServiceBankAccountMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private NewCustomerBankAccountMapper newCustomerBankAccountMapper;

    @Autowired
    private CustomerServiceCashierAccountingMapper customerServiceCashierAccountingMapper;

    @Autowired
    @Lazy
    private ICCustomerServiceService customerServiceService;

    /**
     * 查询服务银行账号
     * 
     * @param id 服务银行账号主键
     * @return 服务银行账号
     */
    @Override
    public CustomerServiceBankAccount selectCustomerServiceBankAccountById(Long id)
    {
        return customerServiceBankAccountMapper.selectCustomerServiceBankAccountById(id);
    }

    /**
     * 查询服务银行账号列表
     * 
     * @param customerServiceBankAccount 服务银行账号
     * @return 服务银行账号
     */
    @Override
    public List<CustomerServiceBankAccount> selectCustomerServiceBankAccountList(CustomerServiceBankAccount customerServiceBankAccount)
    {
        return customerServiceBankAccountMapper.selectCustomerServiceBankAccountList(customerServiceBankAccount);
    }

    /**
     * 新增服务银行账号
     * 
     * @param customerServiceBankAccount 服务银行账号
     * @return 结果
     */
    @Override
    public int insertCustomerServiceBankAccount(CustomerServiceBankAccount customerServiceBankAccount)
    {
        return customerServiceBankAccountMapper.insertCustomerServiceBankAccount(customerServiceBankAccount);
    }

    /**
     * 修改服务银行账号
     * 
     * @param customerServiceBankAccount 服务银行账号
     * @return 结果
     */
    @Override
    public int updateCustomerServiceBankAccount(CustomerServiceBankAccount customerServiceBankAccount)
    {
        return customerServiceBankAccountMapper.updateCustomerServiceBankAccount(customerServiceBankAccount);
    }

    /**
     * 批量删除服务银行账号
     * 
     * @param ids 需要删除的服务银行账号主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceBankAccountByIds(Long[] ids)
    {
        return customerServiceBankAccountMapper.deleteCustomerServiceBankAccountByIds(ids);
    }

    /**
     * 删除服务银行账号信息
     * 
     * @param id 服务银行账号主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceBankAccountById(Long id)
    {
        return customerServiceBankAccountMapper.deleteCustomerServiceBankAccountById(id);
    }

    @Override
    public List<CustomerServiceBankAccount> selectByCustomerServiceIdAndPeriod(Long customerServiceId, Integer period) {
        if (Objects.isNull(customerServiceId) || Objects.isNull(period)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<CustomerServiceBankAccount>().eq("customer_service_id", customerServiceId)
                .and(wrapper -> wrapper.isNull("account_open_date").or().le("date_format(account_open_date, '%Y%m')", period))
                .and(wrapper -> wrapper.isNull("account_close_date").or().ge("date_format(account_close_date, '%Y%m')", period)));
    }

    @Override
    public List<CustomerServiceBankAccount> selectByCustomerServiceId(Long id) {
        if (Objects.isNull(id)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServiceBankAccount>().eq(CustomerServiceBankAccount::getCustomerServiceId, id)
                .orderByDesc(CustomerServiceBankAccount::getCreateTime));
    }

    @Override
    @Transactional
    public void deleteCustomerServiceBankAccount(Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        CustomerServiceBankAccount bankAccount = getById(id);
        if (Objects.isNull(bankAccount)) {
            throw new ServiceException("银行账号不存在");
        }
        removeById(id);
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(bankAccount.getCustomerServiceId())
                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                    .setDeptId(deptId)
                    .setOperType("删除银行账号")
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperContent("删除（" + bankAccount.getBankName() + StringUtils.desensitize(bankAccount.getBankAccountNumber()) + "）")
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void addCustomerServiceBankAccount(CustomerServiceBankAccountDTO vo, CCustomerService customerService) {
        if (checkBankAccountExists(customerService.getBusinessTopDeptId(), vo.getBankAccountNumber(), null, customerService.getCreditCode(), customerService.getId())) {
            throw new ServiceException("该账号已存在");
        }
        CustomerServiceBankAccount customerServiceBankAccount = new CustomerServiceBankAccount()
                .setCustomerServiceId(vo.getCustomerServiceId())
                .setBankName(vo.getBankName())
                .setBankAccountNumber(vo.getBankAccountNumber())
                .setPassword(vo.getPassword())
                .setDepositName(vo.getDepositName())
                .setPhoneNumber(vo.getPhoneNumber())
                .setAccountOpenDate(vo.getAccountOpenDate())
                .setAccountCloseDate(vo.getAccountCloseDate())
                .setReceiptAccountNumber(vo.getReceiptAccountNumber())
                .setReceiptStatus(vo.getReceiptStatus())
                .setBankDirect(vo.getBankDirect())
                .setRemarks(vo.getRemarks())
                .setIsPutOnTaxRecord(vo.getIsPutOnTaxRecord())
                .setCurrency(vo.getCurrency());
        save(customerServiceBankAccount);
        String operContent = getOperContent(null, vo, 1);
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getCustomerServiceId())
                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                    .setDeptId(deptId)
                    .setOperType("新增银行信息")
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperContent(operContent)
                    .setOperUserId(userId)
                    .setOperRemark(vo.getRemarks()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void modifyCustomerServiceBankAccount(CustomerServiceBankAccountDTO vo, CCustomerService customerService, CustomerServiceBankAccount oldBankAccount, Long deptId, Long userId, String operName) {
        if (checkBankAccountExists(customerService.getBusinessTopDeptId(), vo.getBankAccountNumber(), vo.getId(), customerService.getCreditCode(), customerService.getId())) {
            throw new ServiceException("该账号已存在");
        }

        update(new LambdaUpdateWrapper<CustomerServiceBankAccount>()
                .eq(CustomerServiceBankAccount::getId, vo.getId())
                .set(CustomerServiceBankAccount::getCustomerServiceId, vo.getCustomerServiceId())
                .set(CustomerServiceBankAccount::getBankName, vo.getBankName())
                .set(CustomerServiceBankAccount::getBankAccountNumber, vo.getBankAccountNumber())
                .set(CustomerServiceBankAccount::getPassword, vo.getPassword())
                .set(CustomerServiceBankAccount::getPhoneNumber, vo.getPhoneNumber())
                .set(CustomerServiceBankAccount::getAccountOpenDate, vo.getAccountOpenDate())
                .set(CustomerServiceBankAccount::getAccountCloseDate, vo.getAccountCloseDate())
                .set(CustomerServiceBankAccount::getReceiptAccountNumber, vo.getReceiptAccountNumber())
                .set(CustomerServiceBankAccount::getReceiptStatus, vo.getReceiptStatus())
                .set(CustomerServiceBankAccount::getBankDirect, vo.getBankDirect())
                .set(CustomerServiceBankAccount::getDepositName, vo.getDepositName())
                .set(CustomerServiceBankAccount::getRemarks, vo.getRemarks())
                .set(CustomerServiceBankAccount::getIsPutOnTaxRecord, vo.getIsPutOnTaxRecord())
                .set(CustomerServiceBankAccount::getCurrency, vo.getCurrency()));
        String operContent = getOperContent(oldBankAccount, vo, 2);
        if (!StringUtils.isEmpty(operContent)) {
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getCustomerServiceId())
                        .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                        .setDeptId(deptId)
                        .setOperType("编辑银行信息")
                        .setOperName(operName)
                        .setOperContent(operContent)
                        .setOperUserId(userId)
                        .setOperRemark(vo.getRemarks()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    public Boolean checkBankAccountExists(Long businessTopDeptId, String account, Long id, String creditCode, Long customerServiceId) {
        return count(new LambdaQueryWrapper<CustomerServiceBankAccount>().eq(CustomerServiceBankAccount::getCustomerServiceId, customerServiceId)
                .eq(CustomerServiceBankAccount::getBankAccountNumber, account)
                .ne(!Objects.isNull(id), CustomerServiceBankAccount::getId, id)) > 0 ||
                baseMapper.getBusinessTopDeptBankAccountCount(businessTopDeptId, account, id, creditCode) > 0 ||
                newCustomerBankAccountMapper.getBusinessTopDeptBankAccountCount(businessTopDeptId, account, null, creditCode) > 0;
    }

    @Override
    public List<RemoteCustomerBankAccountDTO> getCustomerBankAccountNumberByBankNumbers(RemoteCustomerBankAccountNumberSearchVO vo) {
        if (ObjectUtils.isEmpty(vo.getBankAccountNumbers())) {
             return Collections.emptyList();
        }
        return baseMapper.getCustomerBankAccountNumberByBankNumbers(vo);
    }

    @Override
    public RemoteCustomerBankAccountDTO getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(String bankAccountNumber, Long businessTopDeptId) {
        return baseMapper.getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(bankAccountNumber, businessTopDeptId);
    }

    @Override
    @Transactional
    public void remoteAddCustomerServiceBankAccount(RemoteCustomerBankAccountVO vo, CCustomerService customerService) {
        if (checkBankAccountExists(customerService.getBusinessTopDeptId(), vo.getBankAccountNumber(), vo.getBankId(), customerService.getCreditCode(), customerService.getId())) {
            throw new ServiceException("该账号已存在");
        }
        CustomerServiceBankAccount oldCustomerBank = null;
        if (Objects.isNull(vo.getBankId())) {
            CustomerServiceBankAccount customerServiceBankAccount = new CustomerServiceBankAccount()
                    .setCustomerServiceId(vo.getCustomerServiceId())
                    .setBankName(vo.getBankName())
                    .setBankAccountNumber(vo.getBankAccountNumber())
                    .setPassword((StringUtils.isEmpty(vo.getPassword()) || Objects.equals("无", vo.getPassword())) ? null : vo.getPassword())
                    .setDepositName((StringUtils.isEmpty(vo.getDepositName()) || Objects.equals("无", vo.getDepositName())) ? null : vo.getDepositName())
                    .setPhoneNumber((StringUtils.isEmpty(vo.getPhoneNumber()) || Objects.equals("无", vo.getPhoneNumber())) ? null : vo.getPhoneNumber())
                    .setAccountOpenDate((Objects.isNull(vo.getAccountOpenDate()) || Objects.equals(2099, vo.getAccountOpenDate().getYear())) ? null : vo.getAccountOpenDate())
                    .setAccountCloseDate((Objects.isNull(vo.getAccountCloseDate()) || Objects.equals(2099, vo.getAccountCloseDate().getYear())) ? null : vo.getAccountCloseDate())
                    .setReceiptAccountNumber((StringUtils.isEmpty(vo.getReceiptAccountNumber()) || Objects.equals("无", vo.getReceiptAccountNumber())) ? null : vo.getReceiptAccountNumber())
                    .setReceiptStatus(vo.getReceiptStatus())
                    .setBankDirect(vo.getBankDirect())
                    .setRemarks((StringUtils.isEmpty(vo.getRemark()) || Objects.equals("无", vo.getRemark())) ? null : vo.getRemark())
                    .setIsPutOnTaxRecord(vo.getIsPutOnTaxRecord());
            save(customerServiceBankAccount);
            customerServiceService.updateBankPaymentResultByCreateBank(new CustomerServiceBankAccountDTO().setAccountCloseDate(customerServiceBankAccount.getAccountCloseDate())
                    .setAccountOpenDate(customerServiceBankAccount.getAccountOpenDate()).setCustomerServiceId(customerService.getId()).setBankAccountNumber(vo.getBankAccountNumber()).setBankName(vo.getBankName()),
                    vo.getUserId(), vo.getDeptId(), vo.getOperName(), 1);
        } else {
            oldCustomerBank = getById(vo.getBankId());
            if ((!Objects.isNull(vo.getAccountOpenDate()) && !Objects.equals(2099, vo.getAccountOpenDate().getYear())) || (!Objects.isNull(vo.getAccountCloseDate()) && !Objects.equals(2099, vo.getAccountCloseDate().getYear()))) {
                Integer open = Objects.isNull(vo.getAccountOpenDate()) ? null : Integer.parseInt(vo.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM")));
                Integer close = Objects.isNull(vo.getAccountCloseDate()) ? null : Integer.parseInt(vo.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM")));
                LambdaQueryWrapper<CustomerServiceCashierAccounting> queryWrapper = new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                        .eq(CustomerServiceCashierAccounting::getIsDel, false)
                        .eq(CustomerServiceCashierAccounting::getCustomerServiceId, vo.getCustomerServiceId())
                        .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.FLOW.getCode())
                        .eq(CustomerServiceCashierAccounting::getBankAccountNumber, oldCustomerBank.getBankAccountNumber());
                if (Objects.isNull(open)) {
                    queryWrapper.gt(CustomerServiceCashierAccounting::getPeriod, close);
                } else if (Objects.isNull(close)) {
                    queryWrapper.lt(CustomerServiceCashierAccounting::getPeriod, open);
                } else {
                    queryWrapper.and(wrapper -> wrapper.gt(CustomerServiceCashierAccounting::getPeriod, close).or().lt(CustomerServiceCashierAccounting::getPeriod, open));
                }
                if (customerServiceCashierAccountingMapper.selectCount(queryWrapper) > 0) {
                    throw new ServiceException("存在流水交付单，不允许修改");
                }
            }
            LambdaUpdateWrapper<CustomerServiceBankAccount> updateWrapper = new LambdaUpdateWrapper<CustomerServiceBankAccount>()
                    .eq(CustomerServiceBankAccount::getId, vo.getBankId())
                    .set(CustomerServiceBankAccount::getCustomerServiceId, vo.getCustomerServiceId())
                    .set(CustomerServiceBankAccount::getBankName, vo.getBankName())
                    .set(CustomerServiceBankAccount::getBankAccountNumber, vo.getBankAccountNumber());
            if (!StringUtils.isEmpty(vo.getRemark())) {
                if (Objects.equals("无", vo.getRemark())) {
                    updateWrapper.set(CustomerServiceBankAccount::getRemarks, null);
                } else {
                    updateWrapper.set(CustomerServiceBankAccount::getRemarks, vo.getRemark());
                }
            }
            if (!Objects.isNull(vo.getIsPutOnTaxRecord())) {
                updateWrapper.set(CustomerServiceBankAccount::getIsPutOnTaxRecord, vo.getIsPutOnTaxRecord());
            }
            if (!Objects.isNull(vo.getReceiptStatus())) {
                updateWrapper.set(CustomerServiceBankAccount::getReceiptStatus, vo.getReceiptStatus());
            }
            if (!Objects.isNull(vo.getBankDirect())) {
                updateWrapper.set(CustomerServiceBankAccount::getBankDirect, vo.getBankDirect());
            }
            if (!StringUtils.isEmpty(vo.getDepositName())) {
                if (Objects.equals("无", vo.getDepositName())) {
                    updateWrapper.set(CustomerServiceBankAccount::getDepositName, null);
                } else {
                    updateWrapper.set(CustomerServiceBankAccount::getDepositName, vo.getDepositName());
                }
            }
            if (!StringUtils.isEmpty(vo.getPassword())) {
                if (Objects.equals("无", vo.getPassword())) {
                    updateWrapper.set(CustomerServiceBankAccount::getPassword, null);
                } else {
                    updateWrapper.set(CustomerServiceBankAccount::getPassword, vo.getPassword());
                }
            }
            if (!StringUtils.isEmpty(vo.getPhoneNumber())) {
                if (Objects.equals("无", vo.getPhoneNumber())) {
                    updateWrapper.set(CustomerServiceBankAccount::getPhoneNumber, null);
                } else {
                    updateWrapper.set(CustomerServiceBankAccount::getPhoneNumber, vo.getPhoneNumber());
                }
            }
            if (!StringUtils.isEmpty(vo.getReceiptAccountNumber())) {
                if (Objects.equals("无", vo.getReceiptAccountNumber())) {
                    updateWrapper.set(CustomerServiceBankAccount::getReceiptAccountNumber, null);
                } else {
                    updateWrapper.set(CustomerServiceBankAccount::getReceiptAccountNumber, vo.getReceiptAccountNumber());
                }
            }
            if (!Objects.isNull(vo.getAccountOpenDate())) {
                if (Objects.equals(vo.getAccountOpenDate().getYear(), 2099)) {
                    updateWrapper.set(CustomerServiceBankAccount::getAccountOpenDate, null);
                } else {
                    updateWrapper.set(CustomerServiceBankAccount::getAccountOpenDate, vo.getAccountOpenDate());
                }
            }
            if (!Objects.isNull(vo.getAccountCloseDate())) {
                if (Objects.equals(vo.getAccountCloseDate().getYear(), 2099)) {
                    updateWrapper.set(CustomerServiceBankAccount::getAccountCloseDate, null);
                } else {
                    updateWrapper.set(CustomerServiceBankAccount::getAccountCloseDate, vo.getAccountCloseDate());
                }
            }
            update(updateWrapper);
            customerServiceService.updatePeriodBankPaymentByCustomerServiceId(customerService.getId(), 1, oldCustomerBank, vo.getUserId(), vo.getDeptId(), vo.getOperName(), 1);
        }
        CustomerServiceBankAccountDTO dto = new CustomerServiceBankAccountDTO();
        BeanUtils.copyProperties(vo, dto);
        dto.setRemarks(vo.getRemark());
        String operContent = getOperContentV2(oldCustomerBank, dto, Objects.isNull(vo.getBankId()) ? 1 : 2);
        if (!StringUtils.isEmpty(operContent)) {
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getCustomerServiceId())
                        .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                        .setDeptId(vo.getDeptId())
                        .setOperType(Objects.isNull(vo.getBankId()) ? "新增银行信息" : "编辑银行信息")
                        .setOperName(vo.getOperName())
                        .setOperContent(operContent)
                        .setOperUserId(vo.getUserId())
                        .setOperRemark(vo.getRemark()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    public RemoteCustomerBankAccountDTO getNewCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(String bankAccountNumber, Long businessTopDeptId) {
        return newCustomerBankAccountMapper.getNewCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(bankAccountNumber, businessTopDeptId);
    }

    private String getOperContent(CustomerServiceBankAccount oldBankAccount, CustomerServiceBankAccountDTO dto, Integer operType) {
        StringBuilder operContent = new StringBuilder();
        if (operType == 1) {
            operContent.append("新增银行信息，");
            operContent.append(String.format("%s为%s，", "银行名称", StringUtils.isEmpty(dto.getBankName()) ? "空" : dto.getBankName()));
            operContent.append(String.format("%s为%s，", "银行账号", StringUtils.isEmpty(dto.getBankAccountNumber()) ? "空" : StringUtils.desensitize(dto.getBankAccountNumber())));
            operContent.append(String.format("%s为%s，", "银行密码", StringUtils.isEmpty(dto.getPassword()) ? "空" : dto.getPassword()));
            operContent.append(String.format("%s为%s，", "开户行", StringUtils.isEmpty(dto.getDepositName()) ? "空" : dto.getDepositName()));
            operContent.append(String.format("%s为%s，", "手机号", StringUtils.isEmpty(dto.getPhoneNumber()) ? "空" : dto.getPhoneNumber()));
            operContent.append(String.format("%s为%s，", "开户时间", Objects.isNull(dto.getAccountOpenDate()) ? "空" : (Objects.equals(dto.getAccountOpenDate().getYear(), 2099)) ? "无" : dto.getAccountOpenDate().toString()));
            operContent.append(String.format("%s为%s，", "销户时间", Objects.isNull(dto.getAccountCloseDate()) ? "空" : (Objects.equals(dto.getAccountCloseDate().getYear(), 2099)) ? "无" : dto.getAccountCloseDate().toString()));
            operContent.append(String.format("%s为%s，", "回单卡账号", StringUtils.isEmpty(dto.getReceiptAccountNumber()) ? "空" : dto.getReceiptAccountNumber()));
            operContent.append(String.format("%s为%s，", "回单卡托管", Objects.isNull(dto.getReceiptStatus()) ? "空" : BankReceiptStatus.getBankReceiptStatusByReceiptStatus(dto.getReceiptStatus()).getName()));
            operContent.append(String.format("%s为%s，", "企银直连", Objects.isNull(dto.getBankDirect()) ? "空" : BankDirectStatus.getBankDirectStatusByBankDirect(dto.getBankDirect()).getName()));
            operContent.append(String.format("%s为%s，", "备注说明", StringUtils.isEmpty(dto.getRemarks()) ? "空" : dto.getRemarks()));
            operContent.append(String.format("%s为%s，", "税局备案", Objects.isNull(dto.getIsPutOnTaxRecord()) ? "空" : dto.getIsPutOnTaxRecord() == 0 ? "否" : "是"));
            operContent.append(String.format("%s为%s，", "币种", StringUtils.isEmpty(dto.getCurrency()) ? "空" : Currency.getNameByCode(dto.getCurrency())));
            return operContent.substring(0, operContent.length() - 1);
        } else {
            if (!StringUtils.isEmpty(dto.getBankName()) && !Objects.equals(oldBankAccount.getBankName(), dto.getBankName())) {
                operContent.append(String.format("%s为%s，", "银行名称", StringUtils.isEmpty(dto.getBankName()) ? "空" : dto.getBankName()));
            }
            if (!StringUtils.isEmpty(dto.getBankAccountNumber()) && !Objects.equals(oldBankAccount.getBankAccountNumber(), dto.getBankAccountNumber())) {
                operContent.append(String.format("%s为%s，", "银行账号", StringUtils.isEmpty(dto.getBankAccountNumber()) ? "空" : StringUtils.desensitize(dto.getBankAccountNumber())));
            }
            if (!StringUtils.isEmpty(dto.getPassword()) && !Objects.equals(oldBankAccount.getPassword(), dto.getPassword())) {
                operContent.append(String.format("%s为%s，", "银行密码", StringUtils.isEmpty(dto.getPassword()) ? "空" : dto.getPassword()));
            }
            if (!StringUtils.isEmpty(dto.getDepositName()) && !Objects.equals(oldBankAccount.getDepositName(), dto.getDepositName())) {
                operContent.append(String.format("%s为%s，", "开户行", StringUtils.isEmpty(dto.getDepositName()) ? "空" : dto.getDepositName()));
            }
            if (!StringUtils.isEmpty(dto.getPhoneNumber()) && !Objects.equals(oldBankAccount.getPhoneNumber(), dto.getPhoneNumber())) {
                operContent.append(String.format("%s为%s，", "手机号", StringUtils.isEmpty(dto.getPhoneNumber()) ? "空" : dto.getPhoneNumber()));
            }
            if (!Objects.equals(oldBankAccount.getAccountOpenDate(), dto.getAccountOpenDate())) {
                operContent.append(String.format("%s为%s，", "开户时间", Objects.isNull(dto.getAccountOpenDate()) ? "空" : (Objects.equals(2099, dto.getAccountOpenDate().getYear())) ? "无" : dto.getAccountOpenDate().toString()));
            }
            if (!Objects.equals(oldBankAccount.getAccountCloseDate(), dto.getAccountCloseDate())) {
                operContent.append(String.format("%s为%s，", "销户时间", Objects.isNull(dto.getAccountCloseDate()) ? "空" : (Objects.equals(2099, dto.getAccountCloseDate().getYear())) ? "无" : dto.getAccountCloseDate().toString()));
            }
            if (!StringUtils.isEmpty(dto.getReceiptAccountNumber()) && !Objects.equals(oldBankAccount.getReceiptAccountNumber(), dto.getReceiptAccountNumber())) {
                operContent.append(String.format("%s为%s，", "回单卡账号", StringUtils.isEmpty(dto.getReceiptAccountNumber()) ? "空" : dto.getReceiptAccountNumber()));
            }
            if (!Objects.equals(oldBankAccount.getReceiptStatus(), dto.getReceiptStatus())) {
                operContent.append(String.format("%s为%s，", "回单卡托管", Objects.isNull(dto.getReceiptStatus()) ? "空" : BankReceiptStatus.getBankReceiptStatusByReceiptStatus(dto.getReceiptStatus()).getName()));
            }
            if (!Objects.equals(oldBankAccount.getBankDirect(), dto.getBankDirect())) {
                operContent.append(String.format("%s为%s，", "企银直连", Objects.isNull(dto.getBankDirect()) ? "空" : BankDirectStatus.getBankDirectStatusByBankDirect(dto.getBankDirect()).getName()));
            }
            if (!Objects.equals(oldBankAccount.getRemarks(), dto.getRemarks())) {
                operContent.append(String.format("%s为%s，", "备注说明", StringUtils.isEmpty(dto.getRemarks()) ? "空" : dto.getRemarks()));
            }
            if (!Objects.isNull(dto.getIsPutOnTaxRecord()) && !Objects.equals(oldBankAccount.getIsPutOnTaxRecord(), dto.getIsPutOnTaxRecord())) {
                operContent.append(String.format("%s为%s，", "税局备案", dto.getIsPutOnTaxRecord() == 0 ? "否" : "是"));
            }
            if (!StringUtils.isEmpty(dto.getCurrency()) && !Objects.equals(oldBankAccount.getCurrency(), dto.getCurrency())) {
                operContent.append(String.format("%s为%s，", "币种", Currency.getNameByCode(dto.getCurrency())));
            }
            if (!StringUtils.isEmpty(operContent.toString())) {
                return "修改（" + oldBankAccount.getBankName() + StringUtils.desensitize(oldBankAccount.getBankAccountNumber()) + "）的" + operContent.substring(0, operContent.length() - 1);
            }
            return "";
        }
    }

    private String getOperContentV2(CustomerServiceBankAccount oldBankAccount, CustomerServiceBankAccountDTO dto, Integer operType) {
        StringBuilder operContent = new StringBuilder();
        if (operType == 1) {
            operContent.append("新增银行信息，");
            operContent.append(String.format("%s为%s，", "银行名称", StringUtils.isEmpty(dto.getBankName()) ? "空" : dto.getBankName()));
            operContent.append(String.format("%s为%s，", "银行账号", StringUtils.isEmpty(dto.getBankAccountNumber()) ? "空" : StringUtils.desensitize(dto.getBankAccountNumber())));
            operContent.append(String.format("%s为%s，", "银行密码", StringUtils.isEmpty(dto.getPassword()) ? "空" : dto.getPassword()));
            operContent.append(String.format("%s为%s，", "开户行", StringUtils.isEmpty(dto.getDepositName()) ? "空" : dto.getDepositName()));
            operContent.append(String.format("%s为%s，", "手机号", StringUtils.isEmpty(dto.getPhoneNumber()) ? "空" : dto.getPhoneNumber()));
            operContent.append(String.format("%s为%s，", "开户时间", Objects.isNull(dto.getAccountOpenDate()) ? "空" : (Objects.equals(dto.getAccountOpenDate().getYear(), 2099)) ? "无" : dto.getAccountOpenDate().toString()));
            operContent.append(String.format("%s为%s，", "销户时间", Objects.isNull(dto.getAccountCloseDate()) ? "空" : (Objects.equals(dto.getAccountCloseDate().getYear(), 2099)) ? "无" : dto.getAccountCloseDate().toString()));
            operContent.append(String.format("%s为%s，", "回单卡账号", StringUtils.isEmpty(dto.getReceiptAccountNumber()) ? "空" : dto.getReceiptAccountNumber()));
            operContent.append(String.format("%s为%s，", "回单卡托管", Objects.isNull(dto.getReceiptStatus()) ? "空" : BankReceiptStatus.getBankReceiptStatusByReceiptStatus(dto.getReceiptStatus()).getName()));
            operContent.append(String.format("%s为%s，", "企银直连", Objects.isNull(dto.getBankDirect()) ? "空" : BankDirectStatus.getBankDirectStatusByBankDirect(dto.getBankDirect()).getName()));
            operContent.append(String.format("%s为%s，", "备注说明", StringUtils.isEmpty(dto.getRemarks()) ? "空" : dto.getRemarks()));
            operContent.append(String.format("%s为%s，", "税局备案", Objects.isNull(dto.getIsPutOnTaxRecord()) ? "空" : dto.getIsPutOnTaxRecord() == 0 ? "否" : "是"));
            return operContent.substring(0, operContent.length() - 1);
        } else {
            if (!StringUtils.isEmpty(dto.getBankName()) && !Objects.equals(oldBankAccount.getBankName(), dto.getBankName())) {
                operContent.append(String.format("%s为%s，", "银行名称", StringUtils.isEmpty(dto.getBankName()) ? "空" : dto.getBankName()));
            }
            if (!StringUtils.isEmpty(dto.getBankAccountNumber()) && !Objects.equals(oldBankAccount.getBankAccountNumber(), dto.getBankAccountNumber())) {
                operContent.append(String.format("%s为%s，", "银行账号", StringUtils.isEmpty(dto.getBankAccountNumber()) ? "空" : StringUtils.desensitize(dto.getBankAccountNumber())));
            }
            if (!StringUtils.isEmpty(dto.getPassword()) && !Objects.equals(oldBankAccount.getPassword(), dto.getPassword())) {
                operContent.append(String.format("%s为%s，", "银行密码", StringUtils.isEmpty(dto.getPassword()) ? "空" : dto.getPassword()));
            }
            if (!StringUtils.isEmpty(dto.getDepositName()) && !Objects.equals(oldBankAccount.getDepositName(), dto.getDepositName())) {
                operContent.append(String.format("%s为%s，", "开户行", StringUtils.isEmpty(dto.getDepositName()) ? "空" : dto.getDepositName()));
            }
            if (!StringUtils.isEmpty(dto.getPhoneNumber()) && !Objects.equals(oldBankAccount.getPhoneNumber(), dto.getPhoneNumber())) {
                operContent.append(String.format("%s为%s，", "手机号", StringUtils.isEmpty(dto.getPhoneNumber()) ? "空" : dto.getPhoneNumber()));
            }
            if (!Objects.isNull(dto.getAccountOpenDate()) && !Objects.equals(oldBankAccount.getAccountOpenDate(), dto.getAccountOpenDate())) {
                operContent.append(String.format("%s为%s，", "开户时间", Objects.equals(2099, dto.getAccountOpenDate().getYear()) ? "无" : dto.getAccountOpenDate().toString()));
            }
            if (!Objects.isNull(dto.getAccountCloseDate()) && !Objects.equals(oldBankAccount.getAccountCloseDate(), dto.getAccountCloseDate())) {
                operContent.append(String.format("%s为%s，", "销户时间", Objects.equals(2099, dto.getAccountCloseDate().getYear()) ? "无" : dto.getAccountCloseDate().toString()));
            }
            if (!StringUtils.isEmpty(dto.getReceiptAccountNumber()) && !Objects.equals(oldBankAccount.getReceiptAccountNumber(), dto.getReceiptAccountNumber())) {
                operContent.append(String.format("%s为%s，", "回单卡账号", StringUtils.isEmpty(dto.getReceiptAccountNumber()) ? "空" : dto.getReceiptAccountNumber()));
            }
            if (!Objects.isNull(dto.getReceiptStatus()) && !Objects.equals(oldBankAccount.getReceiptStatus(), dto.getReceiptStatus())) {
                operContent.append(String.format("%s为%s，", "回单卡托管", BankReceiptStatus.getBankReceiptStatusByReceiptStatus(dto.getReceiptStatus()).getName()));
            }
            if (!Objects.isNull(dto.getBankDirect()) && !Objects.equals(oldBankAccount.getBankDirect(), dto.getBankDirect())) {
                operContent.append(String.format("%s为%s，", "企银直连", BankDirectStatus.getBankDirectStatusByBankDirect(dto.getBankDirect()).getName()));
            }
            if (!StringUtils.isEmpty(dto.getRemarks()) && !Objects.equals(oldBankAccount.getRemarks(), dto.getRemarks())) {
                operContent.append(String.format("%s为%s，", "备注说明", StringUtils.isEmpty(dto.getRemarks()) ? "空" : dto.getRemarks()));
            }
            if (!Objects.isNull(dto.getIsPutOnTaxRecord()) && !Objects.equals(oldBankAccount.getIsPutOnTaxRecord(), dto.getIsPutOnTaxRecord())) {
                operContent.append(String.format("%s为%s，", "税局备案", dto.getIsPutOnTaxRecord() == 0 ? "否" : "是"));
            }
            if (!StringUtils.isEmpty(operContent.toString())) {
                return "修改（" + oldBankAccount.getBankName() + StringUtils.desensitize(oldBankAccount.getBankAccountNumber()) + "）的" + operContent.substring(0, operContent.length() - 1);
            }
            return "";
        }
    }
}
