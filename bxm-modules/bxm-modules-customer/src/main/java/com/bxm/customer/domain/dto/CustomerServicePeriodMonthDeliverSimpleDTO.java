package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePeriodMonthDeliverSimpleDTO {

    @ApiModelProperty("交付单id")
    private Long deliverId;

    @ApiModelProperty("交付单类型")
    private Integer deliverType;

    @ApiModelProperty("交付单类型名称")
    private String deliverTypeName;

    @ApiModelProperty("是否应报，true-是，false-否")
    private Boolean isShouldReport;

    @ApiModelProperty("交付单状态")
    private Integer deliverStatus;

    @ApiModelProperty("交付单状态名称")
    private String deliverStatusName;

    @ApiModelProperty("税款总额")
    private BigDecimal deliverAmount;

    @ApiModelProperty("申报附件数量，点击查看附件调用接口/bxmCustomer/deliver/getDeliverFiles，fileTypes传[2]")
    private Long reportFileCount;

    @ApiModelProperty("扣除附件数量，点击查看附件调用接口/bxmCustomer/deliver/getDeliverFiles，fileTypes传[3]")
    private Long deductionFileCount;
}
