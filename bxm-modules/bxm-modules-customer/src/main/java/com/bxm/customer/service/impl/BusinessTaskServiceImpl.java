package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.enums.accountingCashier.*;
import com.bxm.common.core.enums.businessTask.*;
import com.bxm.common.core.enums.inAccount.InAccountStatus;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskAddVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskFinishVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchV2VO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchVO;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.AccountingInfoSourceDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierSimpleDTO;
import com.bxm.customer.domain.dto.accoutingCashier.BankPaymentTaskDTO;
import com.bxm.customer.domain.dto.businessTask.*;
import com.bxm.customer.domain.vo.TagSearchVO;
import com.bxm.customer.domain.vo.accoutingCashier.BatchUpdateBankAccountNumberVO;
import com.bxm.customer.domain.vo.accoutingCashier.PeriodBankAccountNumberVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskCommentVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskMattersNotesModifyVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskVO;
import com.bxm.customer.domain.vo.businessTask.operate.*;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.mapper.*;
import com.bxm.customer.properties.SpecialDeptIdProperties;
import com.bxm.customer.properties.SpecialTagProperties;
import com.bxm.customer.properties.SpecialUserIdIdProperties;
import com.bxm.customer.properties.ZhuliDeptProperties;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.*;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.thirdpart.api.domain.BanksEnterprisesExtractDTO;
import com.bxm.thirdpart.api.domain.CheckFilesDTO;
import com.bxm.thirdpart.api.domain.GenerateVoucherDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Slf4j
@Service

public class BusinessTaskServiceImpl extends ServiceImpl<BusinessTaskMapper, BusinessTask> implements IBusinessTaskService {
    private static final Integer BIZ_LOG_TYPE = BusinessLogBusinessType.TASK.getCode();

    @Autowired
    private BusinessTaskMapper businessTaskMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private IBusinessTaskFileService iBusinessTaskFileService;

    @Autowired
    private ICustomerServiceDocHandoverService iCustomerServiceDocHandoverService;

    @Autowired
    private ICCustomerServiceService icCustomerServiceService;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private ISettlementOrderDataTempService iSettlementOrderDataTempService;

    @Autowired
    private ISettlementOrderConditionService iSettlementOrderConditionService;

    @Autowired
    private ICustomerServiceInAccountService iCustomerServiceInAccountService;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    @Autowired
    private RedisService redisService;
    @Autowired
    private BusinessTaskFileMapper businessTaskFileMapper;

    @Autowired
    @Lazy
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @Autowired
    private ICustomerMattersNotesService customerMattersNotesService;

    @Autowired
    private ZhuliDeptProperties zhuliDeptProperties;

    @Autowired
    private SpecialUserIdIdProperties specialUserIdIdProperties;

    @Autowired
    private CustomerServiceBankAccountMapper customerServiceBankAccountMapper;

    @Autowired
    private SpecialDeptIdProperties specialDeptIdProperties;

    @Autowired
    private CustomerServiceCashierAccountingMapper customerServiceCashierAccountingMapper;

    /**
     * 查询业务任务
     *
     * @param id 业务任务主键
     * @return 业务任务
     */
    @Override
    public BusinessTask selectBusinessTaskById(Long id) {
        return businessTaskMapper.selectBusinessTaskById(id);
    }

    /**
     * 查询业务任务列表
     *
     * @param businessTask 业务任务
     * @return 业务任务
     */
    @Override
    public List<BusinessTask> selectBusinessTaskList(BusinessTask businessTask) {
        return businessTaskMapper.selectBusinessTaskList(businessTask);
    }

    /**
     * 新增业务任务
     *
     * @param businessTask 业务任务
     * @return 结果
     */
    @Override
    public int insertBusinessTask(BusinessTask businessTask) {
        businessTask.setCreateTime(DateUtils.getNowDate());
        return businessTaskMapper.insertBusinessTask(businessTask);
    }

    /**
     * 修改业务任务
     *
     * @param businessTask 业务任务
     * @return 结果
     */
    @Override
    public int updateBusinessTask(BusinessTask businessTask) {
        businessTask.setUpdateTime(DateUtils.getNowDate());
        return businessTaskMapper.updateBusinessTask(businessTask);
    }

    /**
     * 批量删除业务任务
     *
     * @param ids 需要删除的业务任务主键
     * @return 结果
     */
    @Override
    public int deleteBusinessTaskByIds(Long[] ids) {
        return businessTaskMapper.deleteBusinessTaskByIds(ids);
    }

    /**
     * 删除业务任务信息
     *
     * @param id 业务任务主键
     * @return 结果
     */
    @Override
    public int deleteBusinessTaskById(Long id) {
        return businessTaskMapper.deleteBusinessTaskById(id);
    }

    @Override
    public IPage<BusinessTaskForPeriodDTO> businessTaskListForPeriod(Long deptId, BusinessTaskVO vo) {
        Gson gson = new Gson();

//        log.info("businessTaskListForPeriod deptId={} vo={}", deptId, gson.toJson(vo));

        IPage<BusinessTaskForPeriodDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());

        Long userId = Objects.isNull(vo.getUserId()) ? SecurityUtils.getUserId() : vo.getUserId();
        UserDeptDTO userDept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();
//        log.info("businessTaskListForPeriod userDept={} vo={}", gson.toJson(userDept), gson.toJson(vo));
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return result;
        }

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
//        log.info("businessTaskListForPeriod deptId={} userDept={} tagSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return result;
        }

        //处理，服务标签搜索
        TagSearchVO customerServiceTagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getCustomerServiceTagIncludeFlag(), vo.getCustomerServiceTagName(), TagBusinessType.CUSTOMER_SERVICE, deptId);
//        log.info("businessTaskListForPeriod deptId={} userDept={} customerServiceTagSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(customerServiceTagSearchVO));
        if (customerServiceTagSearchVO.getNeedSearch() && customerServiceTagSearchVO.getFail()) {
            return result;
        }

        List<Long> searchCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            searchCustomerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(searchCustomerServiceIds)) {
                return result;
            }
        }

        if (!StringUtils.isEmpty(vo.getFirstCompleteTimeStart())) {
            vo.setFirstCompleteTimeStart(vo.getFirstCompleteTimeStart() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getFirstCompleteTimeEnd())) {
            vo.setFirstCompleteTimeEnd(vo.getFirstCompleteTimeEnd() + " 23:59:59");
        }

        //原始数据
        List<BusinessTaskForPeriodDTO> source = businessTaskMapper.searchListForPeriod(result, vo, tagSearchVO, customerServiceTagSearchVO, userDept, searchCustomerServiceIds);
//        log.info("businessTaskListForPeriod deptId={} userDept={} source={}", deptId, gson.toJson(userDept), gson.toJson(source));

        //处理数据
        if (!ObjectUtils.isEmpty(source)) {
            //拿会计
            List<Long> customerServicePeriodMonthIds = source.stream().map(BusinessTaskForPeriodDTO::getBizId).distinct().collect(Collectors.toList());
            List<Long> customerServiceIds = source.stream().map(BusinessTaskForPeriodDTO::getCustomerServiceId).distinct().collect(Collectors.toList());
            Map<Long, List<AccountingInfoSourceDTO>> accountingInfoSourceMap = iCustomerServiceDocHandoverService.getAccountingInfoSource(
                    customerServicePeriodMonthIds
            );

            //拿用户
            List<Long> adminUserIds = source.stream().filter(r -> r.getAdminUserId() != null).map(BusinessTaskForPeriodDTO::getAdminUserId).distinct().collect(Collectors.toList());
            List<Long> executeUserIds = source.stream().filter(r -> r.getExecuteUserId() != null).map(BusinessTaskForPeriodDTO::getExecuteUserId).distinct().collect(Collectors.toList());
            List<Long> lastOperateUserIdIds = source.stream().filter(r -> r.getLastOperateUserId() != null).map(BusinessTaskForPeriodDTO::getLastOperateUserId).distinct().collect(Collectors.toList());
            List<Long> allUserIds = handleIds(
                    Lists.newArrayList(adminUserIds, executeUserIds, lastOperateUserIdIds)
            );
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(allUserIds).getDataThrowException();

            // 拿所有部门
            Map<Long, String> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            // 拿账期
            Map<Long, CustomerServicePeriodMonth> periodMonthMap = customerServicePeriodMonthMapper.selectBatchIds(customerServicePeriodMonthIds).stream().collect(Collectors.toMap(CustomerServicePeriodMonth::getId, Function.identity()));
            // 拿服务
            Map<Long, CCustomerService> customerServiceMap = customerServiceMapper.selectBatchIds(customerServiceIds).stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
            // 拿服务标签
            Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServiceIds, TagBusinessType.CUSTOMER_SERVICE);
            // 拿账期标签
//            Map<Long, List<TagDTO>> customerServicePeriodTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServicePeriodMonthIds, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            // 银行材料
            Map<String, List<CustomerServiceCashierAccountingFile>> fileMap = customerServiceCashierAccountingService.selectBatchByPeriodIdAndBankAccountNumber(
                    source.stream().filter(s -> !Objects.isNull(s.getBizId()) && !StringUtils.isEmpty(s.getBankAccountNumber()))
                            .map(s -> PeriodBankAccountNumberVO.builder().periodId(s.getBizId())
                                    .bankAccountNumber(s.getBankAccountNumber())
                                    .build()).collect(Collectors.toList())
            );
            for (BusinessTaskForPeriodDTO row : source) {
                SysUser adminUser = sysUserMap.get(row.getAdminUserId());
                SysUser executeUser = sysUserMap.get(row.getExecuteUserId());
                SysUser lastOperateUser = sysUserMap.get(row.getLastOperateUserId());

                row.setAccountingEmployeeNameFull(CustomerServiceDocHandoverServiceImpl.handleAccountingEmployeeNameFull(accountingInfoSourceMap.get(row.getBizId())));
                row.setAdminUserName(adminUser == null ? null : adminUser.getNickName());
                row.setExecuteUserName(executeUser == null ? null : executeUser.getNickName());
                row.setFinishResultStr(row.getFinishResult() == null ? null : BusinessTaskFinishResult.getByCode(row.getFinishResult()).getName());
                row.setItemTypeStr(row.getItemType() == null ? null : BusinessTaskItemType.getByCode(row.getItemType()).getName());
                row.setLastOperateTypeStr(row.getLastOperateType() == null ? null : BusinessTaskOperateType.getByCode(row.getLastOperateType()).getName());
                row.setLastOperateUserName(lastOperateUser == null ? null : lastOperateUser.getNickName());
                row.setPeriodStr(row.getPeriod() == null ? null : DateUtils.periodToYeaMonth(row.getPeriod()));
                row.setStatusStr(row.getStatus() == null ? null : BusinessTaskStatus.getByCode(row.getStatus()).getName());
                CustomerServicePeriodMonth customerServicePeriodMonth = periodMonthMap.get(row.getBizId());
                CCustomerService customerService = customerServiceMap.get(row.getCustomerServiceId());
                row.setPeriodBusinessDeptName(Objects.isNull(customerServicePeriodMonth) ? "" : deptMap.getOrDefault(customerServicePeriodMonth.getBusinessDeptId(), ""));
                row.setCustomerServiceBusinessDeptName(Objects.isNull(customerService) || customerService.getIsDel() ? "" : deptMap.getOrDefault(customerService.getBusinessDeptId(), ""));
                row.setIsYiqiZhangtao(customerServiceTagMap.getOrDefault(row.getCustomerServiceId(), Lists.newArrayList()).stream()
                        .anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getYq())) ? "是" : "");
                row.setTaxTypeStr(Objects.isNull(customerServicePeriodMonth) || customerServicePeriodMonth.getTaxType() == null ? null : TaxType.getByCode(customerServicePeriodMonth.getTaxType()).getDesc());
                row.setMediumPaperStr(null == row.getMediumPaper() ? "" : (row.getMediumPaper() == 1 ? "有" : "无"));
                row.setMediumElectricStr(null == row.getMediumElectric() ? "" : (row.getMediumElectric() == 1 ? "有" : "无"));
                row.setMediumBankStr(null == row.getMediumBank() ? "" : (row.getMediumBank() == 1 ? "有" : "无"));
                row.setCanCheck(userDept.getIsAdmin() || (!ObjectUtils.isEmpty(userDept.getDeptIds()) && userDept.getDeptIds().contains(customerServicePeriodMonth.getAccountingDeptId())));
                row.setCustomerName(customerService == null ? null : customerService.getCustomerName());
                row.setCreditCode(customerService == null ? null : customerService.getCreditCode());
                row.setFirstCompleteTimeStr(Objects.isNull(row.getFirstCompleteTime()) ? "" : row.getFirstCompleteTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                row.setCreateTimeStr(Objects.isNull(row.getCreateTime()) ? "" : row.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                row.setBankInfo(StringUtils.isEmpty(row.getBankName()) || StringUtils.isEmpty(row.getBankAccountNumber()) ? "" : ("（" + row.getBankName() + "）" + row.getBankAccountNumber()));
                row.setBankMaterialFileCount(Objects.isNull(row.getBizId()) || StringUtils.isEmpty(row.getBankAccountNumber()) ? 0 : (long) fileMap.getOrDefault(row.getBizId() + "_" + row.getBankAccountNumber(), Lists.newArrayList()).size());
                row.setMattersNotesStr(StringUtils.isEmpty(row.getMattersNotes()) ? "无" : "有");
                row.setHasBankPaymentStr(Objects.isNull(row.getHasBankPayment()) ? "" : (row.getHasBankPayment() == 1 ? "有" : "无"));
                row.setStatementBalanceStr(Objects.isNull(row.getStatementBalance()) ? null : row.getStatementBalance().stripTrailingZeros().toPlainString());
            }
        }

        //返回数据
        result.setRecords(source);

        return result;
    }

    @Override
    public List<BusinessTaskAccountingDeptCountDTO> businessTaskCountForPeriod(Long deptId, BusinessTaskVO vo) {
        Gson gson = new Gson();

//        log.info("businessTaskListForPeriod deptId={} vo={}", deptId, gson.toJson(vo));

        Long userId = SecurityUtils.getUserId();
        UserDeptDTO userDept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();
//        log.info("businessTaskListForPeriod userDept={} vo={}", gson.toJson(userDept), gson.toJson(vo));
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return Lists.newArrayList();
        }

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
//        log.info("businessTaskListForPeriod deptId={} userDept={} tagSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return Lists.newArrayList();
        }

        //原始数据
        return businessTaskMapper.searchListCountForPeriod(vo, tagSearchVO, userDept).stream().sorted(Comparator.comparing(BusinessTaskAccountingDeptCountDTO::getAccountingDeptId)).collect(Collectors.toList());
    }

    @Override
    public List<BusinessTaskAccountingDeptCountDTO> businessTaskBusinessDeptCountForPeriod(Long deptId, BusinessTaskVO vo) {
        Gson gson = new Gson();

//        log.info("businessTaskListForPeriod deptId={} vo={}", deptId, gson.toJson(vo));

        Long userId = SecurityUtils.getUserId();
        UserDeptDTO userDept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();
//        log.info("businessTaskListForPeriod userDept={} vo={}", gson.toJson(userDept), gson.toJson(vo));
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return Lists.newArrayList();
        }

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
//        log.info("businessTaskListForPeriod deptId={} userDept={} tagSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return Lists.newArrayList();
        }

        //原始数据
        return businessTaskMapper.searchListBusinessDeptCountForPeriod(vo, tagSearchVO, userDept).stream().sorted(Comparator.comparing(BusinessTaskAccountingDeptCountDTO::getAccountingDeptId)).collect(Collectors.toList());
    }

    @Override
    public IPage<BusinessTaskForManageDTO> businessTaskListForManage(Long deptId, BusinessTaskVO vo) {
        Gson gson = new Gson();

//        log.info("businessTaskListForManage deptId={} vo={}", deptId, gson.toJson(vo));

        Long userId = Objects.isNull(vo.getUserId()) ? SecurityUtils.getUserId() : vo.getUserId();
        UserDeptDTO userDept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();

        //看监管人是自己的数据
        vo.setAdminUserId(userId);

//        log.info("businessTaskListForManage deptId={} vo2={}", deptId, gson.toJson(vo));

        IPage<BusinessTaskForManageDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
//        log.info("businessTaskListForManage deptId={} tagSearchVO={}", deptId, gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return result;
        }

        //处理，服务标签搜索
        TagSearchVO customerServiceTagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getCustomerServiceTagIncludeFlag(), vo.getCustomerServiceTagName(), TagBusinessType.CUSTOMER_SERVICE, deptId);
//        log.info("businessTaskListForPeriod deptId={} userDept={} customerServiceTagSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(customerServiceTagSearchVO));
        if (customerServiceTagSearchVO.getNeedSearch() && customerServiceTagSearchVO.getFail()) {
            return result;
        }


        List<Long> searchCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            searchCustomerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(searchCustomerServiceIds)) {
                return result;
            }
        }
        if (!StringUtils.isEmpty(vo.getFirstCompleteTimeStart())) {
            vo.setFirstCompleteTimeStart(vo.getFirstCompleteTimeStart() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getFirstCompleteTimeEnd())) {
            vo.setFirstCompleteTimeEnd(vo.getFirstCompleteTimeEnd() + " 23:59:59");
        }
        //原始数据
        List<BusinessTaskForManageDTO> source = businessTaskMapper.searchListForManage(result, vo, tagSearchVO, customerServiceTagSearchVO, null, searchCustomerServiceIds);
//        log.info("businessTaskListForManage deptId={} source={}", deptId, gson.toJson(source));

        //处理数据
        if (!ObjectUtils.isEmpty(source)) {
            List<Long> customerServicePeriodMonthIds = source.stream().map(BusinessTaskForManageDTO::getBizId).distinct().collect(Collectors.toList());
            List<Long> customerServiceIds = source.stream().map(BusinessTaskForManageDTO::getCustomerServiceId).distinct().collect(Collectors.toList());
            //拿用户
            List<Long> executeUserIds = source.stream().filter(r -> r.getExecuteUserId() != null).map(BusinessTaskForManageDTO::getExecuteUserId).distinct().collect(Collectors.toList());
            List<Long> lastOperateUserIdIds = source.stream().filter(r -> r.getLastOperateUserId() != null).map(BusinessTaskForManageDTO::getLastOperateUserId).distinct().collect(Collectors.toList());
            List<Long> allUserIds = handleIds(
                    Lists.newArrayList(executeUserIds, lastOperateUserIdIds)
            );
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(allUserIds).getDataThrowException();

            // 拿所有部门
            Map<Long, String> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            // 拿账期
            Map<Long, CustomerServicePeriodMonth> periodMonthMap = customerServicePeriodMonthMapper.selectBatchIds(customerServicePeriodMonthIds).stream().collect(Collectors.toMap(CustomerServicePeriodMonth::getId, Function.identity()));
            // 拿服务
            Map<Long, CCustomerService> customerServiceMap = customerServiceMapper.selectBatchIds(customerServiceIds).stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
            // 拿服务标签
            Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServiceIds, TagBusinessType.CUSTOMER_SERVICE);

            // 拿账期标签
//            Map<Long, List<TagDTO>> customerServicePeriodTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServicePeriodMonthIds, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);

            Map<Long, List<AccountingInfoSourceDTO>> accountingInfoSourceMap = iCustomerServiceDocHandoverService.getAccountingInfoSource(
                    customerServicePeriodMonthIds
            );

            // 银行材料
            // 银行材料
            Map<String, List<CustomerServiceCashierAccountingFile>> fileMap = customerServiceCashierAccountingService.selectBatchByPeriodIdAndBankAccountNumber(
                    source.stream().filter(s -> !Objects.isNull(s.getBizId()) && !StringUtils.isEmpty(s.getBankAccountNumber()))
                            .map(s -> PeriodBankAccountNumberVO.builder().periodId(s.getBizId())
                                    .bankAccountNumber(s.getBankAccountNumber())
                                    .build()).collect(Collectors.toList())
            );

            for (BusinessTaskForManageDTO row : source) {
                SysUser executeUser = sysUserMap.get(row.getExecuteUserId());
                SysUser lastOperateUser = sysUserMap.get(row.getLastOperateUserId());

                row.setAccountingEmployeeNameFull(CustomerServiceDocHandoverServiceImpl.handleAccountingEmployeeNameFull(accountingInfoSourceMap.get(row.getBizId())));
                row.setExecuteUserName(executeUser == null ? null : executeUser.getNickName());
                row.setFinishResultStr(row.getFinishResult() == null ? null : BusinessTaskFinishResult.getByCode(row.getFinishResult()).getName());
                row.setItemTypeStr(row.getItemType() == null ? null : BusinessTaskItemType.getByCode(row.getItemType()).getName());
                row.setLastOperateTypeStr(row.getLastOperateType() == null ? null : BusinessTaskOperateType.getByCode(row.getLastOperateType()).getName());
                row.setLastOperateUserName(lastOperateUser == null ? null : lastOperateUser.getNickName());
                row.setPeriodStr(row.getPeriod() == null ? null : DateUtils.periodToYeaMonth(row.getPeriod()));
                row.setStatusStr(row.getStatus() == null ? null : BusinessTaskStatus.getByCode(row.getStatus()).getName());
                CustomerServicePeriodMonth customerServicePeriodMonth = periodMonthMap.get(row.getBizId());
                CCustomerService customerService = customerServiceMap.get(row.getCustomerServiceId());
                row.setPeriodBusinessDeptName(Objects.isNull(customerServicePeriodMonth) ? "" : deptMap.getOrDefault(customerServicePeriodMonth.getBusinessDeptId(), ""));
                row.setCustomerServiceBusinessDeptName(Objects.isNull(customerService) || customerService.getIsDel() ? "" : deptMap.getOrDefault(customerService.getBusinessDeptId(), ""));
                row.setIsYiqiZhangtao(customerServiceTagMap.getOrDefault(row.getCustomerServiceId(), Lists.newArrayList()).stream()
                        .anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getYq())) ? "是" : "");
                row.setTaxTypeStr(Objects.isNull(customerServicePeriodMonth) || customerServicePeriodMonth.getTaxType() == null ? null : TaxType.getByCode(customerServicePeriodMonth.getTaxType()).getDesc());
                row.setMediumPaperStr(null == row.getMediumPaper() ? "" : (row.getMediumPaper() == 1 ? "有" : "无"));
                row.setMediumElectricStr(null == row.getMediumElectric() ? "" : (row.getMediumElectric() == 1 ? "有" : "无"));
                row.setMediumBankStr(null == row.getMediumBank() ? "" : (row.getMediumBank() == 1 ? "有" : "无"));
                row.setCanCheck(userDept.getIsAdmin() || (!ObjectUtils.isEmpty(userDept.getDeptIds()) && userDept.getDeptIds().contains(customerServicePeriodMonth.getAccountingDeptId())));
                row.setCustomerName(customerService == null ? null : customerService.getCustomerName());
                row.setCreditCode(customerService == null ? null : customerService.getCreditCode());
                row.setFirstCompleteTimeStr(Objects.isNull(row.getFirstCompleteTime()) ? "" : row.getFirstCompleteTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                row.setCreateTimeStr(Objects.isNull(row.getCreateTime()) ? "" : row.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                row.setBankInfo(StringUtils.isEmpty(row.getBankName()) || StringUtils.isEmpty(row.getBankAccountNumber()) ? "" : ("（" + row.getBankName() + "）" + row.getBankAccountNumber()));
                row.setBankMaterialFileCount(Objects.isNull(row.getBizId()) || StringUtils.isEmpty(row.getBankAccountNumber()) ? 0 : (long) fileMap.getOrDefault(row.getBizId() + "_" + row.getBankAccountNumber(), Lists.newArrayList()).size());
                row.setMattersNotesStr(StringUtils.isEmpty(row.getMattersNotes()) ? "无" : "有");
                row.setHasBankPaymentStr(Objects.isNull(row.getHasBankPayment()) ? "" : (row.getHasBankPayment() == 1 ? "有" : "无"));
                row.setStatementBalanceStr(Objects.isNull(row.getStatementBalance()) ? null : row.getStatementBalance().stripTrailingZeros().toPlainString());
            }
        }

        //返回数据
        result.setRecords(source);

        return result;
    }

    @Override
    public List<BusinessTaskAccountingDeptCountDTO> businessTaskCountForManager(Long deptId, BusinessTaskVO vo) {
        Gson gson = new Gson();

//        log.info("businessTaskListForManage deptId={} vo={}", deptId, gson.toJson(vo));

        //看监管人是自己的数据
        vo.setAdminUserId(SecurityUtils.getUserId());

//        log.info("businessTaskListForManage deptId={} vo2={}", deptId, gson.toJson(vo));

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
//        log.info("businessTaskListForManage deptId={} tagSearchVO={}", deptId, gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return Lists.newArrayList();
        }

        //原始数据
        return businessTaskMapper.searchListCountForManager(vo, tagSearchVO, null).stream().sorted(Comparator.comparing(BusinessTaskAccountingDeptCountDTO::getAccountingDeptId)).collect(Collectors.toList());
    }

    @Override
    public List<BusinessTaskAccountingDeptCountDTO> businessTaskBusinessDeptCountForManager(Long deptId, BusinessTaskVO vo) {
        Gson gson = new Gson();

//        log.info("businessTaskListForManage deptId={} vo={}", deptId, gson.toJson(vo));

        //看监管人是自己的数据
        vo.setAdminUserId(SecurityUtils.getUserId());

//        log.info("businessTaskListForManage deptId={} vo2={}", deptId, gson.toJson(vo));

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
//        log.info("businessTaskListForManage deptId={} tagSearchVO={}", deptId, gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return Lists.newArrayList();
        }

        //原始数据
        return businessTaskMapper.searchListBusinessDeptCountForManager(vo, tagSearchVO, null).stream().sorted(Comparator.comparing(BusinessTaskAccountingDeptCountDTO::getAccountingDeptId)).collect(Collectors.toList());
    }

    @Override
    public IPage<BusinessTaskForMyDTO> businessTaskListForMy(Long deptId, BusinessTaskVO vo) {
        Gson gson = new Gson();

//        log.info("businessTaskListForMy deptId={} vo={}", deptId, gson.toJson(vo));

        Long userId = Objects.isNull(vo.getUserId()) ? SecurityUtils.getUserId() : vo.getUserId();
        UserDeptDTO userDept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();

        //看监管人是自己的数据
        vo.setExecuteUserId(userId);

//        log.info("businessTaskListForMy deptId={} vo2={}", deptId, gson.toJson(vo));

        IPage<BusinessTaskForMyDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
//        log.info("businessTaskListForMy deptId={} tagSearchVO={}", deptId, gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return result;
        }

        //处理，服务标签搜索
        TagSearchVO customerServiceTagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getCustomerServiceTagIncludeFlag(), vo.getCustomerServiceTagName(), TagBusinessType.CUSTOMER_SERVICE, deptId);
//        log.info("businessTaskListForPeriod deptId={} userDept={} customerServiceTagSearchVO={}", deptId, gson.toJson(userDept), gson.toJson(customerServiceTagSearchVO));
        if (customerServiceTagSearchVO.getNeedSearch() && customerServiceTagSearchVO.getFail()) {
            return result;
        }

        List<Long> searchCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            searchCustomerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(searchCustomerServiceIds)) {
                return result;
            }
        }
        if (!StringUtils.isEmpty(vo.getFirstCompleteTimeStart())) {
            vo.setFirstCompleteTimeStart(vo.getFirstCompleteTimeStart() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getFirstCompleteTimeEnd())) {
            vo.setFirstCompleteTimeEnd(vo.getFirstCompleteTimeEnd() + " 23:59:59");
        }
        //原始数据
        List<BusinessTaskForMyDTO> source = businessTaskMapper.searchListForMy(result, vo, tagSearchVO, customerServiceTagSearchVO, searchCustomerServiceIds);
//        log.info("businessTaskListForMy deptId={} source={}", deptId, gson.toJson(source));

        //处理数据
        if (!ObjectUtils.isEmpty(source)) {
            List<Long> customerServicePeriodMonthIds = source.stream().map(BusinessTaskForMyDTO::getBizId).distinct().collect(Collectors.toList());
            List<Long> customerServiceIds = source.stream().map(BusinessTaskForMyDTO::getCustomerServiceId).distinct().collect(Collectors.toList());

            //拿用户
            List<Long> lastOperateUserIdIds = source.stream().filter(r -> r.getLastOperateUserId() != null).map(BusinessTaskForMyDTO::getLastOperateUserId).distinct().collect(Collectors.toList());
            List<Long> allUserIds = handleIds(
                    Lists.newArrayList(null, lastOperateUserIdIds)
            );
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(allUserIds).getDataThrowException();
            // 拿所有部门
            Map<Long, String> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            // 拿账期
            Map<Long, CustomerServicePeriodMonth> periodMonthMap = customerServicePeriodMonthMapper.selectBatchIds(customerServicePeriodMonthIds).stream().collect(Collectors.toMap(CustomerServicePeriodMonth::getId, Function.identity()));
            // 拿服务
            Map<Long, CCustomerService> customerServiceMap = customerServiceMapper.selectBatchIds(customerServiceIds).stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
            // 拿服务标签
            Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServiceIds, TagBusinessType.CUSTOMER_SERVICE);

            // 拿账期标签
//            Map<Long, List<TagDTO>> customerServicePeriodTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServicePeriodMonthIds, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);

            Map<Long, List<AccountingInfoSourceDTO>> accountingInfoSourceMap = iCustomerServiceDocHandoverService.getAccountingInfoSource(
                    customerServicePeriodMonthIds
            );

            // 银行材料
            // 银行材料
            Map<String, List<CustomerServiceCashierAccountingFile>> fileMap = customerServiceCashierAccountingService.selectBatchByPeriodIdAndBankAccountNumber(
                    source.stream().filter(s -> !Objects.isNull(s.getBizId()) && !StringUtils.isEmpty(s.getBankAccountNumber()))
                            .map(s -> PeriodBankAccountNumberVO.builder().periodId(s.getBizId())
                                    .bankAccountNumber(s.getBankAccountNumber())
                                    .build()).collect(Collectors.toList())
            );

            for (BusinessTaskForMyDTO row : source) {
                SysUser lastOperateUser = sysUserMap.get(row.getLastOperateUserId());

                row.setAccountingEmployeeNameFull(CustomerServiceDocHandoverServiceImpl.handleAccountingEmployeeNameFull(accountingInfoSourceMap.get(row.getBizId())));
                row.setFinishResultStr(row.getFinishResult() == null ? null : BusinessTaskFinishResult.getByCode(row.getFinishResult()).getName());
                row.setItemTypeStr(row.getItemType() == null ? null : BusinessTaskItemType.getByCode(row.getItemType()).getName());
                row.setLastOperateTypeStr(row.getLastOperateType() == null ? null : BusinessTaskOperateType.getByCode(row.getLastOperateType()).getName());
                row.setLastOperateUserName(lastOperateUser == null ? null : lastOperateUser.getNickName());
                row.setPeriodStr(row.getPeriod() == null ? null : DateUtils.periodToYeaMonth(row.getPeriod()));
                row.setStatusStr(row.getStatus() == null ? null : BusinessTaskStatus.getByCode(row.getStatus()).getName());
                CustomerServicePeriodMonth customerServicePeriodMonth = periodMonthMap.get(row.getBizId());
                CCustomerService customerService = customerServiceMap.get(row.getCustomerServiceId());
                row.setPeriodBusinessDeptName(Objects.isNull(customerServicePeriodMonth) ? "" : deptMap.getOrDefault(customerServicePeriodMonth.getBusinessDeptId(), ""));
                row.setCustomerServiceBusinessDeptName(Objects.isNull(customerService) || customerService.getIsDel() ? "" : deptMap.getOrDefault(customerService.getBusinessDeptId(), ""));
                row.setIsYiqiZhangtao(customerServiceTagMap.getOrDefault(row.getCustomerServiceId(), Lists.newArrayList()).stream()
                        .anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getYq())) ? "是" : "");
                row.setTaxTypeStr(Objects.isNull(customerServicePeriodMonth) || customerServicePeriodMonth.getTaxType() == null ? null : TaxType.getByCode(customerServicePeriodMonth.getTaxType()).getDesc());
                row.setMediumPaperStr(null == row.getMediumPaper() ? "" : (row.getMediumPaper() == 1 ? "有" : "无"));
                row.setMediumElectricStr(null == row.getMediumElectric() ? "" : (row.getMediumElectric() == 1 ? "有" : "无"));
                row.setMediumBankStr(null == row.getMediumBank() ? "" : (row.getMediumBank() == 1 ? "有" : "无"));
                row.setCanCheck(userDept.getIsAdmin() || (!ObjectUtils.isEmpty(userDept.getDeptIds()) && userDept.getDeptIds().contains(customerServicePeriodMonth.getAccountingDeptId())));
                row.setCustomerName(customerService == null ? null : customerService.getCustomerName());
                row.setCreditCode(customerService == null ? null : customerService.getCreditCode());
                row.setFirstCompleteTimeStr(Objects.isNull(row.getFirstCompleteTime()) ? "" : row.getFirstCompleteTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                row.setCreateTimeStr(Objects.isNull(row.getCreateTime()) ? "" : row.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                row.setBankInfo(StringUtils.isEmpty(row.getBankName()) || StringUtils.isEmpty(row.getBankAccountNumber()) ? "" : ("（" + row.getBankName() + "）" + row.getBankAccountNumber()));
                row.setBankMaterialFileCount(Objects.isNull(row.getBizId()) || StringUtils.isEmpty(row.getBankAccountNumber()) ? 0 : (long) fileMap.getOrDefault(row.getBizId() + "_" + row.getBankAccountNumber(), Lists.newArrayList()).size());
                row.setMattersNotesStr(StringUtils.isEmpty(row.getMattersNotes()) ? "无" : "有");
                row.setHasBankPaymentStr(Objects.isNull(row.getHasBankPayment()) ? "" : (row.getHasBankPayment() == 1 ? "有" : "无"));
                row.setStatementBalanceStr(Objects.isNull(row.getStatementBalance()) ? null : row.getStatementBalance().stripTrailingZeros().toPlainString());
            }
        }

        //返回数据
        result.setRecords(source);

        return result;
    }

    @Override
    public List<BusinessTaskAccountingDeptCountDTO> businessTaskCountForMy(Long deptId, BusinessTaskVO vo) {
        Gson gson = new Gson();

//        log.info("businessTaskListForMy deptId={} vo={}", deptId, gson.toJson(vo));

        //看监管人是自己的数据
        vo.setExecuteUserId(SecurityUtils.getUserId());

//        log.info("businessTaskListForMy deptId={} vo2={}", deptId, gson.toJson(vo));

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
//        log.info("businessTaskListForMy deptId={} tagSearchVO={}", deptId, gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return Lists.newArrayList();
        }

        //原始数据
        return businessTaskMapper.searchListCountForMy(vo, tagSearchVO).stream().sorted(Comparator.comparing(BusinessTaskAccountingDeptCountDTO::getAccountingDeptId)).collect(Collectors.toList());
    }

    @Override
    public List<BusinessTaskAccountingDeptCountDTO> businessTaskBusinessDeptCountForMy(Long deptId, BusinessTaskVO vo) {
        Gson gson = new Gson();

//        log.info("businessTaskListForMy deptId={} vo={}", deptId, gson.toJson(vo));

        //看监管人是自己的数据
        vo.setExecuteUserId(SecurityUtils.getUserId());

//        log.info("businessTaskListForMy deptId={} vo2={}", deptId, gson.toJson(vo));

        //处理，标签搜索
        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
//        log.info("businessTaskListForMy deptId={} tagSearchVO={}", deptId, gson.toJson(tagSearchVO));
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return Lists.newArrayList();
        }

        //原始数据
        return businessTaskMapper.searchListBusinessDeptCountForMy(vo, tagSearchVO).stream().sorted(Comparator.comparing(BusinessTaskAccountingDeptCountDTO::getAccountingDeptId)).collect(Collectors.toList());
    }


    @Override
    public BusinessTaskDetailDTO businessTaskDetail(Long deptId, Long id) {
        Gson gson = new Gson();

//        log.info("businessTaskDetail deptId={} vo={}", deptId, id);

        BusinessTask row = businessTaskMapper.selectById(id);
        if (row == null || row.getIsDel()) {
            throw new ServiceException("任务不存在");
        }

        //拿到文件
        Map<Long, Map<Integer, List<BusinessTaskFile>>> filesMap = iBusinessTaskFileService.selectMapBatchByMainIdsAndFileTypes(
                Lists.newArrayList(id),
                Lists.newArrayList(BusinessTaskFileType.BASE)
        );
        List<BusinessTaskFile> files = filesMap.getOrDefault(id, Maps.newHashMap())
                .get(BusinessTaskFileType.BASE.getCode());

        //拿用户
        List<Long> allUserIds = Lists.newArrayList(
                row.getAdminUserId(),
                row.getExecuteUserId(),
                row.getLastOperateUserId()
        );
        Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                allUserIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList())
        ).getDataThrowException();

        SysUser adminUser = row.getAdminUserId() == null ? null : sysUserMap.get(row.getAdminUserId());
        SysUser executeUser = row.getExecuteUserId() == null ? null : sysUserMap.get(row.getExecuteUserId());

        List<Integer> medium = getMedium(row);
        String mediumStr = Medium.getDescsByCodes(medium);

        Map<String, List<CustomerServiceCashierAccountingFile>> fileMap =
                Objects.isNull(row.getBizId()) || StringUtils.isEmpty(row.getBankAccountNumber()) ? Maps.newHashMap() :
                        customerServiceCashierAccountingService.selectBatchByPeriodIdAndBankAccountNumber(Collections.singletonList(PeriodBankAccountNumberVO.builder()
                .periodId(row.getBizId()).bankAccountNumber(row.getBankAccountNumber()).build()));
        List<CustomerServiceCashierAccountingFile> materialFiles = Objects.isNull(row.getBizId()) || StringUtils.isEmpty(row.getBankAccountNumber()) ? Lists.newArrayList() :
                fileMap.get(row.getBizId() + "_" + row.getBankAccountNumber());
        List<AccountingCashierSimpleDTO> flowCashierList = Lists.newArrayList();
        if (Objects.equals(row.getItemType(), BusinessTaskItemType.BANK_PAYMENT.getCode())) {
            List<CustomerServiceCashierAccounting> flowAccountingCashierList = customerServiceCashierAccountingService.list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, row.getBizId())
                    .eq(CustomerServiceCashierAccounting::getBankAccountNumber, row.getBankAccountNumber()));
            flowCashierList = flowAccountingCashierList.stream().map(item ->
                AccountingCashierSimpleDTO.builder().id(item.getId())
                        .title(item.getTitle())
                        .deliverStatus(item.getDeliverStatus())
                        .deliverStatusStr(AccountingCashierDeliverStatus.getByCode(item.getDeliverStatus()).getName())
                        .type(item.getType())
                        .customerServicePeriodMonthId(item.getCustomerServicePeriodMonthId())
                        .build()
            ).collect(Collectors.toList());
        }
        return BusinessTaskDetailDTO.builder()
                .adminUserId(row.getAdminUserId())
                .adminUserName(adminUser == null ? null : adminUser.getNickName())
                .deadline(row.getDeadline())
                .executeTime(row.getExecuteTime())
                .executeUserId(row.getExecuteUserId())
                .executeUserName(executeUser == null ? null : executeUser.getNickName())
                .files(iBusinessTaskFileService.covToCommonFileVO(files))
                .finishResult(row.getFinishResult())
                .finishResultStr(row.getFinishResult() == null ? null : BusinessTaskFinishResult.getByCode(row.getFinishResult()).getName())
                .id(row.getId())
                .remark(row.getRemark())
                .status(row.getStatus())
                .statusStr(row.getStatus() == null ? null : BusinessTaskStatus.getByCode(row.getStatus()).getName())
                .title(row.getTitle())
                .medium(medium)
                .mediumStr(mediumStr)
                .bankMaterialFileCount(ObjectUtils.isEmpty(materialFiles) ? 0 : (long) materialFiles.size())
                .mattersNotes(customerMattersNotesService.getMattersNotesByCustomerServiceIdAndItemType(row.getCustomerServiceId(), CustomerServiceMattersNotesItemType.BANK_PAYMENT.getCode()))
                .materialIntegrity(row.getMaterialIntegrity())
                .flowCashierList(flowCashierList)
                .statementBalanceStr(Objects.isNull(row.getStatementBalance()) ? null : row.getStatementBalance().stripTrailingZeros().toPlainString())
                .build();
    }

    private List<Integer> getMedium(BusinessTask row) {
        List<Integer> result = Lists.newArrayList();
        if (null != row.getMediumPaper() && row.getMediumPaper() == 1) {
            result.add(Medium.PAPER.getCode());
        }
        if (null != row.getMediumElectric() && row.getMediumElectric() == 1) {
            result.add(Medium.ELECTRONIC.getCode());
        }
        if (null != row.getMediumBank() && row.getMediumBank() == 1) {
            result.add(Medium.SILVER.getCode());
        }
        return result;
    }

    @Override
    public List<SelectDTO> getAdminUser(Long deptId) {
        //找角色“助理主管”的用户（去重）
        //112就是这个角色
        List<SysUser> sysUsers = remoteUserService.getAllByRole(112L).getDataThrowException();

        return sysUsers.stream().map(r -> SelectDTO.builder().id(r.getUserId()).name(r.getNickName()).build()).collect(Collectors.toList());
    }

    @Override
    public List<SelectDTO> getExecuteUser(Long deptId) {
        Gson gson = new Gson();

        List<SelectDTO> result = Lists.newArrayList();

        //操作者
        //就是监管人
        Long userId = SecurityUtils.getUserId();

        List<SysUser> sysUsers = remoteUserService.userByUserFromEmployeeAndDept(userId, deptId).getDataThrowException();
//        log.info("getExecuteUser userId={} sysUsers={}", userId, gson.toJson(sysUsers));
        if (ObjectUtils.isEmpty(sysUsers)) {
            return result;
        }

        return sysUsers.stream().map(r -> SelectDTO.builder().id(r.getUserId()).name(r.getNickName()).build()).collect(Collectors.toList());
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> addBatch(Long deptId, AddBatchVO vo) {
        Gson gson = new Gson();

//        log.info("BusinessTaskServiceImplAddBatch deptId={} vo={}", deptId, gson.toJson(vo));

        //操作者
        Long userId = SecurityUtils.getUserId();

        //入参
        List<AddBatchItemVO> items = vo.getItems();
        Long adminUserId = vo.getAdminUserId();
        LocalDate deadline = vo.getDeadline();
        Integer itemType = vo.getItemType();
        Integer type = vo.getType();

        if (!BusinessTaskType.isPeriod(vo.getType())) {
            throw new ServiceException("不支持的类型");
        }

        //结果
        List<BusinessTask> success = Lists.newArrayList();
        List<BusinessTask> fail = Lists.newArrayList();

        BusinessTaskItemType businessTaskItemType = BusinessTaskItemType.getByCode(itemType);

        //拿到用户
        Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                Lists.newArrayList(userId, adminUserId)
        ).getDataThrowException();
        SysUser adminUser = adminUserId == null ? null : sysUserMap.get(adminUserId);
        SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);

        Map<Long, CustomerServicePeriodMonth> customerServicePeriodMonthsMap = Maps.newHashMap();
        Map<Long, CCustomerService> cCustomerServicesMap = Maps.newHashMap();

        List<Long> bizIds = items.stream().map(AddBatchItemVO::getBizId).distinct().collect(Collectors.toList());

        if (!ObjectUtils.isEmpty(bizIds)) {
            List<CustomerServicePeriodMonth> customerServicePeriodMonths = customerServicePeriodMonthMapper.selectBatchIds(bizIds);
            customerServicePeriodMonthsMap = customerServicePeriodMonths.stream().collect(Collectors.toMap(CustomerServicePeriodMonth::getId, r -> r));

            if (!ObjectUtils.isEmpty(customerServicePeriodMonths)) {
                //拿客户服务
                List<Long> customerServiceIds = customerServicePeriodMonths.stream().map(CustomerServicePeriodMonth::getCustomerServiceId).distinct().collect(Collectors.toList());

                if (!ObjectUtils.isEmpty(customerServiceIds)) {
                    List<CCustomerService> cCustomerServices = icCustomerServiceService.listByIds(customerServiceIds);
                    cCustomerServicesMap = cCustomerServices.stream().collect(Collectors.toMap(CCustomerService::getId, r -> r));
                }
            }
        }
        int mediumPaper = 0;
        int mediumElectric = 0;
        int mediumBank = 0;
        if (!ObjectUtils.isEmpty(vo.getMedium())) {
            mediumPaper = vo.getMedium().contains(Medium.PAPER.getCode()) ? 1 : 0;
            mediumElectric = vo.getMedium().contains(Medium.ELECTRONIC.getCode()) ? 1 : 0;
            mediumBank = vo.getMedium().contains(Medium.SILVER.getCode()) ? 1 : 0;
        }

        for (AddBatchItemVO item : items) {
            BusinessTask newEntry = new BusinessTask();
            newEntry.setBizId(item.getBizId());

            CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthsMap.get(item.getBizId());
            if (customerServicePeriodMonth == null) {
                fail.add(newEntry);
                continue;
            }

            CCustomerService cCustomerService = cCustomerServicesMap.get(customerServicePeriodMonth.getCustomerServiceId());
            if (cCustomerService == null) {
                fail.add(newEntry);
                continue;
            }

            newEntry.setAdminUserId(adminUserId);
            newEntry.setAdminUserName(adminUser == null ? null : adminUser.getNickName());
            newEntry.setAssignTime(null);
            newEntry.setCreditCode(cCustomerService.getCreditCode());
            newEntry.setCustomerCompanyName(cCustomerService.getCustomerCompanyName());
            newEntry.setCustomerName(cCustomerService.getCustomerName());
            newEntry.setCustomerServiceId(cCustomerService.getId());
            newEntry.setDeadline(deadline);
            newEntry.setEndTime(null);
            newEntry.setExecuteTime(null);
            newEntry.setExecuteUserId(null);
            newEntry.setExecuteUserName(null);
            newEntry.setFinishResult(null);
            newEntry.setFinishTime(null);
            newEntry.setFinishUserId(null);
            //newEntry.setId();
            newEntry.setIsAssign(null);
            newEntry.setIsDel(false);
            newEntry.setItemType(itemType);
            newEntry.setLastOperateTime(LocalDateTime.now());
            newEntry.setLastOperateType(BusinessTaskOperateType.NEW.getCode());
            newEntry.setLastOperateUserId(userId);
            newEntry.setLastOperateUserName(operatorUser == null ? null : operatorUser.getNickName());
            newEntry.setPeriod(customerServicePeriodMonth.getPeriod());
            newEntry.setPeriodAccountingDeptId(customerServicePeriodMonth.getAccountingDeptId());
            newEntry.setPeriodAdvisorDeptId(customerServicePeriodMonth.getAdvisorDeptId());
            newEntry.setRemark(null);
            newEntry.setStatus(BusinessTaskStatus.NEED_FINISH.getCode());
            newEntry.setTaxType(cCustomerService.getTaxType());
            newEntry.setTitle(businessTaskItemType.getName() + "任务单【" + customerServicePeriodMonth.getPeriod() + "】");
            newEntry.setType(type);
            newEntry.setMediumPaper(mediumPaper);
            newEntry.setMediumElectric(mediumElectric);
            newEntry.setMediumBank(mediumBank);
            newEntry.setCreateBy(Objects.isNull(operatorUser) ? "系统" : operatorUser.getNickName());

            success.add(newEntry);
        }

        //存数据
        saveBatch(success);

        List<BusinessTask> total = Lists.newArrayList(success);
        total.addAll(fail);

        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        //获取操作人员信息
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        //插入操作日志
        success.forEach(row -> {
            try {
                Map<String, Object> map = Maps.newLinkedHashMap();
                String operContent = JSONObject.toJSONString(map);

                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(row.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType("新建任务单")
                                .setOperName(operateUserInfoDTO.getOperName())
                                .setOperContent(operContent)
                                .setOperRemark(null)
                                .setOperImages(null)
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });

        return result;
    }

    @Override
    @Transactional
    public void remoteAddSingle(RemoteBusinessTaskAddVO vo) {
        //操作者
        Long userId = vo.getUserId();

        //入参
        Long adminUserId = vo.getAdminUserId();
        Integer itemType = vo.getItemType();
        Integer type = vo.getType();

        if (!BusinessTaskType.isPeriod(vo.getType())) {
            throw new ServiceException("不支持的类型");
        }

        BusinessTaskItemType businessTaskItemType = BusinessTaskItemType.getByCode(itemType);

        //拿到用户
        Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                Lists.newArrayList(userId, adminUserId)
        ).getDataThrowException();
        SysUser adminUser = adminUserId == null ? null : sysUserMap.get(adminUserId);

        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(vo.getPeriodId());
        if (Objects.isNull(customerServicePeriodMonth)) {
            throw new ServiceException("账期不存在");
        }
        CCustomerService cCustomerService = customerServiceMapper.selectById(customerServicePeriodMonth.getCustomerServiceId());
        if (Objects.isNull(cCustomerService) || cCustomerService.getIsDel()) {
            throw new ServiceException("服务不存在");
        }
        int mediumPaper = 0;
        int mediumElectric = 0;
        int mediumBank = 0;
        if (!ObjectUtils.isEmpty(vo.getMedium())) {
            mediumPaper = vo.getMedium().contains(Medium.PAPER.getCode()) ? 1 : 0;
            mediumElectric = vo.getMedium().contains(Medium.ELECTRONIC.getCode()) ? 1 : 0;
            mediumBank = vo.getMedium().contains(Medium.SILVER.getCode()) ? 1 : 0;
        }

        LocalDateTime operTime = LocalDateTime.now();
        BusinessTask newEntry = new BusinessTask();
        newEntry.setBizId(vo.getPeriodId());

        newEntry.setAdminUserId(adminUserId);
        newEntry.setAdminUserName(adminUser == null ? null : adminUser.getNickName());
        newEntry.setAssignTime(null);
        newEntry.setCreditCode(cCustomerService.getCreditCode());
        newEntry.setCustomerCompanyName(cCustomerService.getCustomerCompanyName());
        newEntry.setCustomerName(cCustomerService.getCustomerName());
        newEntry.setCustomerServiceId(cCustomerService.getId());
        newEntry.setEndTime(null);
        newEntry.setExecuteTime(null);
        newEntry.setExecuteUserId(null);
        newEntry.setExecuteUserName(null);
        newEntry.setFinishResult(null);
        newEntry.setFinishTime(null);
        newEntry.setFinishUserId(null);
        newEntry.setIsAssign(null);
        newEntry.setIsDel(false);
        newEntry.setItemType(itemType);
        newEntry.setLastOperateTime(operTime);
        newEntry.setLastOperateType(BusinessTaskOperateType.NEW.getCode());
        newEntry.setLastOperateUserId(userId);
        newEntry.setLastOperateUserName(vo.getOperName());
        newEntry.setPeriod(customerServicePeriodMonth.getPeriod());
        newEntry.setPeriodAccountingDeptId(customerServicePeriodMonth.getAccountingDeptId());
        newEntry.setPeriodAdvisorDeptId(customerServicePeriodMonth.getAdvisorDeptId());
        newEntry.setRemark(null);
        newEntry.setStatus(BusinessTaskStatus.NEED_FINISH.getCode());
        newEntry.setTaxType(cCustomerService.getTaxType());
        newEntry.setTitle(businessTaskItemType.getName() + "任务单【" + customerServicePeriodMonth.getPeriod() + "】");
        newEntry.setType(type);
        newEntry.setMediumPaper(mediumPaper);
        newEntry.setMediumElectric(mediumElectric);
        newEntry.setMediumBank(mediumBank);
        newEntry.setCreateBy(vo.getOperName());
        save(newEntry);

        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(newEntry.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(vo.getDeptId())
                            .setOperType("新建任务单")
                            .setOperName(vo.getOperName())
                            .setOperContent("")
                            .setOperRemark(null)
                            .setOperImages(null)
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> updateBatch(Long deptId, UpdateBatchVO vo) {
        Gson gson = new Gson();

//        log.info("BusinessTaskServiceImplUpdateBatch deptId={} vo={}", deptId, gson.toJson(vo));

        //操作者
        Long userId = SecurityUtils.getUserId();

        //入参
        List<Long> ids = vo.getIds();
        LocalDate deadline = vo.getDeadline();
        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();

        //结果
        List<BusinessTask> success = Lists.newArrayList();
        List<BusinessTask> fail = Lists.newArrayList();

        //背刺涉及到的全部数据
        List<BusinessTask> total = list(
                new LambdaQueryWrapper<BusinessTask>()
                        .in(BusinessTask::getId, ids)
        );

        if (ObjectUtils.isEmpty(total)) {
            throw new ServiceException("数据异常");
        }

        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();
        Map<Long, CCustomerService> customerMap = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).in(CCustomerService::getId, total.stream().map(BusinessTask::getCustomerServiceId).distinct().collect(Collectors.toList())))
                .stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
        Map<Long, CustomerServicePeriodMonth> periodMap = customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getId, total.stream().map(BusinessTask::getBizId).distinct().collect(Collectors.toList())))
                .stream().collect(Collectors.toMap(CustomerServicePeriodMonth::getId, Function.identity()));

        boolean canModifyDDL = true;
        //区分是否可惭怍
        for (BusinessTask row : total) {
            if (BusinessTaskStatus.canUpdate(row.getStatus())) {
                CCustomerService customerService = customerMap.get(row.getCustomerServiceId());
                CustomerServicePeriodMonth period = periodMap.get(row.getBizId());
                if (!Objects.equals(row.getDeadline(), vo.getDeadline())) {
                    if (userDeptDTO.getIsAdmin() ||
                            (!Objects.isNull(customerService) && !Objects.isNull(customerService.getAccountingDeptId()) && userDeptDTO.getDeptIds().contains(customerService.getAccountingDeptId())) ||
                            (!Objects.isNull(period) && !Objects.isNull(period.getAccountingDeptId()) && userDeptDTO.getDeptIds().contains(period.getAccountingDeptId()))) {
                        success.add(row);
                    } else {
                        fail.add(row);
                        canModifyDDL = false;
                    }
                } else {
                    success.add(row);
                }
            } else {
                fail.add(row);
            }
        }

        if (vo.getIds().size() == 1 && !canModifyDDL) {
            throw new ServiceException("非账期会计或服务会计， 不允许修改ddl");
        }

//        int mediumPaper = 0;
//        int mediumElectric = 0;
//        int mediumBank = 0;
//        if (!ObjectUtils.isEmpty(vo.getMedium())) {
//            mediumPaper = vo.getMedium().contains(Medium.PAPER.getCode()) ? 1 : 0;
//            mediumElectric = vo.getMedium().contains(Medium.ELECTRONIC.getCode()) ? 1 : 0;
//            mediumBank = vo.getMedium().contains(Medium.SILVER.getCode()) ? 1 : 0;
//        }
        if (!ObjectUtils.isEmpty(success)) {
            //拿到用户
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                    Lists.newArrayList(userId)
            ).getDataThrowException();
            SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);

            Map<Long, String> customerNameMap = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                    .eq(CCustomerService::getIsDel, fail)
                    .in(CCustomerService::getId, success.stream().map(BusinessTask::getCustomerServiceId).distinct().collect(Collectors.toList())))
                    .stream().collect(Collectors.toMap(CCustomerService::getId, CCustomerService::getCustomerName));

            //获取操作人员信息
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

            List<RemoteUserMessageVO> messageList = Lists.newArrayList();

            for (BusinessTask row : success) {
                Long id = row.getId();

                if (!Objects.equals(deadline, row.getDeadline()) && !Objects.isNull(row.getExecuteUserId())) {
                    messageList.add(RemoteUserMessageVO.builder()
                            .sendEmployeeName(operateUserInfoDTO.getOperName())
                            .receiveUserId(row.getExecuteUserId()).receiveEmployeeId(0L).receiveEmployeeName(row.getExecuteUserName())
                            .receiveDeptId(0L).receiveDeptName("").content(String.format("%s %s DDL修改为：%s，请及时完成", customerNameMap.getOrDefault(row.getCustomerServiceId(), ""), row.getTitle(), deadline.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))).messageType(2)
                            .build());
                }
                row.setDeadline(deadline);
                if (!StringUtils.isEmpty(remark)) {
                    row.setRemark(remark);
                    row.setModifyRemark(remark);
                }

//                row.setMediumPaper(mediumPaper);
//                row.setMediumElectric(mediumElectric);
//                row.setMediumBank(mediumBank);


                handleOperator(row, operatorUser, BusinessTaskOperateType.UPDATE);

                //先删除文件
                iBusinessTaskFileService.deleteByMainIdAndFileTypes(Lists.newArrayList(id), Lists.newArrayList(BusinessTaskFileType.BASE));

                //再存文件
                iBusinessTaskFileService.saveFile(id, vo.getFiles(), BusinessTaskFileType.BASE, String.valueOf(id));
            }

            if (!ObjectUtils.isEmpty(messageList)) {
                remoteUserService.sendMessage(messageList);
            }

            //批量更新
            updateBatchById(success);

            //插入操作日志
            success.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("截止日期", deadline == null ? "" : deadline);
                    if (!StringUtils.isEmpty(remark)) {
                        map.put("备注", remark);
                    }
//                    map.put("介质", Medium.getDescsByCodes(vo.getMedium()));
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("修改任务单")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark(null)
                                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        return result;
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> closeBatch(Long deptId, CloseBatchVO vo) {
        Gson gson = new Gson();

//        log.info("BusinessTaskServiceImplCloseBatch deptId={} vo={}", deptId, gson.toJson(vo));

        //操作者
        Long userId = SecurityUtils.getUserId();

        //入参
        List<Long> ids = vo.getIds();
        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();

        //结果
        List<BusinessTask> success = Lists.newArrayList();
        List<BusinessTask> fail = Lists.newArrayList();

        //背刺涉及到的全部数据
        List<BusinessTask> total = list(
                new LambdaQueryWrapper<BusinessTask>()
                        .in(BusinessTask::getId, ids)
        );

        //区分是否可惭怍
        for (BusinessTask row : total) {
            if (BusinessTaskStatus.canClose(row.getStatus())) {
                success.add(row);
            } else {
                fail.add(row);
            }
        }

        if (!ObjectUtils.isEmpty(success)) {
            //拿到用户
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                    Lists.newArrayList(userId)
            ).getDataThrowException();
            SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);

            for (BusinessTask row : success) {
                Long id = row.getId();

                handleOperator(row, operatorUser, BusinessTaskOperateType.CLOSE);

                row.setStatus(BusinessTaskStatus.CLOSED.getCode());
                row.setCloseTime(LocalDateTime.now());

                //先删除文件
                iBusinessTaskFileService.deleteByMainIdAndFileTypes(Lists.newArrayList(id), Lists.newArrayList(BusinessTaskFileType.CLOSE));

                //再存文件
                iBusinessTaskFileService.saveFile(id, vo.getFiles(), BusinessTaskFileType.CLOSE, String.valueOf(id));
            }

            //批量更新
            updateBatchById(success);

            //获取操作人员信息
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

            //插入操作日志
            success.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("备注", remark);
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("关闭任务单")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark(null)
                                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        return result;
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> deleteBatch(Long deptId, DeleteBatchVO vo) {
        Gson gson = new Gson();

//        log.info("BusinessTaskServiceImplDeleteBatch deptId={} vo={}", deptId, gson.toJson(vo));

        //操作者
        Long userId = SecurityUtils.getUserId();

        //入参
        List<Long> ids = vo.getIds();
        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();

        //结果
        List<BusinessTask> success = Lists.newArrayList();
        List<BusinessTask> fail = Lists.newArrayList();

        //背刺涉及到的全部数据
        List<BusinessTask> total = list(
                new LambdaQueryWrapper<BusinessTask>()
                        .in(BusinessTask::getId, ids)
        );

        //区分是否可惭怍
        for (BusinessTask row : total) {
//            if (row.getExecuteUserId() == null) {
//                //可操作的条件：没有执行人的数据。确定后再进行操作。
//                success.add(row);
//            } else {
//                fail.add(row);
//            }
            success.add(row);
        }

        if (!ObjectUtils.isEmpty(success)) {
            //拿到用户
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                    Lists.newArrayList(userId)
            ).getDataThrowException();
            SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);

            for (BusinessTask row : success) {
                Long id = row.getId();

                handleOperator(row, operatorUser, BusinessTaskOperateType.DELETE);

                row.setIsDel(true);

                //先删除文件
                iBusinessTaskFileService.deleteByMainIdAndFileTypes(Lists.newArrayList(id), Lists.newArrayList(BusinessTaskFileType.DELETE));

                //再存文件
                iBusinessTaskFileService.saveFile(id, vo.getFiles(), BusinessTaskFileType.DELETE, String.valueOf(id));
            }

            //批量更新
            updateBatchById(success);

            //获取操作人员信息
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

            //插入操作日志
            success.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("备注", remark);
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("删除任务单")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark(null)
                                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        return result;
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> distributeBatch(Long deptId, DistributeBatchVO vo) {
        Gson gson = new Gson();

//        log.info("BusinessTaskServiceImplDistributeBatch deptId={} vo={}", deptId, gson.toJson(vo));

        //操作者
        Long userId = SecurityUtils.getUserId();

        //入参
        List<Long> ids = vo.getIds();
        Long adminUserId = vo.getAdminUserId();
        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();

        //结果
        List<BusinessTask> success = Lists.newArrayList();
        List<BusinessTask> fail = Lists.newArrayList();

        //背刺涉及到的全部数据
        List<BusinessTask> total = list(
                new LambdaQueryWrapper<BusinessTask>()
                        .in(BusinessTask::getId, ids)
        );

        //区分是否可惭怍
        for (BusinessTask row : total) {
            if (BusinessTaskStatus.canDistribute(row.getStatus())) {
                success.add(row);
            } else {
                fail.add(row);
            }
        }

        if (!ObjectUtils.isEmpty(success)) {
            //拿到用户
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                    Lists.newArrayList(userId, adminUserId)
            ).getDataThrowException();
            SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);
            SysUser adminUserUser = adminUserId == null ? null : sysUserMap.get(adminUserId);

            Map<Long, String> oldAdminUserMap = Maps.newHashMap();//临时存放原来的执行人

            for (BusinessTask row : success) {
                Long id = row.getId();

                if (row.getAdminUserId() != null) {
                    oldAdminUserMap.put(id, row.getAdminUserName());
                }

                row.setAdminUserId(adminUserId);
                row.setAdminUserName(adminUserUser == null ? null : adminUserUser.getNickName());

                handleOperator(row, operatorUser, BusinessTaskOperateType.DISTRIBUTE);

                //先删除文件
                iBusinessTaskFileService.deleteByMainIdAndFileTypes(Lists.newArrayList(id), Lists.newArrayList(BusinessTaskFileType.DISTRIBUTE));

                //再存文件
                iBusinessTaskFileService.saveFile(id, vo.getFiles(), BusinessTaskFileType.DISTRIBUTE, String.valueOf(id));
            }

            //批量更新
            updateBatchById(success);

            update(new LambdaUpdateWrapper<BusinessTask>().in(BusinessTask::getId, success.stream().map(BusinessTask::getId).collect(Collectors.toList()))
                    .set(BusinessTask::getExecuteUserId, null)
                    .set(BusinessTask::getExecuteUserName, null)
                    .set(BusinessTask::getExecuteTime, null));

            //获取操作人员信息
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

            //插入操作日志
            success.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("监管人", adminUserUser == null ? "" : adminUserUser.getNickName());
                    map.put("备注", remark);
                    String operContent = JSONObject.toJSONString(map);

                    String operRemark = null;
                    if (oldAdminUserMap.get(row.getId()) != null) {
                        operRemark = "原监管人：" + oldAdminUserMap.get(row.getId());
                    }

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("重新分单")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark(operRemark)
                                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        return result;
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> assignBatch(Long deptId, AssignBatchVO vo) {
        Gson gson = new Gson();

//        log.info("BusinessTaskServiceImplAssignBatch deptId={} vo={}", deptId, gson.toJson(vo));

        //操作者
        Long userId = SecurityUtils.getUserId();

        //入参
        List<Long> ids = vo.getIds();
        Long executeUserId = vo.getExecuteUserId();
        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();

        //结果
        List<BusinessTask> success = Lists.newArrayList();
        List<BusinessTask> fail = Lists.newArrayList();

        //背刺涉及到的全部数据
        List<BusinessTask> total = list(
                new LambdaQueryWrapper<BusinessTask>()
                        .in(BusinessTask::getId, ids)
        );

        //区分是否可惭怍
        for (BusinessTask row : total) {
            if (BusinessTaskStatus.canAssign(row.getStatus())) {
                success.add(row);
            } else {
                fail.add(row);
            }
        }

        if (!ObjectUtils.isEmpty(success)) {
            LocalDateTime now = LocalDateTime.now();

            //拿到用户
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                    Lists.newArrayList(userId, executeUserId)
            ).getDataThrowException();
            SysUser executeUser = executeUserId == null ? null : sysUserMap.get(executeUserId);
            SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);

            Map<Long, String> oldExecuteUserMap = Maps.newHashMap();//临时存放原来的执行人

            for (BusinessTask row : success) {
                Long id = row.getId();

                if (row.getExecuteUserId() != null) {
                    oldExecuteUserMap.put(id, row.getExecuteUserName());
                }

                row.setIsAssign(true);
                row.setAssignTime(now);

                row.setExecuteUserId(executeUserId);
                row.setExecuteUserName(executeUser == null ? null : executeUser.getNickName());

                handleOperator(row, operatorUser, BusinessTaskOperateType.ASSIGN);

                //先删除文件
                iBusinessTaskFileService.deleteByMainIdAndFileTypes(Lists.newArrayList(id), Lists.newArrayList(BusinessTaskFileType.ASSIGN));

                //再存文件
                iBusinessTaskFileService.saveFile(id, vo.getFiles(), BusinessTaskFileType.ASSIGN, String.valueOf(id));
            }

            //批量更新
            updateBatchById(success);

            //获取操作人员信息
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

            //插入操作日志
            success.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("执行人", executeUser == null ? "" : executeUser.getNickName());
                    map.put("备注", remark);
                    String operContent = JSONObject.toJSONString(map);

                    String operRemark = null;
                    if (oldExecuteUserMap.get(row.getId()) != null) {
                        operRemark = "原执行人：" + oldExecuteUserMap.get(row.getId());
                    }

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("派单")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark(operRemark)
                                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        return result;
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> finishBatch(Long deptId, FinishBatchVO vo) {
        Gson gson = new Gson();

//        log.info("BusinessTaskServiceImplDistributeBatch deptId={} vo={}", deptId, gson.toJson(vo));

        //操作者
        Long userId = SecurityUtils.getUserId();

        //入参
        List<Long> ids = vo.getIds();
        Integer finishResult = vo.getFinishResult();
        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();
        boolean isCannotFinish = Objects.equals(finishResult, BusinessTaskFinishResult._6.getCode());
        boolean firstCompleteTimeFlag = Objects.equals(finishResult, BusinessTaskFinishResult.OK.getCode()) || Objects.equals(finishResult, BusinessTaskFinishResult._4.getCode());
        //当结果为无需交付或无法完成时，备注必填
        if (Objects.equals(finishResult, BusinessTaskFinishResult._5.getCode()) || Objects.equals(finishResult, BusinessTaskFinishResult._6.getCode())) {
            if (StringUtils.isEmpty(remark)) {
                throw new ServiceException("备注必填");
            }
        }
        //当结果为正常时，对账单余额必填
        boolean isNormal = Objects.equals(finishResult, BusinessTaskFinishResult.OK.getCode()) || Objects.equals(finishResult, BusinessTaskFinishResult._2.getCode());
        if (isNormal) {
            if (Objects.isNull(vo.getStatementBalance())) {
                throw new ServiceException("对账单余额不能为空");
            }
        }
        BigDecimal statementBalance = isNormal ? vo.getStatementBalance() : null;

        //结果
        List<BusinessTask> success = Lists.newArrayList();
        List<BusinessTask> fail = Lists.newArrayList();

        //背刺涉及到的全部数据
        List<BusinessTask> total = list(
                new LambdaQueryWrapper<BusinessTask>()
                        .in(BusinessTask::getId, ids)
        );

        //区分是否可惭怍
        for (BusinessTask row : total) {
            if (BusinessTaskStatus.canFinish(row.getStatus())) {
                success.add(row);
            } else {
                fail.add(row);
            }
        }

        if (!ObjectUtils.isEmpty(success)) {
            //拿到用户
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                    Lists.newArrayList(userId)
            ).getDataThrowException();
            SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);

            for (BusinessTask row : success) {
                Long id = row.getId();

                row.setFinishResult(finishResult);
                row.setFinishTime(LocalDateTime.now());
                row.setFinishUserId(userId);
                row.setStatementBalance(statementBalance);

                if (!StringUtils.isEmpty(remark)) {
                    row.setRemark(remark);
                    row.setFinishRemark(remark);
                }

                row.setExecuteTime(LocalDateTime.now());
                row.setMaterialIntegrity(vo.getMaterialIntegrity());

                handleOperator(row, operatorUser, BusinessTaskOperateType.FINISH);

                //当结果是无法完成时，后置状态为：异常
                //当结果不为“无法完成”时，后置状态为：待审核
                if (isCannotFinish) {
                    row.setStatus(BusinessTaskStatus.EXCEPTION.getCode());
                } else {
                    row.setStatus(BusinessTaskStatus.NEED_CHECK.getCode());
                }

                // 当结果是“正常完成”或“银行部分缺”，且首次完成时间没有值的时候，写值
                if (firstCompleteTimeFlag && Objects.isNull(row.getFirstCompleteTime())) {
                    row.setFirstCompleteTime(LocalDateTime.now());
                }

                //先删除文件
                iBusinessTaskFileService.deleteByMainIdAndFileTypes(Lists.newArrayList(id), Lists.newArrayList(BusinessTaskFileType.FINISH));

                //再存文件
                iBusinessTaskFileService.saveFile(id, vo.getFiles(), BusinessTaskFileType.FINISH, String.valueOf(id));
            }

            //批量更新
            updateBatchById(success);

            if (!isNormal) {
                update(new LambdaUpdateWrapper<BusinessTask>()
                        .set(BusinessTask::getStatementBalance, null)
                        .in(BusinessTask::getId, success.stream().map(BusinessTask::getId).collect(Collectors.toList())));
            }

            //获取操作人员信息
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

            Map<String, Object> map = Maps.newLinkedHashMap();
            map.put("结果", finishResult == null ? "" : BusinessTaskFinishResult.getByCode(finishResult).getName());
            map.put("备注", remark);
            map.put("材料完整", MaterialIntegrity.getByCode(vo.getMaterialIntegrity()).getDesc());
            if (!Objects.isNull(statementBalance)) {
                map.put("对账单余额", statementBalance.stripTrailingZeros().toPlainString());
            }
            String operContent = JSONObject.toJSONString(map);
            //插入操作日志
            success.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("完成任务")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark(null)
                                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        return result;
    }

    @Override
    @Transactional
    public void remoteFinishSingle(RemoteBusinessTaskFinishVO vo) {
        BusinessTask businessTask = getById(vo.getId());
        if (Objects.isNull(businessTask) || businessTask.getIsDel()) {
            throw new ServiceException("任务不存在");
        }
        Long userId = vo.getUserId();
        Integer finishResult = vo.getFinishResult();
        String remark = vo.getRemark();
        boolean isCannotFinish = Objects.equals(finishResult, BusinessTaskFinishResult._6.getCode());
        boolean firstCompleteTimeFlag = Objects.equals(finishResult, BusinessTaskFinishResult.OK.getCode()) || Objects.equals(finishResult, BusinessTaskFinishResult._4.getCode());
        //当结果为无需交付或无法完成时，备注必填
        if (Objects.equals(finishResult, BusinessTaskFinishResult._5.getCode()) || Objects.equals(finishResult, BusinessTaskFinishResult._6.getCode())) {
            if (StringUtils.isEmpty(remark)) {
                throw new ServiceException("备注必填");
            }
        }
        if (!BusinessTaskStatus.canFinish(businessTask.getStatus())) {
            throw new ServiceException("当前任务状态不可完成");
        }
        boolean isNormal = Objects.equals(finishResult, BusinessTaskFinishResult.OK.getCode()) || Objects.equals(finishResult, BusinessTaskFinishResult._2.getCode());
        LambdaUpdateWrapper<BusinessTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BusinessTask::getId, vo.getId())
                .set(BusinessTask::getFinishResult, finishResult)
                .set(BusinessTask::getFinishTime, LocalDateTime.now())
                .set(BusinessTask::getFinishUserId, userId)
                .set(BusinessTask::getExecuteTime, LocalDateTime.now())
                .set(!StringUtils.isEmpty(remark), BusinessTask::getRemark, remark)
                .set(!StringUtils.isEmpty(remark), BusinessTask::getFinishRemark, remark)
                .set(BusinessTask::getStatementBalance, isNormal ? vo.getStatementBalance() : null)
                .set(BusinessTask::getMaterialIntegrity, vo.getMaterialIntegrity())
                .set(BusinessTask::getLastOperateType, BusinessTaskOperateType.FINISH.getCode())
                .set(BusinessTask::getLastOperateUserId, userId)
                .set(BusinessTask::getLastOperateTime, LocalDateTime.now())
                .set(BusinessTask::getLastOperateUserName, vo.getOperName());
        //当结果是无法完成时，后置状态为：异常
        //当结果不为“无法完成”时，后置状态为：待审核
        if (isCannotFinish) {
            updateWrapper.set(BusinessTask::getStatus, BusinessTaskStatus.EXCEPTION.getCode());
        } else {
            updateWrapper.set(BusinessTask::getStatus, BusinessTaskStatus.NEED_CHECK.getCode());
        }
        // 当结果是“正常完成”或“银行部分缺”，且首次完成时间没有值的时候，写值
        if (firstCompleteTimeFlag && Objects.isNull(businessTask.getFirstCompleteTime())) {
            updateWrapper.set(BusinessTask::getFirstCompleteTime, LocalDateTime.now());
        }
        update(updateWrapper);
        try {
            Map<String, Object> map = Maps.newLinkedHashMap();
            map.put("结果", finishResult == null ? "" : BusinessTaskFinishResult.getByCode(finishResult).getName());
            map.put("备注", remark);
            map.put("材料完整", MaterialIntegrity.getByCode(vo.getMaterialIntegrity()).getDesc());
            if (isNormal && !Objects.isNull(vo.getStatementBalance())) {
                map.put("对账单余额", vo.getStatementBalance().stripTrailingZeros().toPlainString());
            }
            String operContent = JSONObject.toJSONString(map);

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(vo.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(vo.getDeptId())
                            .setOperType("完成任务")
                            .setOperName(vo.getOperName())
                            .setOperContent(operContent)
                            .setOperRemark(null)
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    public static void main(String[] args) {
        System.out.println(true && false || true);
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> checkBatch(Long deptId, CheckBatchVO vo) {
        Gson gson = new Gson();

//        log.info("BusinessTaskServiceImplCheckBatch deptId={} vo={}", deptId, gson.toJson(vo));

        //操作者
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO userDept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();

        //入参
        List<Long> ids = vo.getIds();
        Integer checkResult = vo.getCheckResult();//审核结果：1-通过、0-驳回
        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();

        BusinessTaskCheckResult businessTaskCheckResult = BusinessTaskCheckResult.getByCode(checkResult);
        boolean isPass = Objects.equals(businessTaskCheckResult, BusinessTaskCheckResult.PASS);

        //备注和附件：如果审核结果是“驳回”，则备注必填
        if (Objects.equals(checkResult, BusinessTaskCheckResult.REJECT.getCode())) {
            if (StringUtils.isEmpty(remark)) {
                throw new ServiceException("备注必填");
            }
        }

        //结果
        List<BusinessTask> success = Lists.newArrayList();
        List<BusinessTask> fail = Lists.newArrayList();

        //背刺涉及到的全部数据
        List<BusinessTask> total = list(
                new LambdaQueryWrapper<BusinessTask>()
                        .in(BusinessTask::getId, ids)
        );
        Map<Long, Long> periodAccountingDeptMap = ObjectUtils.isEmpty(total) ? Maps.newHashMap() :
                customerServicePeriodMonthMapper.selectBatchIds(total.stream().map(BusinessTask::getBizId).collect(Collectors.toList()))
                        .stream().filter(row -> !Objects.isNull(row.getAccountingDeptId()))
                        .collect(Collectors.toMap(CustomerServicePeriodMonth::getId, CustomerServicePeriodMonth::getAccountingDeptId));

        List<Long> executeUserIds = total.stream().map(BusinessTask::getExecuteUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, SysUser> userMap = ObjectUtils.isEmpty(executeUserIds) ? Maps.newHashMap() :
                remoteUserService.getBatchUserByIds(executeUserIds).getDataThrowException();
        //区分是否可惭怍
        for (BusinessTask row : total) {
            Long accountingDeptId = periodAccountingDeptMap.get(row.getBizId());
            if (BusinessTaskStatus.canCheck(row.getStatus()) && (userDept.getIsAdmin() || Objects.isNull(accountingDeptId) || (!ObjectUtils.isEmpty(userDept.getDeptIds()) && userDept.getDeptIds().contains(accountingDeptId)))) {
                // 审核需要有账期会计的数据权限
                if (!Objects.isNull(row.getExecuteUserId())) {
                    SysUser executeUser = userMap.get(row.getExecuteUserId());
                    if (!Objects.isNull(executeUser) && Objects.equals("1", executeUser.getStatus()) && !isPass) {
                        fail.add(row);
                    } else {
                        success.add(row);
                    }
                } else {
                    success.add(row);
                }
            } else {
                fail.add(row);
            }
        }

        if (!ObjectUtils.isEmpty(success)) {
            //拿到用户
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                    Lists.newArrayList(userId)
            ).getDataThrowException();
            SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);

            //拿到账期
//            List<Long> periodIds = success.stream().map(BusinessTask::getBizId).distinct().collect(Collectors.toList());

//            //获取入账交付单
//            List<CustomerServiceInAccount> customerServiceInAccounts = iCustomerServiceInAccountService.list(
//                    new LambdaQueryWrapper<CustomerServiceInAccount>()
//                            .in(CustomerServiceInAccount::getCustomerServicePeriodMonthId, periodIds)
//                            .eq(CustomerServiceInAccount::getIsDel, false)
//            );
//            Map<Long, CustomerServiceInAccount> customerServiceInAccountsMap = customerServiceInAccounts.stream().collect(Collectors.toMap(CustomerServiceInAccount::getCustomerServicePeriodMonthId, r -> r, (k1, k2) -> k1));

            List<CustomerServiceCashierAccounting> needUpdateInAccounts = Lists.newArrayList();
            List<BizIdBankAccountDTO> dtoList = success.stream().filter(row -> !StringUtils.isEmpty(row.getBankAccountNumber()) && !Objects.isNull(row.getBizId()))
                    .map(row -> BizIdBankAccountDTO.builder().bizId(row.getBizId()).bankAccountNumber(row.getBankAccountNumber()).build()).collect(Collectors.toList());
            List<CustomerServiceCashierAccounting> cashierList = ObjectUtils.isEmpty(dtoList) ? Lists.newArrayList() :
                    customerServiceCashierAccountingService.selectBatchByCustomerServicePeriodMonthIdAndBankAccountNumber(dtoList);
            Map<String, CustomerServiceCashierAccounting> cashierMap = cashierList.stream().collect(Collectors.toMap(row -> row.getCustomerServicePeriodMonthId() + "_" + row.getBankAccountNumber(), Function.identity(), (v1, v2) -> v1));
            Map<Long, String> businessFinishRemarkMap = new HashMap<>();
            for (BusinessTask row : success) {
                Long id = row.getId();

                handleOperator(row, operatorUser, BusinessTaskOperateType.CHECK);

                if (isPass) {
                    row.setStatus(BusinessTaskStatus.FINISHED.getCode());
                    row.setCloseTime(LocalDateTime.now());
                } else {
                    row.setStatus(BusinessTaskStatus.NEED_FINISH.getCode());
                }
                if (checkResult == 0) {
                    row.setRejectTimes(row.getRejectTimes() + 1);
                }

                if (!StringUtils.isEmpty(remark)) {
                    row.setRemark(remark);
                    row.setCheckRemark(remark);
                }

                //先删除文件
                iBusinessTaskFileService.deleteByMainIdAndFileTypes(Lists.newArrayList(id), Lists.newArrayList(BusinessTaskFileType.CHECK));

                //再存文件
                iBusinessTaskFileService.saveFile(id, vo.getFiles(), BusinessTaskFileType.CHECK, String.valueOf(id));

                //@zc：这里有个逻辑要改下，银行流水录入时间为空的时候  目前审核：审核通过和驳回都会写这个时间。驳回的时候不要写
//                if (isPass) {
//                    //可操作的条件：状态是“待审核”。确定后再进行操作。操作后按以下逻辑对入账交付单进行操作：
//                    //当任务单的结果不为“银行部分缺”时，需要去操作入账交付单。
//                    //如果入账交付单的银行流水为空时，将审核时间所在日写为银行流水录入时间。
//                    //且根据入账交付单的逻辑来影响入账交付单的状态（只有银行流水录入时间时，入账状态不变，有银行流水录入时间和入账时间时，取最晚为结账时间，且入账状态改为已入账已结账）
//                    CustomerServiceInAccount customerServiceInAccount = handleForInAccountDeliverV2(row, LocalDate.now(), customerServiceInAccountsMap.get(row.getBizId()));
//                    if (customerServiceInAccount != null) {
//                        needUpdateInAccounts.add(customerServiceInAccount);
//                    }
//                }
                if (isPass && !StringUtils.isEmpty(row.getBankAccountNumber())) {
                    CustomerServiceCashierAccounting customerServiceCashierAccounting = cashierMap.get(row.getBizId() + "_" + row.getBankAccountNumber());
                    if (!Objects.isNull(customerServiceCashierAccounting)) {
                        Integer deliverResult = AccountingCashierDeliverResult.convertFromFinishResult(row.getFinishResult());
                        if (!Objects.isNull(deliverResult)) {
                            needUpdateInAccounts.add(new CustomerServiceCashierAccounting()
                                    .setCustomerServicePeriodMonthId(row.getBizId()).setId(customerServiceCashierAccounting.getId()).setDeliverResult(deliverResult)
                                    .setInTime(customerServiceCashierAccounting.getInTime())
                                    .setStatementBalance(row.getStatementBalance()));
                            businessFinishRemarkMap.put(customerServiceCashierAccounting.getId(), row.getFinishRemark());
                        }
                    }
                }
            }

            //批量更新
            updateBatchById(success);

            // 驳回的时候，如果操作时间和首次完成时间是同一个月，则清除任务首次完成时间和末次完成时间
            if (!isPass) {
                String nowPeriod = DateUtils.getNowPeriod().toString();
                List<Long> needClearIdList = success.stream().filter(row -> !Objects.isNull(row.getFirstCompleteTime()) && Objects.equals(row.getFirstCompleteTime().format(DateTimeFormatter.ofPattern("yyyyMM")), nowPeriod))
                        .map(BusinessTask::getId).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(needClearIdList)) {
                    update(new LambdaUpdateWrapper<BusinessTask>().in(BusinessTask::getId, needClearIdList)
                            .set(BusinessTask::getFirstCompleteTime, null)
                            .set(BusinessTask::getFinishTime, null));
                }
            }

            //获取操作人员信息
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

            LocalDateTime operTime = LocalDateTime.now();
            if (!ObjectUtils.isEmpty(needUpdateInAccounts)) {
                customerServiceCashierAccountingService.updateBankAccountingCashierStatusBatch(needUpdateInAccounts, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode(),
                        operateUserInfoDTO.getOperName(), remark, operTime, "银行流水任务审核通过修改", operateUserInfoDTO.getDeptId(), operateUserInfoDTO.getUserId(), vo.getMaterialIntegrity());
            }

            if (!ObjectUtils.isEmpty(needUpdateInAccounts)) {
                needUpdateInAccounts.forEach(row -> {
                    try {
                        Map<String, Object> map = Maps.newLinkedHashMap();
                        if (!Objects.isNull(row.getDeliverResult())) {
                            map.put("交付结果", AccountingCashierDeliverResult.getNameByCode(row.getDeliverResult(), AccountingCashierType.FLOW.getCode()));
                        }
                        if (!StringUtils.isEmpty(remark)) {
                            map.put("交付备注", remark);
                        }
                        String businessTaskFinishRemark = businessFinishRemarkMap.get(row.getId());
                        if (!StringUtils.isEmpty(businessTaskFinishRemark)) {
                            map.put("任务完成备注", businessTaskFinishRemark);
                        }
                        if (!Objects.isNull(row.getStatementBalance())) {
                            map.put("对账单金额", row.getStatementBalance().stripTrailingZeros().toPlainString());
                        }
                        String operContent = JSONObject.toJSONString(map);

                        asyncLogService.saveBusinessLog(
                                new BusinessLogDTO()
                                        .setBusinessId(row.getId())
                                        .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                        .setDeptId(deptId)
                                        .setOperType("银行流水任务审核通过修改")
                                        .setOperName(operateUserInfoDTO.getOperName())
                                        .setCreateTime(operTime)
                                        .setOperContent(operContent)
                                        .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                        .setOperUserId(userId));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }

            //插入操作日志
            success.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("审核结果", checkResult == null ? "" : businessTaskCheckResult.getName());
                    map.put("备注", remark);
                    map.put("材料完整度", MaterialIntegrity.getByCode(vo.getMaterialIntegrity()).getDesc());
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("审核")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark(null)
                                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        return result;
    }

    @Override
    @Transactional
    public void checkSingle(Long deptId, CheckSingleVO vo) {
        BusinessTask businessTask = getById(vo.getId());
        if (Objects.isNull(businessTask) || businessTask.getIsDel()) {
            throw new ServiceException("任务不存在");
        }
        //操作者
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO userDept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();

        //入参
        Long id = vo.getId();
        Integer checkResult = vo.getCheckResult();//审核结果：1-通过、0-驳回
        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();

        BusinessTaskCheckResult businessTaskCheckResult = BusinessTaskCheckResult.getByCode(checkResult);
        boolean isPass = Objects.equals(businessTaskCheckResult, BusinessTaskCheckResult.PASS);

        //备注和附件：如果审核结果是“驳回”，则备注必填
        if (Objects.equals(checkResult, BusinessTaskCheckResult.REJECT.getCode())) {
            if (StringUtils.isEmpty(remark)) {
                throw new ServiceException("备注必填");
            }
        }

        if (!BusinessTaskStatus.canCheck(businessTask.getStatus())) {
            throw new ServiceException("任务状态不符合");
        }

        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(businessTask.getBizId());
        if (!(userDept.getIsAdmin() || (!ObjectUtils.isEmpty(userDept.getDeptIds()) && !Objects.isNull(customerServicePeriodMonth.getAccountingDeptId()) && userDept.getDeptIds().contains(customerServicePeriodMonth.getAccountingDeptId())))) {
            throw new ServiceException("您无操作权限");
        }
        if (!Objects.isNull(businessTask.getExecuteUserId())) {
            SysUser executeUser = remoteUserService.getByUserId(businessTask.getExecuteUserId(), SecurityConstants.INNER).getDataThrowException();
            if (!Objects.isNull(executeUser) && Objects.equals("1", executeUser.getStatus()) && !isPass) {
                throw new ServiceException("任务执行人已离职，只允许审核通过");
            }
        }
        //拿到用户
        Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                Lists.newArrayList(userId)
        ).getDataThrowException();
        SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);

        //获取操作人员信息
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        handleOperator(businessTask, operatorUser, BusinessTaskOperateType.CHECK);

        if (isPass) {
            businessTask.setStatus(BusinessTaskStatus.FINISHED.getCode());
            businessTask.setCloseTime(LocalDateTime.now());
        } else {
            businessTask.setStatus(BusinessTaskStatus.NEED_FINISH.getCode());
        }
        if (checkResult == 0) {
            businessTask.setRejectTimes(businessTask.getRejectTimes() + 1);
        }

        if (!StringUtils.isEmpty(remark)) {
            businessTask.setRemark(remark);
            businessTask.setCheckRemark(remark);
        }
        businessTask.setMaterialIntegrity(vo.getMaterialIntegrity());
        updateById(businessTask);
        // 驳回的时候，如果操作时间和首次完成时间是同一个月，则清除任务首次完成时间和末次完成时间
        if (!isPass && !Objects.isNull(businessTask.getFirstCompleteTime()) && Objects.equals(businessTask.getFirstCompleteTime().format(DateTimeFormatter.ofPattern("yyyyMM")), LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")))) {
            update(new LambdaUpdateWrapper<BusinessTask>().eq(BusinessTask::getId, vo.getId())
                    .set(BusinessTask::getFirstCompleteTime, null)
                    .set(BusinessTask::getFinishTime, null));
        }
        //先删除文件
        iBusinessTaskFileService.deleteByMainIdAndFileTypes(Lists.newArrayList(id), Lists.newArrayList(BusinessTaskFileType.CHECK));

        //再存文件
        iBusinessTaskFileService.saveFile(id, vo.getFiles(), BusinessTaskFileType.CHECK, String.valueOf(id));

        List<CustomerServiceCashierAccounting> needUpdateInAccounts = Lists.newArrayList();
//        if (isPass) {
//            CustomerServiceInAccount needUpdateInAccount = iCustomerServiceInAccountService.getOne(
//                    new LambdaQueryWrapper<CustomerServiceInAccount>()
//                            .eq(CustomerServiceInAccount::getCustomerServicePeriodMonthId, businessTask.getBizId())
//                            .eq(CustomerServiceInAccount::getIsDel, false)
//                            .last("limit 1")
//            );
//            //可操作的条件：状态是“待审核”。确定后再进行操作。操作后按以下逻辑对入账交付单进行操作：
//            //当任务单的结果不为“银行部分缺”时，需要去操作入账交付单。
//            //如果入账交付单的银行流水为空时，将审核时间所在日写为银行流水录入时间。
//            //且根据入账交付单的逻辑来影响入账交付单的状态（只有银行流水录入时间时，入账状态不变，有银行流水录入时间和入账时间时，取最晚为结账时间，且入账状态改为已入账已结账）
//            CustomerServiceInAccount customerServiceInAccount = handleForInAccountDeliverV2(businessTask, LocalDate.now(), needUpdateInAccount);
//            if (customerServiceInAccount != null) {
//                needUpdateInAccounts.add(customerServiceInAccount);
//            }
        if (!StringUtils.isEmpty(businessTask.getBankAccountNumber())) {
            List<CustomerServiceCashierAccounting> customerServiceCashierAccountings = customerServiceCashierAccountingService.selectBatchByCustomerServicePeriodMonthIdAndBankAccountNumber(Collections.singletonList(BizIdBankAccountDTO.builder().bizId(businessTask.getBizId()).bankAccountNumber(businessTask.getBankAccountNumber()).build()));
            CustomerServiceCashierAccounting customerServiceCashierAccounting = ObjectUtils.isEmpty(customerServiceCashierAccountings) ? null : customerServiceCashierAccountings.get(0);
            if (isPass && !Objects.isNull(customerServiceCashierAccounting)) {
                Integer deliverResult = AccountingCashierDeliverResult.convertFromFinishResult(businessTask.getFinishResult());
                if (!Objects.isNull(deliverResult)) {
                    needUpdateInAccounts.add(new CustomerServiceCashierAccounting()
                            .setCustomerServicePeriodMonthId(businessTask.getBizId()).setId(customerServiceCashierAccounting.getId()).setDeliverResult(deliverResult)
                            .setInTime(customerServiceCashierAccounting.getInTime())
                            .setStatementBalance(businessTask.getStatementBalance()));
                }
            }
        }
        LocalDateTime operTime = LocalDateTime.now();
        if (!ObjectUtils.isEmpty(needUpdateInAccounts)) {
            customerServiceCashierAccountingService.updateBankAccountingCashierStatusBatch(needUpdateInAccounts, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode(),
                    operateUserInfoDTO.getOperName(), remark, operTime, "银行流水任务审核通过修改", operateUserInfoDTO.getDeptId(), operateUserInfoDTO.getUserId(), vo.getMaterialIntegrity());
        }

        if (!ObjectUtils.isEmpty(needUpdateInAccounts)) {
            needUpdateInAccounts.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    if (!Objects.isNull(row.getDeliverResult())) {
                        map.put("交付结果", AccountingCashierDeliverResult.getNameByCode(row.getDeliverResult(), AccountingCashierType.FLOW.getCode()));
                    }
                    if (!StringUtils.isEmpty(remark)) {
                        map.put("交付备注", remark);
                    }
                    if (!StringUtils.isEmpty(businessTask.getFinishRemark())) {
                        map.put("任务完成备注", businessTask.getFinishRemark());
                    }
                    if (!Objects.isNull(row.getStatementBalance())) {
                        map.put("对账单金额", row.getStatementBalance().stripTrailingZeros().toPlainString());
                    }
                    map.put("材料完整度", MaterialIntegrity.getByCode(vo.getMaterialIntegrity()).getDesc());
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType("银行流水任务审核通过修改")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setCreateTime(operTime)
                                    .setOperContent(operContent)
                                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        try {
            Map<String, Object> map = Maps.newLinkedHashMap();
            map.put("审核结果", businessTaskCheckResult.getName());
            map.put("备注", remark);
            map.put("材料完整度", MaterialIntegrity.getByCode(vo.getMaterialIntegrity()).getDesc());
            String operContent = JSONObject.toJSONString(map);

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(id)
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("审核")
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setOperContent(operContent)
                            .setOperRemark(null)
                            .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> handleExceptionBatch(Long deptId, HandleExceptionBatchVO vo) {
        Gson gson = new Gson();

//        log.info("BusinessTaskServiceImplDistributeBatch deptId={} vo={}", deptId, gson.toJson(vo));

        //操作者
        Long userId = SecurityUtils.getUserId();

        //入参
        List<Long> ids = vo.getIds();
        Integer handleResult = vo.getHandleResult();
        String remark = vo.getRemark();
        List<CommonFileVO> files = vo.getFiles();

        BusinessTaskHandleExceptionResult businessTaskHandleExceptionResult = BusinessTaskHandleExceptionResult.getByCode(handleResult);
        boolean isPass = Objects.equals(businessTaskHandleExceptionResult, BusinessTaskHandleExceptionResult.PASS);

        //结果
        List<BusinessTask> success = Lists.newArrayList();
        List<BusinessTask> fail = Lists.newArrayList();

        //背刺涉及到的全部数据
        List<BusinessTask> total = list(
                new LambdaQueryWrapper<BusinessTask>()
                        .in(BusinessTask::getId, ids)
        );

        //区分是否可惭怍
        for (BusinessTask row : total) {
            if (BusinessTaskStatus.canHandleException(row.getStatus())) {
                success.add(row);
            } else {
                fail.add(row);
            }
        }

        if (!ObjectUtils.isEmpty(success)) {
            //拿到用户
            Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                    Lists.newArrayList(userId)
            ).getDataThrowException();
            SysUser operatorUser = userId == null ? null : sysUserMap.get(userId);

            for (BusinessTask row : success) {
                Long id = row.getId();

                row.setFinishTime(LocalDateTime.now());
                row.setFinishUserId(userId);

                if (!StringUtils.isEmpty(remark)) {
                    row.setRemark(remark);
                    row.setDealExceptionRemark(remark);
                }

                handleOperator(row, operatorUser, BusinessTaskOperateType.HANDLE_EXCEPTION);

                //当结果是解除异常时，后置状态为：待完成
                //当结果为关闭任务时，后置状态为：已关闭
                if (isPass) {
                    row.setStatus(BusinessTaskStatus.NEED_FINISH.getCode());
                } else {
                    row.setStatus(BusinessTaskStatus.CLOSED.getCode());
                    row.setCloseTime(LocalDateTime.now());
                }

                //先删除文件
                iBusinessTaskFileService.deleteByMainIdAndFileTypes(Lists.newArrayList(id), Lists.newArrayList(BusinessTaskFileType.HANDLE_EXCEPTION));

                //再存文件
                iBusinessTaskFileService.saveFile(id, vo.getFiles(), BusinessTaskFileType.HANDLE_EXCEPTION, String.valueOf(id));
            }

            //批量更新
            updateBatchById(success);

            List<BizIdBankAccountDTO> dtoList = Objects.equals(handleResult, BusinessTaskHandleExceptionResult.NEED_PAYMENT.getCode()) ?
                    success.stream().filter(row -> !StringUtils.isEmpty(row.getBankAccountNumber()))
                            .map(row -> BizIdBankAccountDTO.builder().bizId(row.getBizId()).bankAccountNumber(row.getBankAccountNumber()).build())
                            .collect(Collectors.toList()) : Lists.newArrayList();
            List<CustomerServiceCashierAccounting> customerServiceCashierAccountings = customerServiceCashierAccountingService.selectBatchByCustomerServicePeriodMonthIdAndBankAccountNumber(dtoList);

            //获取操作人员信息
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

            LocalDateTime operTime = LocalDateTime.now();
            if (!ObjectUtils.isEmpty(customerServiceCashierAccountings)) {
                List<Long> needPaymentAccountingCashierIds = customerServiceCashierAccountings.stream().map(CustomerServiceCashierAccounting::getId)
                        .collect(Collectors.toList());
                customerServiceCashierAccountingService.updateBankAccountingCashierStatus(needPaymentAccountingCashierIds, AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode(),
                        operateUserInfoDTO.getOperName(), vo.getRemark(), operTime, "任务异常", operateUserInfoDTO.getDeptId(), operateUserInfoDTO.getUserId());
                needPaymentAccountingCashierIds.forEach(row -> {
                    try {
                        Map<String, Object> map = Maps.newLinkedHashMap();
                        map.put("备注", remark);
                        String operContent = JSONObject.toJSONString(map);

                        asyncLogService.saveBusinessLog(
                                new BusinessLogDTO()
                                        .setBusinessId(row)
                                        .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                        .setDeptId(deptId)
                                        .setOperType("任务异常")
                                        .setOperName(operateUserInfoDTO.getOperName())
                                        .setOperContent(operContent)
                                        .setOperRemark(null)
                                        .setCreateTime(operTime)
                                        .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                        .setOperUserId(userId));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }

            //插入操作日志
            success.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("处理结果", handleResult == null ? "" : businessTaskHandleExceptionResult.getName());
                    map.put("备注", remark);
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType("处理异常")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark(null)
                                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONObject.toJSONString(files))
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }

        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        result.setFail(fail);
        result.setSuccess(success);
        result.setTotal(total);

        return result;
    }

    @Transactional
    @Override
    public TCommonOperateDTO<BusinessTask> createTask(Long deptId, CreateTaskVO vo) {
        Gson gson = new Gson();
//        log.info("BusinessTaskServiceImplCreateTask deptId={} vo={}", deptId, gson.toJson(vo));

        if (!BusinessTaskType.isPeriod(vo.getType())) {
            throw new ServiceException("不支持的类型");
        }

        List<SettlementOrderDataTemp> settlementOrderDataTemps = iSettlementOrderDataTempService.list(
                new LambdaQueryWrapper<SettlementOrderDataTemp>()
                        .eq(SettlementOrderDataTemp::getSettlementOrderBatchNo, vo.getBatchNo())
        );

        if (ObjectUtils.isEmpty(settlementOrderDataTemps)) {
            throw new ServiceException("条件结果为空，无法生成任务单");
        }

        List<Long> periodIds = settlementOrderDataTemps.stream().map(SettlementOrderDataTemp::getBusinessId).distinct().collect(Collectors.toList());
//        log.info("BusinessTaskServiceImplCreateTask deptId={} periodIds={}", deptId, gson.toJson(periodIds));

        //去除还在进行中的账期任务
        List<Long> existsTaskPeriodIds = businessTaskMapper.selectList(
                new LambdaQueryWrapper<BusinessTask>()
                        .eq(BusinessTask::getIsDel, false)
                        .eq(BusinessTask::getType, vo.getType())
                        .eq(BusinessTask::getItemType, vo.getItemType())
                        .notIn(BusinessTask::getStatus, BusinessTaskStatus.RE)
                        .select(BusinessTask::getBizId)).stream().map(BusinessTask::getBizId).distinct().collect(Collectors.toList()
        );
//        log.info("BusinessTaskServiceImplCreateTask deptId={} existsTaskPeriodIds={}", deptId, gson.toJson(existsTaskPeriodIds));
        periodIds = periodIds.stream().filter(r -> !existsTaskPeriodIds.contains(r)).distinct().collect(Collectors.toList());
//        log.info("BusinessTaskServiceImplCreateTask deptId={} periodIds2={}", deptId, gson.toJson(periodIds));

        AddBatchVO addBatchVO = AddBatchVO.builder()
                .adminUserId(vo.getAdminUserId())
                .deadline(vo.getDeadline())
                .items(periodIds.stream().map(r -> AddBatchItemVO.builder().bizId(r).build()).collect(Collectors.toList()))
                .itemType(vo.getItemType())
                .type(vo.getType())
                .medium(vo.getMedium())
                .build();

        return addBatch(deptId, addBatchVO);
    }

    @Override
    public List<BusinessTask> getByPeriodIdsAndTypeAndItemType(RemoteBusinessTaskSearchVO vo) {
        if (ObjectUtils.isEmpty(vo.getCustomerServicePeriodMonthIds())) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<BusinessTask>()
                .in(BusinessTask::getBizId, vo.getCustomerServicePeriodMonthIds())
                .eq(BusinessTask::getItemType, vo.getItemType())
                .eq(BusinessTask::getType, vo.getType())
                .eq(BusinessTask::getIsDel, false));
    }

    @Override
    @Transactional
    public void comment(Long deptId, BusinessTaskCommentVO vo) {
        BusinessTask businessTask = getById(vo.getId());
        if (Objects.isNull(businessTask) || businessTask.getIsDel()) {
            throw new ServiceException("任务不存在");
        }
        //获取操作人员信息
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        LocalDateTime now = LocalDateTime.now();
        updateById(new BusinessTask().setId(vo.getId())
                .setLastOperateTime(now)
                .setLastOperateType(BusinessTaskOperateType.COMMENT.getCode())
                .setLastOperateUserId(userId)
                .setLastOperateUserName(operateUserInfoDTO.getOperName()));
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(vo.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("评论")
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setOperRemark(vo.getContent())
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public List<BusinessTask> selectWaitFinishTask(Integer itemType, Long userId) {
        List<BusinessTask> businessTasks = list(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getItemType, itemType)
                .eq(BusinessTask::getExecuteUserId, userId)
                .in(BusinessTask::getStatus, BusinessTaskStatus.CAN_ASSIGN)
                .eq(BusinessTask::getIsDel, false));
        if (ObjectUtils.isEmpty(businessTasks)) {
            return Collections.emptyList();
        }
        List<Long> customerServicePeriodMonthIds = businessTasks.stream().map(BusinessTask::getBizId).distinct().collect(Collectors.toList());
        Map<Long, CustomerServicePeriodMonth> periodMonthMap = ObjectUtils.isEmpty(customerServicePeriodMonthIds) ? Maps.newHashMap() :
                customerServicePeriodMonthMapper.selectBatchIds(customerServicePeriodMonthIds).stream().collect(Collectors.toMap(CustomerServicePeriodMonth::getId, r -> r));
        businessTasks.forEach(row -> {
            CustomerServicePeriodMonth customerServicePeriodMonth = periodMonthMap.get(row.getBizId());
            if (!Objects.isNull(customerServicePeriodMonth)) {
                row.setCustomerName(customerServicePeriodMonth.getCustomerName());
                row.setCreditCode(customerServicePeriodMonth.getCreditCode());
                row.setPeriod(customerServicePeriodMonth.getPeriod());
            }
        });
        return businessTasks;
    }

    @Override
    public List<BankPaymentTaskDTO> getBankPaymentTaskListByPeriodIdAndBankAccountNumber(Long customerServicePeriodMonthId, String bankAccountNumber) {
        if (Objects.isNull(customerServicePeriodMonthId) || StringUtils.isEmpty(bankAccountNumber)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getBizId, customerServicePeriodMonthId)
                .eq(BusinessTask::getBankAccountNumber, bankAccountNumber)
                .eq(BusinessTask::getIsDel, false)
                .eq(BusinessTask::getItemType, 1)
                .eq(BusinessTask::getType, 1)
                .orderByDesc(BusinessTask::getId)).stream().map(row ->
                BankPaymentTaskDTO.builder()
                        .taskId(row.getId())
                        .taskName(row.getTitle())
                        .taskStatus(row.getStatus())
                        .taskStatusStr(BusinessTaskStatus.getByCode(row.getStatus()).getName())
                        .period(row.getPeriod())
                        .build()).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void createBankTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CustomerServiceCashierAccountingFile> files, Long deptId, Long userId, String operName, LocalDateTime operTime, String ddl, Long adminUserId) {
        if (count(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .eq(BusinessTask::getType, BusinessTaskType.PERIOD.getCode())
                .eq(BusinessTask::getItemType, BusinessTaskItemType.BANK_PAYMENT.getCode())
                .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())
                .notIn(BusinessTask::getStatus, BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.CLOSED.getCode())) > 0) {
            throw new ServiceException("存在未完成的银行流水任务");
        }
        //操作者
        //入参
        Integer itemType = BusinessTaskItemType.BANK_PAYMENT.getCode();
        Integer type = BusinessTaskType.PERIOD.getCode();

        //拿到用户
        Map<Long, SysUser> sysUserMap = remoteUserService.getBatchUserByIds(
                Lists.newArrayList(userId, adminUserId)
        ).getDataThrowException();
        SysUser adminUser = adminUserId == null ? null : sysUserMap.get(adminUserId);

        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
        if (Objects.isNull(customerServicePeriodMonth)) {
            throw new ServiceException("账期不存在");
        }
        CCustomerService cCustomerService = customerServiceMapper.selectById(customerServicePeriodMonth.getCustomerServiceId());
        if (Objects.isNull(cCustomerService) || cCustomerService.getIsDel()) {
            throw new ServiceException("服务不存在");
        }
        LocalDate deadline = null;
        if (!StringUtils.isEmpty(ddl)) {
            deadline = DateUtils.strToLocalDate(ddl, DateUtils.YYYY_MM_DD);
        } else {
            List<CBusinessTagRelation> customerTags = businessTagRelationService.selectByBusinessIdAndBusinessType(cCustomerService.getId(), TagBusinessType.CUSTOMER_SERVICE.getCode());
            List<CBusinessTagRelation> periodTag = businessTagRelationService.selectByBusinessIdAndBusinessType(customerServicePeriodMonth.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD.getCode());
            List<Long> czTagIds = Lists.newArrayList(specialTagProperties.getCy15cz(), specialTagProperties.getCymcz());
            if (customerTags.stream().anyMatch(row -> czTagIds.contains(row.getTagId())) || periodTag.stream().anyMatch(row -> czTagIds.contains(row.getTagId()))) {
                deadline = LocalDate.now().plusDays(3);
            }
        }
        if (Objects.isNull(deadline) && !Objects.isNull(customerServiceCashierAccounting.getDdl())) {
            deadline = customerServiceCashierAccounting.getDdl();
            if (!deadline.isAfter(LocalDate.now())) {
                deadline = LocalDate.now().plusDays(1);
            }
        }
        int mediumPaper = 0;
        int mediumElectric = 0;
        int mediumBank = 0;
        if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.PAPER.getCode())) {
            mediumPaper = 1;
        } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.ELECTRONIC.getCode())) {
            mediumElectric = 1;
        } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.BANK_CARD.getCode())) {
            mediumBank = 1;
        }

        BusinessTask newEntry = new BusinessTask();
        newEntry.setBankAccountingCashierId(customerServiceCashierAccounting.getId());
        newEntry.setBizId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
        newEntry.setDeadline(deadline);
        newEntry.setAdminUserId(adminUserId);
        newEntry.setAdminUserName(adminUser == null ? null : adminUser.getNickName());
        Long adminDeptId = null;
        if (!Objects.isNull(adminUserId)) {
            List<SysEmployee> employeeList = remoteEmployeeService.getBatchByUserIds(Collections.singletonList(adminUserId)).getDataThrowException();
            if (!ObjectUtils.isEmpty(employeeList)) {
                adminDeptId = employeeList.get(0).getDeptId();
            }
        }
        newEntry.setAdminDeptId(adminDeptId);
        newEntry.setAssignTime(null);
        newEntry.setCreditCode(cCustomerService.getCreditCode());
        newEntry.setCustomerCompanyName(cCustomerService.getCustomerCompanyName());
        newEntry.setCustomerName(cCustomerService.getCustomerName());
        newEntry.setCustomerServiceId(cCustomerService.getId());
        newEntry.setEndTime(null);
        newEntry.setExecuteTime(null);
        newEntry.setExecuteUserId(null);
        newEntry.setExecuteUserName(null);
        newEntry.setFinishResult(null);
        newEntry.setFinishTime(null);
        newEntry.setFinishUserId(null);
        newEntry.setIsAssign(null);
        newEntry.setIsDel(false);
        newEntry.setItemType(itemType);
        newEntry.setLastOperateTime(operTime);
        newEntry.setLastOperateType(BusinessTaskOperateType.NEW.getCode());
        newEntry.setLastOperateUserId(userId);
        newEntry.setLastOperateUserName(operName);
        newEntry.setPeriod(customerServicePeriodMonth.getPeriod());
        newEntry.setPeriodAccountingDeptId(customerServicePeriodMonth.getAccountingDeptId());
        newEntry.setPeriodAdvisorDeptId(customerServicePeriodMonth.getAdvisorDeptId());
        newEntry.setRemark(customerServiceCashierAccounting.getDeliverRequire());
        newEntry.setCreateRemark(customerServiceCashierAccounting.getDeliverRequire());
        newEntry.setStatus(BusinessTaskStatus.NEED_FINISH.getCode());
        newEntry.setTaxType(cCustomerService.getTaxType());
        newEntry.setTitle(String.format("流水任务单【%s-%s-%s】", customerServiceCashierAccounting.getBankName(), customerServiceCashierAccounting.getBankAccountNumber(), customerServiceCashierAccounting.getPeriod()));
        newEntry.setType(type);
        newEntry.setMediumPaper(mediumPaper);
        newEntry.setMediumElectric(mediumElectric);
        newEntry.setMediumBank(mediumBank);
        newEntry.setBankName(customerServiceCashierAccounting.getBankName());
        newEntry.setBankAccountNumber(customerServiceCashierAccounting.getBankAccountNumber());
        newEntry.setHasBankPayment(customerServiceCashierAccounting.getHasBankPayment());
        newEntry.setCreateBy(operName);
        save(newEntry);

        if (!ObjectUtils.isEmpty(files)) {
            iBusinessTaskFileService.saveFile(newEntry.getId(), files.stream().map(f -> CommonFileVO.builder().fileName(f.getFileName()).fileUrl(f.getFileUrl())
                    .fileSize(f.getFileSize()).build()).collect(Collectors.toList()), BusinessTaskFileType.BANK_MATERIAL, newEntry.getId().toString());
        }

        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(newEntry.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("创建")
                            .setOperName(operName)
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (Objects.isNull(adminUserId)) {
            log.info("开始任务自动分单，任务id:{}", newEntry.getId());
            autoDispatchBusinessTask(newEntry.getId(), cCustomerService);
        }
    }

    @Override
    @Transactional
    public Long createBankTaskV2(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CommonFileVO> files, Long deptId, Long userId, String operName, LocalDateTime operTime) {
        BusinessTask businessTask = getOne(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .eq(BusinessTask::getType, BusinessTaskType.PERIOD.getCode())
                .eq(BusinessTask::getItemType, BusinessTaskItemType.BANK_PAYMENT.getCode())
                .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())
                .notIn(BusinessTask::getStatus, BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.CLOSED.getCode()), false);
        if (!Objects.isNull(businessTask)) {
            return businessTask.getId();
        }
        //操作者
        //入参
        Integer itemType = BusinessTaskItemType.BANK_PAYMENT.getCode();
        Integer type = BusinessTaskType.PERIOD.getCode();

        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
        if (Objects.isNull(customerServicePeriodMonth)) {
            return null;
        }
        CCustomerService cCustomerService = customerServiceMapper.selectById(customerServicePeriodMonth.getCustomerServiceId());
        if (Objects.isNull(cCustomerService) || cCustomerService.getIsDel()) {
            return null;
        }
        int mediumPaper = 0;
        int mediumElectric = 0;
        int mediumBank = 0;
        if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.PAPER.getCode())) {
            mediumPaper = 1;
        } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.ELECTRONIC.getCode())) {
            mediumElectric = 1;
        } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.BANK_CARD.getCode())) {
            mediumBank = 1;
        }

        BusinessTask newEntry = new BusinessTask();
        newEntry.setBankAccountingCashierId(customerServiceCashierAccounting.getId());
        newEntry.setBizId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
        newEntry.setCreditCode(cCustomerService.getCreditCode());
        newEntry.setCustomerCompanyName(cCustomerService.getCustomerCompanyName());
        newEntry.setCustomerName(cCustomerService.getCustomerName());
        newEntry.setCustomerServiceId(cCustomerService.getId());
        newEntry.setEndTime(null);
        newEntry.setExecuteTime(null);
        newEntry.setFinishResult(null);
        newEntry.setFinishTime(null);
        newEntry.setFinishUserId(null);
        newEntry.setIsDel(false);
        newEntry.setItemType(itemType);
        newEntry.setLastOperateTime(operTime);
        newEntry.setLastOperateType(BusinessTaskOperateType.NEW.getCode());
        newEntry.setLastOperateUserId(userId);
        newEntry.setLastOperateUserName(operName);
        newEntry.setPeriod(customerServicePeriodMonth.getPeriod());
        newEntry.setPeriodAccountingDeptId(customerServicePeriodMonth.getAccountingDeptId());
        newEntry.setPeriodAdvisorDeptId(customerServicePeriodMonth.getAdvisorDeptId());
        newEntry.setRemark(customerServiceCashierAccounting.getDeliverRequire());
        newEntry.setCreateRemark(customerServiceCashierAccounting.getDeliverRequire());
        newEntry.setStatus(BusinessTaskStatus.NEED_FINISH.getCode());
        newEntry.setTaxType(cCustomerService.getTaxType());
        newEntry.setTitle(String.format("流水任务单【%s-%s-%s】", customerServiceCashierAccounting.getBankName(), customerServiceCashierAccounting.getBankAccountNumber(), customerServiceCashierAccounting.getPeriod()));
        newEntry.setType(type);
        newEntry.setMediumPaper(mediumPaper);
        newEntry.setMediumElectric(mediumElectric);
        newEntry.setMediumBank(mediumBank);
        newEntry.setBankName(customerServiceCashierAccounting.getBankName());
        newEntry.setBankAccountNumber(customerServiceCashierAccounting.getBankAccountNumber());
        newEntry.setHasBankPayment(customerServiceCashierAccounting.getHasBankPayment());
        newEntry.setCreateBy(operName);
        save(newEntry);

        if (!ObjectUtils.isEmpty(files)) {
            iBusinessTaskFileService.saveFile(newEntry.getId(), files, BusinessTaskFileType.BANK_MATERIAL, newEntry.getId().toString());
        }

        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(newEntry.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("创建")
                            .setOperName(operName)
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return newEntry.getId();
    }

    @Override
    @Transactional
    public void closeOldBusinessTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, Long deptId, Long userId, String operName, LocalDateTime operTime, String operRemark) {
        BusinessTask businessTask = getOne(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .eq(BusinessTask::getType, BusinessTaskType.PERIOD.getCode())
                .eq(BusinessTask::getItemType, BusinessTaskItemType.BANK_PAYMENT.getCode())
                .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())
                .notIn(BusinessTask::getStatus, BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.CLOSED.getCode()), false);
        if (!Objects.isNull(businessTask)) {
            updateById(new BusinessTask().setId(businessTask.getId())
                    .setFinishResult(BusinessTaskFinishResult._6.getCode())
                    .setFinishTime(operTime)
                    .setStatus(BusinessTaskStatus.CLOSED.getCode())
                    .setLastOperateUserName(operName)
                    .setLastOperateType(BusinessTaskOperateType.CLOSE.getCode())
                    .setLastOperateTime(operTime)
                    .setLastOperateUserId(userId));
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
            operContent.put("后置状态", BusinessTaskStatus.CLOSED.getName());
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(businessTask.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType(BusinessTaskOperateType.CLOSE.getName())
                                .setOperName(operName)
                                .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                                .setCreateTime(operTime)
                                .setOperRemark(operRemark)
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Transactional
    @Override
    public BusinessTask createBankTaskV3(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CommonFileVO> files, Long deptId, Long userId, String operName, LocalDateTime operTime, Integer finishResult, Integer status, R resp) {
        if (count(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .eq(BusinessTask::getType, BusinessTaskType.PERIOD.getCode())
                .eq(BusinessTask::getItemType, BusinessTaskItemType.BANK_PAYMENT.getCode())
                .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())
                .notIn(BusinessTask::getStatus, BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.CLOSED.getCode())) > 0) {
            return null;
        }
        //操作者
        //入参
        Integer itemType = BusinessTaskItemType.BANK_PAYMENT.getCode();
        Integer type = BusinessTaskType.PERIOD.getCode();

        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
        if (Objects.isNull(customerServicePeriodMonth)) {
            return null;
        }
        CCustomerService cCustomerService = customerServiceMapper.selectById(customerServicePeriodMonth.getCustomerServiceId());
        if (Objects.isNull(cCustomerService) || cCustomerService.getIsDel()) {
            return null;
        }

        int mediumPaper = 0;
        int mediumElectric = 0;
        int mediumBank = 0;
        if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.PAPER.getCode())) {
            mediumPaper = 1;
        } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.ELECTRONIC.getCode())) {
            mediumElectric = 1;
        } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.BANK_CARD.getCode())) {
            mediumBank = 1;
        }
        LocalDate deadline = null;
        List<CBusinessTagRelation> customerTags = businessTagRelationService.selectByBusinessIdAndBusinessType(cCustomerService.getId(), TagBusinessType.CUSTOMER_SERVICE.getCode());
        List<CBusinessTagRelation> periodTag = businessTagRelationService.selectByBusinessIdAndBusinessType(customerServicePeriodMonth.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD.getCode());
        List<Long> czTagIds = Lists.newArrayList(specialTagProperties.getCy15cz(), specialTagProperties.getCymcz());
        if (customerTags.stream().anyMatch(row -> czTagIds.contains(row.getTagId())) || periodTag.stream().anyMatch(row -> czTagIds.contains(row.getTagId()))) {
            deadline = LocalDate.now().plusDays(3);
        }
        if (Objects.isNull(deadline) && !Objects.isNull(customerServiceCashierAccounting.getDdl())) {
            deadline = customerServiceCashierAccounting.getDdl();
            if (!deadline.isAfter(LocalDate.now())) {
                deadline = LocalDate.now().plusDays(1);
            }
        }
        BusinessTask newEntry = new BusinessTask();
        newEntry.setDeadline(deadline);
        newEntry.setBankAccountingCashierId(customerServiceCashierAccounting.getId());
        newEntry.setBizId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
        newEntry.setCreditCode(cCustomerService.getCreditCode());
        newEntry.setCustomerCompanyName(cCustomerService.getCustomerCompanyName());
        newEntry.setCustomerName(cCustomerService.getCustomerName());
        newEntry.setCustomerServiceId(cCustomerService.getId());
        newEntry.setEndTime(null);
        newEntry.setExecuteTime(null);
        newEntry.setFinishResult(finishResult);
        newEntry.setFinishTime(Objects.equals(BusinessTaskFinishResult.OK.getCode(), finishResult) ? operTime : null);
        newEntry.setFinishUserId(Objects.equals(BusinessTaskFinishResult.OK.getCode(), finishResult) ? 1L : null);
        newEntry.setIsDel(false);
        newEntry.setItemType(itemType);
        newEntry.setLastOperateTime(operTime);
        newEntry.setLastOperateType(Objects.equals(BusinessTaskFinishResult.OK.getCode(), finishResult) ? BusinessTaskOperateType.FINISH.getCode() : BusinessTaskOperateType.NEW.getCode());
        newEntry.setLastOperateUserId(userId);
        newEntry.setLastOperateUserName(operName);
        newEntry.setPeriod(customerServicePeriodMonth.getPeriod());
        newEntry.setPeriodAccountingDeptId(customerServicePeriodMonth.getAccountingDeptId());
        newEntry.setPeriodAdvisorDeptId(customerServicePeriodMonth.getAdvisorDeptId());
        newEntry.setRemark(customerServiceCashierAccounting.getDeliverRequire());
        newEntry.setCreateRemark(customerServiceCashierAccounting.getDeliverRequire());
        newEntry.setStatus(status);
        newEntry.setTaxType(cCustomerService.getTaxType());
        newEntry.setTitle(String.format("流水任务单【%s-%s-%s】", customerServiceCashierAccounting.getBankName(), customerServiceCashierAccounting.getBankAccountNumber(), customerServiceCashierAccounting.getPeriod()));
        newEntry.setType(type);
        newEntry.setMediumPaper(mediumPaper);
        newEntry.setMediumElectric(mediumElectric);
        newEntry.setMediumBank(mediumBank);
        newEntry.setBankName(customerServiceCashierAccounting.getBankName());
        newEntry.setBankAccountNumber(customerServiceCashierAccounting.getBankAccountNumber());
        newEntry.setHasBankPayment(customerServiceCashierAccounting.getHasBankPayment());
        newEntry.setCreateBy(operName);
        if (Objects.equals(BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode(), status)) {
            newEntry.setWaitInAccountTime(operTime);
        }
        save(newEntry);

        if (!ObjectUtils.isEmpty(files)) {
            iBusinessTaskFileService.saveFile(newEntry.getId(), files, BusinessTaskFileType.BANK_MATERIAL, newEntry.getId().toString());
        }

        Map<String, Object> operContent = new LinkedHashMap<>();
        if (Objects.equals(BusinessTaskFinishResult.OK.getCode(), finishResult)) {
            operContent.put("结果", "正常");
            operContent.put("查询结果", "成功");
            Object data = resp.getData();
            if (data instanceof BanksEnterprisesExtractDTO) {
                BanksEnterprisesExtractDTO dto = (BanksEnterprisesExtractDTO) data;
                operContent.put("待下载回单文件数", dto.getCount());
                operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
            }
        }
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(newEntry.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType(Objects.equals(BusinessTaskFinishResult.OK.getCode(), finishResult) ? "完成" : "创建")
                            .setOperName(operName)
                            .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSON.toJSONString(operContent))
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return newEntry;
    }

    @Override
    @Transactional
    public BusinessTask createBankTaskV4(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CommonFileVO> files, Long deptId, Long userId, String operName, LocalDateTime operTime, Integer finishResult, Integer status) {
        List<BusinessTask> businessTasks = list(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .eq(BusinessTask::getType, BusinessTaskType.PERIOD.getCode())
                .eq(BusinessTask::getItemType, BusinessTaskItemType.BANK_PAYMENT.getCode())
                .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())
                .notIn(BusinessTask::getStatus, BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.CLOSED.getCode()));
        if (!ObjectUtils.isEmpty(businessTasks)) {
            return businessTasks.get(0);
        }
        //操作者
        //入参
        Integer itemType = BusinessTaskItemType.BANK_PAYMENT.getCode();
        Integer type = BusinessTaskType.PERIOD.getCode();

        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
        if (Objects.isNull(customerServicePeriodMonth)) {
            return null;
        }
        CCustomerService cCustomerService = customerServiceMapper.selectById(customerServicePeriodMonth.getCustomerServiceId());
        if (Objects.isNull(cCustomerService) || cCustomerService.getIsDel()) {
            return null;
        }

        int mediumPaper = 0;
        int mediumElectric = 0;
        int mediumBank = 0;
        if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.PAPER.getCode())) {
            mediumPaper = 1;
        } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.ELECTRONIC.getCode())) {
            mediumElectric = 1;
        } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.BANK_CARD.getCode())) {
            mediumBank = 1;
        }
        LocalDate deadline = null;
        List<CBusinessTagRelation> customerTags = businessTagRelationService.selectByBusinessIdAndBusinessType(cCustomerService.getId(), TagBusinessType.CUSTOMER_SERVICE.getCode());
        List<CBusinessTagRelation> periodTag = businessTagRelationService.selectByBusinessIdAndBusinessType(customerServicePeriodMonth.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD.getCode());
        List<Long> czTagIds = Lists.newArrayList(specialTagProperties.getCy15cz(), specialTagProperties.getCymcz());
        if (customerTags.stream().anyMatch(row -> czTagIds.contains(row.getTagId())) || periodTag.stream().anyMatch(row -> czTagIds.contains(row.getTagId()))) {
            deadline = LocalDate.now().plusDays(3);
        }
        if (Objects.isNull(deadline) && !Objects.isNull(customerServiceCashierAccounting.getDdl())) {
            deadline = customerServiceCashierAccounting.getDdl();
            if (!deadline.isAfter(LocalDate.now())) {
                deadline = LocalDate.now().plusDays(1);
            }
        }
        BusinessTask newEntry = new BusinessTask();
        newEntry.setDeadline(deadline);
        newEntry.setBankAccountingCashierId(customerServiceCashierAccounting.getId());
        newEntry.setBizId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
        newEntry.setCreditCode(cCustomerService.getCreditCode());
        newEntry.setCustomerCompanyName(cCustomerService.getCustomerCompanyName());
        newEntry.setCustomerName(cCustomerService.getCustomerName());
        newEntry.setCustomerServiceId(cCustomerService.getId());
        newEntry.setEndTime(null);
        newEntry.setExecuteTime(null);
        newEntry.setFinishResult(finishResult);
        newEntry.setFinishTime(Objects.equals(BusinessTaskFinishResult.OK.getCode(), finishResult) ? operTime : null);
        newEntry.setFinishUserId(Objects.equals(BusinessTaskFinishResult.OK.getCode(), finishResult) ? 1L : null);
        newEntry.setIsDel(false);
        newEntry.setItemType(itemType);
        newEntry.setLastOperateTime(operTime);
        newEntry.setLastOperateType(Objects.equals(BusinessTaskFinishResult.OK.getCode(), finishResult) ? BusinessTaskOperateType.FINISH.getCode() : BusinessTaskOperateType.NEW.getCode());
        newEntry.setLastOperateUserId(userId);
        newEntry.setLastOperateUserName(operName);
        newEntry.setPeriod(customerServicePeriodMonth.getPeriod());
        newEntry.setPeriodAccountingDeptId(customerServicePeriodMonth.getAccountingDeptId());
        newEntry.setPeriodAdvisorDeptId(customerServicePeriodMonth.getAdvisorDeptId());
        newEntry.setRemark(customerServiceCashierAccounting.getDeliverRequire());
        newEntry.setCreateRemark(customerServiceCashierAccounting.getDeliverRequire());
        newEntry.setStatus(status);
        newEntry.setTaxType(cCustomerService.getTaxType());
        newEntry.setTitle(String.format("流水任务单【%s-%s-%s】", customerServiceCashierAccounting.getBankName(), customerServiceCashierAccounting.getBankAccountNumber(), customerServiceCashierAccounting.getPeriod()));
        newEntry.setType(type);
        newEntry.setMediumPaper(mediumPaper);
        newEntry.setMediumElectric(mediumElectric);
        newEntry.setMediumBank(mediumBank);
        newEntry.setBankName(customerServiceCashierAccounting.getBankName());
        newEntry.setBankAccountNumber(customerServiceCashierAccounting.getBankAccountNumber());
        newEntry.setHasBankPayment(customerServiceCashierAccounting.getHasBankPayment());
        newEntry.setCreateBy(operName);
        if (Objects.equals(BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode(), status)) {
            newEntry.setWaitInAccountTime(operTime);
        }
        save(newEntry);

        if (!ObjectUtils.isEmpty(files)) {
            iBusinessTaskFileService.saveFile(newEntry.getId(), files, BusinessTaskFileType.BANK_MATERIAL, newEntry.getId().toString());
        }

        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(newEntry.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType(Objects.equals(status, BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode()) ? "入账处理中" : "新建（待完成）")
                            .setOperName(operName)
                            .setOperContent("")
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return newEntry;
    }

    @Override
    public List<CommonFileVO> getBusinessTaskFiles(Long id, Integer fileType) {
        if (Objects.isNull(id)) {
            return Collections.emptyList();
        }
        BusinessTask businessTask = getById(id);
        if (Objects.isNull(businessTask) || businessTask.getIsDel() || Objects.isNull(businessTask.getBizId()) || StringUtils.isEmpty(businessTask.getBankAccountNumber())) {
            return Collections.emptyList();
        }
        Map<String, List<CustomerServiceCashierAccountingFile>> fileMap = customerServiceCashierAccountingService.selectBatchByPeriodIdAndBankAccountNumber(
                Collections.singletonList(PeriodBankAccountNumberVO.builder().bankAccountNumber(businessTask.getBankAccountNumber())
                        .periodId(businessTask.getBizId()).build()));
        List<CustomerServiceCashierAccountingFile> files = fileMap.get(businessTask.getBizId() + "_" + businessTask.getBankAccountNumber());
        return files.stream().map(file -> CommonFileVO.builder()
                .fileUrl(file.getFileUrl())
                .fileName(file.getFileName())
                .fileSize(file.getFileSize())
                .build()).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void modifyMattersNotes(BusinessTaskMattersNotesModifyVO vo, Long deptId) {
        BusinessTask businessTask = getById(vo.getBusinessTaskId());
        if (Objects.isNull(businessTask) || businessTask.getIsDel()) {
            throw new ServiceException("任务");
        }
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
        customerMattersNotesService.saveOrUpdateMattersNotes(businessTask.getCustomerServiceId(), CustomerServiceMattersNotesItemType.BANK_PAYMENT.getCode(), vo.getMattersNotes());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(businessTask.getCustomerServiceId())
                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                    .setDeptId(deptId)
                    .setOperType("编辑事项备忘（" + CustomerServiceMattersNotesItemType.BANK_PAYMENT.getName() + "）")
                    .setOperName(operateUserInfoDTO.getOperName())
                    .setCreateTime(LocalDateTime.now())
                    .setOperUserId(operateUserInfoDTO.getUserId())
                    .setOperContent(vo.getMattersNotes()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public List<BusinessTask> getByPeriodAndBankAccountNumber(RemoteBusinessTaskSearchV2VO vo) {
        if (ObjectUtils.isEmpty(vo.getPeriodBankAccountNumberList())) {
            return Collections.emptyList();
        }
        return baseMapper.getByPeriodAndBankAccountNumber(vo);
    }

    @Override
    @Transactional
    public void updateBankInfoByCustomerServiceId(Long customerServiceId, CustomerServiceBankAccount oldBankAccount, String bankAccountNumber, String bankName, Long deptId, Long userId, String operName) {
        List<BusinessTask> taskList = list(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .eq(BusinessTask::getType, BusinessTaskType.PERIOD.getCode())
                .eq(BusinessTask::getItemType, BusinessTaskItemType.BANK_PAYMENT.getCode())
                .eq(BusinessTask::getCustomerServiceId, customerServiceId)
                .eq(BusinessTask::getBankAccountNumber, oldBankAccount.getBankAccountNumber()));
        if (ObjectUtils.isEmpty(taskList)) {
            return;
        }
        LocalDateTime operTime = LocalDateTime.now();
        updateBatchById(taskList.stream().map(row -> new BusinessTask().setId(row.getId())
                .setTitle(String.format("流水任务单【%s-%s-%s】", bankName, bankAccountNumber, row.getPeriod()))
                .setBankName(bankName)
                .setBankAccountNumber(bankAccountNumber)).collect(Collectors.toList()));
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("修改前", String.format("%s，%s", oldBankAccount.getBankName(), oldBankAccount.getBankAccountNumber()));
        map.put("修改后", String.format("%s，%s", bankName, bankAccountNumber));
        taskList.forEach(row -> {
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(row.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType("银行信息修改")
                                .setOperContent(JSONObject.toJSONString(map))
                                .setOperName(operName)
                                .setCreateTime(operTime)
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });
    }

    @Override
    @Transactional
    public void exceptionDealByCheckFile(BusinessTask businessTask, LocalDateTime operTime, Integer checkType, R<CheckFilesDTO> resp) {
        String operType = "RPA异常交付";
        //结果：异常
        //状态：已关闭
        //操作记录：
        //oper_type：RPA异常交付
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        updateById(new BusinessTask().setId(businessTask.getId())
                .setFinishResult(BusinessTaskFinishResult._6.getCode())
                .setFinishTime(operTime)
                .setStatus(BusinessTaskStatus.CLOSED.getCode())
                .setLastOperateUserName("系统")
                .setLastOperateType(BusinessTaskOperateType.CLOSE.getCode())
                .setLastOperateTime(operTime)
                .setLastOperateUserId(1L));
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("结果", "异常");
        operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
        operContent.put("后置状态", BusinessTaskStatus.CLOSED.getName());
        boolean isSuccess = R.SUCCESS == resp.getCode();
        operContent.put("查询结果", isSuccess ? "成功" : "失败");
        if (isSuccess) {
            if (checkType == 1) {
                CheckFilesDTO dto = resp.getData();
                operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
            } else {
                CheckFilesDTO dto = resp.getData();
                String bankReceiptFileExistContent = getBankReceiptFileExistContent(dto.getBankReceiptFileExist());
                if (!StringUtils.isEmpty(bankReceiptFileExistContent)) {
                    operContent.put("识别状态", bankReceiptFileExistContent);
                }
                operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                if (!Objects.isNull(dto.getBankReceiptSuccessCount())) {
                    operContent.put("回单数量", dto.getBankReceiptSuccessCount());
                }
            }
        }
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(businessTask.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(null)
                            .setOperType(operType)
                            .setOperName("系统")
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setCreateTime(operTime)
                            .setOperRemark(resp.getMsg())
                            .setOperUserId(1L));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void noFlowByCheckFile(BusinessTask businessTask, LocalDateTime operTime, Integer checkType, R<CheckFilesDTO> resp) {
        String operType = "入账处理交付";
        //结果：无流水
        //状态：
        //如果流水任务单关联的客户归属会计小组的上级组织id不包含：242,247时，待完成
        //如果流水任务单关联的客户归属会计小组的上级组织id包含：242,247时，待审核
        //操作记录：
        //oper_type：入账处理交付
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        updateById(new BusinessTask().setId(businessTask.getId())
                .setFinishResult(BusinessTaskFinishResult._2.getCode())
                .setFinishTime(operTime)
                .setStatus(BusinessTaskStatus.NEED_FINISH.getCode())
                .setAdminUserId(null)
                .setAdminUserName(null)
                .setExecuteUserId(null)
                .setExecuteUserName(null)
                .setIsAssign(false)
                .setAssignTime(null)
                .setLastOperateUserName("系统")
                .setLastOperateType(BusinessTaskOperateType.DELIVER.getCode())
                .setLastOperateTime(operTime)
                .setLastOperateUserId(1L));
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("结果", "异常");
        operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
        operContent.put("后置状态", BusinessTaskStatus.NEED_FINISH.getName());
        boolean isSuccess = R.SUCCESS == resp.getCode();
        operContent.put("查询结果", isSuccess ? "成功" : "失败");
        if (isSuccess) {
            if (checkType == 1) {
                CheckFilesDTO dto = resp.getData();
                operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
            } else {
                CheckFilesDTO dto = resp.getData();
                String bankReceiptFileExistContent = getBankReceiptFileExistContent(dto.getBankReceiptFileExist());
                if (!StringUtils.isEmpty(bankReceiptFileExistContent)) {
                    operContent.put("识别状态", bankReceiptFileExistContent);
                }
                operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                if (!Objects.isNull(dto.getBankReceiptSuccessCount())) {
                    operContent.put("回单数量", dto.getBankReceiptSuccessCount());
                }
            }
        }
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(businessTask.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(null)
                            .setOperType(operType)
                            .setOperName("系统")
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setCreateTime(operTime)
                            .setOperRemark(resp.getMsg())
                            .setOperUserId(1L));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void normalCompleteByCheckFile(BusinessTask businessTask, LocalDateTime operTime, Integer checkType, R<CheckFilesDTO> resp) {
        String operType = "入账处理交付";
        //结果：正常
        //状态：
        //如果流水任务单关联的客户归属会计小组的上级组织id不包含：242,247时，待完成
        //如果流水任务单关联的客户归属会计小组的上级组织id包含：242,247时，待审核
        //操作记录：
        //oper_type：入账处理交付
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        updateById(new BusinessTask().setId(businessTask.getId())
                .setFinishResult(BusinessTaskFinishResult.OK.getCode())
                .setFinishTime(operTime)
                .setStatus(BusinessTaskStatus.NEED_FINISH.getCode())
                .setAdminUserId(null)
                .setAdminUserName(null)
                .setExecuteUserId(null)
                .setExecuteUserName(null)
                .setIsAssign(false)
                .setAssignTime(null)
                .setLastOperateUserName("系统")
                .setLastOperateType(BusinessTaskOperateType.DELIVER.getCode())
                .setLastOperateTime(operTime)
                .setLastOperateUserId(1L));
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("结果", "异常");
        operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
        operContent.put("后置状态", BusinessTaskStatus.NEED_FINISH.getName());
        boolean isSuccess = R.SUCCESS == resp.getCode();
        operContent.put("查询结果", isSuccess ? "成功" : "失败");
        if (isSuccess) {
            if (checkType == 1) {
                CheckFilesDTO dto = resp.getData();
                operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
            } else {
                CheckFilesDTO dto = resp.getData();
                String bankReceiptFileExistContent = getBankReceiptFileExistContent(dto.getBankReceiptFileExist());
                if (!StringUtils.isEmpty(bankReceiptFileExistContent)) {
                    operContent.put("识别状态", bankReceiptFileExistContent);
                }
                operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                if (!Objects.isNull(dto.getBankReceiptSuccessCount())) {
                    operContent.put("回单数量", dto.getBankReceiptSuccessCount());
                }
            }
        }
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(businessTask.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(null)
                            .setOperType(operType)
                            .setOperName("系统")
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setCreateTime(operTime)
                            .setOperRemark(resp.getMsg())
                            .setOperUserId(1L));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void waitInAccountByCheckFile(BusinessTask businessTask, LocalDateTime operTime) {
        String operType = "入账处理中";
        // 操作记录：
        //oper_type：入账处理中
        update(new LambdaUpdateWrapper<BusinessTask>()
                .eq(BusinessTask::getId, businessTask.getId())
                .set(BusinessTask::getFinishResult, null)
                .set(BusinessTask::getStatus, BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode())
                .set(BusinessTask::getWaitInAccountTime, operTime)
                .set(BusinessTask::getLastOperateTime, operTime)
                .set(BusinessTask::getLastOperateUserName, "系统")
                .set(BusinessTask::getLastOperateType, BusinessTaskOperateType.DELIVER.getCode())
                .set(BusinessTask::getLastOperateUserId, 1L));
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(businessTask.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(null)
                            .setOperType(operType)
                            .setOperName("系统")
                            .setCreateTime(operTime)
                            .setOperUserId(1L));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void exceptionDealByExtract(Long businessTaskId, LocalDateTime operTime, R resp, String operName, Long userId, Long deptId, String uuid, Integer checkType) {
        String operType = "RPA异常关闭";

        BusinessTask businessTask = getById(businessTaskId);
        if (Objects.isNull(businessTask) || businessTask.getIsDel() || Lists.newArrayList(BusinessTaskStatus.CLOSED.getCode(), BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.NEED_CHECK.getCode()).contains(businessTask.getStatus())) {
            return;
        } else {
            if (!Objects.isNull(resp)) {
                updateById(new BusinessTask().setId(businessTask.getId())
                        .setFinishTime(operTime)
                        .setStatus(BusinessTaskStatus.CLOSED.getCode())
                        .setLastOperateUserName(operName)
                        .setLastOperateType(BusinessTaskOperateType.EXCEPTION_CLOSE.getCode())
                        .setLastOperateTime(operTime)
                        .setLastOperateUserId(userId));
                boolean isSuccess = R.SUCCESS == resp.getCode();
                Map<String, Object> operContent = new LinkedHashMap<>();
                operContent.put("结果", "异常");
                operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
                operContent.put("后置状态", BusinessTaskStatus.CLOSED.getName());
                operContent.put("查询结果", isSuccess ? "成功" : "失败");
                if (resp.getData() instanceof CheckFilesDTO) {
                    CheckFilesDTO checkFilesDTO = (CheckFilesDTO) resp.getData();
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileExist())) {
                        operContent.put("识别状态", getBankReceiptFileExistContent(checkFilesDTO.getBankReceiptFileExist()));
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCount())) {
                        operContent.put("总数", checkFilesDTO.getBankReceiptFileCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFailCount())) {
                        operContent.put("失败数", checkFilesDTO.getBankReceiptFailCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptSuccessCount())) {
                        operContent.put("成功数", checkFilesDTO.getBankReceiptSuccessCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCheckStatus())) {
                        operContent.put("识别结果", getBankReceiptFileCheckStatusContent(checkFilesDTO.getBankReceiptFileCheckStatus()));
                    }
                }
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(businessTask.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType(operType)
                                    .setOperName(operName)
                                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                                    .setCreateTime(operTime)
                                    .setOperRemark(resp.getMsg())
                                    .setOperUserId(userId)
                                    .setCreateBy(Constants.RPA_CREATE_BY));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            } else {
                updateById(new BusinessTask().setId(businessTask.getId())
                        .setFinishResult(BusinessTaskFinishResult._6.getCode())
                        .setFinishTime(operTime)
                        .setStatus(BusinessTaskStatus.CLOSED.getCode())
                        .setLastOperateUserName(operName)
                        .setLastOperateType(BusinessTaskOperateType.CLOSE.getCode())
                        .setLastOperateTime(operTime)
                        .setLastOperateUserId(userId));
                Map<String, Object> operContent = new LinkedHashMap<>();
                operContent.put("结果", "异常");
                operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
                operContent.put("后置状态", BusinessTaskStatus.CLOSED.getName());
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(businessTask.getId())
                                    .setBusinessType(BIZ_LOG_TYPE)
                                    .setDeptId(deptId)
                                    .setOperType(operType)
                                    .setOperName(operName)
                                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                                    .setCreateTime(operTime)
                                    .setOperRemark("超时关闭")
                                    .setOperUserId(userId)
                                    .setCreateBy(Constants.RPA_CREATE_BY));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
        }
    }

    @Override
    @Transactional
    public void noFlowDeal(Long businessTaskId, LocalDateTime operTime, R resp, String operName, Long userId, Long deptId, String uuid, Integer checkType) {
        String operType = "RPA入账处理交付";

        BusinessTask businessTask = getById(businessTaskId);
        if (Objects.isNull(businessTask) || businessTask.getIsDel() || Lists.newArrayList(BusinessTaskStatus.CLOSED.getCode(), BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.NEED_CHECK.getCode()).contains(businessTask.getStatus())) {
            return;
        } else {
            BusinessTask update = new BusinessTask().setId(businessTask.getId())
                    .setFinishResult(BusinessTaskFinishResult._2.getCode())
                    .setFinishTime(operTime)
                    .setStatus(BusinessTaskStatus.NEED_FINISH.getCode())
                    .setIsAssign(false)
                    .setLastOperateUserName(operName)
                    .setLastOperateType(BusinessTaskOperateType.NO_FLOW_WAIT_CHECK.getCode())
                    .setLastOperateTime(operTime)
                    .setLastOperateUserId(userId);
            updateById(update);
            boolean isSuccess = R.SUCCESS == resp.getCode();
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("结果", "无流水");
            operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
            operContent.put("后置状态", BusinessTaskStatus.getByCode(update.getStatus()).getName());
            operContent.put("查询结果", isSuccess ? "成功" : "失败");
            if (isSuccess) {
                if (resp.getData() instanceof BanksEnterprisesExtractDTO) {
                    BanksEnterprisesExtractDTO dto = (BanksEnterprisesExtractDTO) resp.getData();
                    operContent.put("待下载回单文件数", dto.getCount());
                    operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                    operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
                } else if (resp.getData() instanceof GenerateVoucherDTO) {
                    GenerateVoucherDTO data = (GenerateVoucherDTO) resp.getData();
                    if (!Objects.isNull(data.getSuccessCount())) {
                        operContent.put("生成凭证成功条数", data.getSuccessCount());
                    }
                    if (!Objects.isNull(data.getFailCount())) {
                        operContent.put("生成凭证失败条数", data.getFailCount());
                    }
                } else if (resp.getData() instanceof CheckFilesDTO) {
                    CheckFilesDTO checkFilesDTO = (CheckFilesDTO) resp.getData();
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileExist())) {
                        operContent.put("识别状态", getBankReceiptFileExistContent(checkFilesDTO.getBankReceiptFileExist()));
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCount())) {
                        operContent.put("总数", checkFilesDTO.getBankReceiptFileCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFailCount())) {
                        operContent.put("失败数", checkFilesDTO.getBankReceiptFailCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptSuccessCount())) {
                        operContent.put("成功数", checkFilesDTO.getBankReceiptSuccessCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCheckStatus())) {
                        operContent.put("识别结果", getBankReceiptFileCheckStatusContent(checkFilesDTO.getBankReceiptFileCheckStatus()));
                    }
                }
            }
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(businessTask.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operName)
                                .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                                .setCreateTime(operTime)
                                .setOperRemark(resp.getMsg())
                                .setOperUserId(userId)
                                .setCreateBy(Constants.RPA_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            CCustomerService customerService = customerServiceMapper.selectById(businessTask.getCustomerServiceId());
            CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(businessTask.getBizId());
            if (checkBusinessTaskClose(customerService, customerServicePeriodMonth)) {
                noDispatchClose(businessTaskId, operTime, deptId, userId, operName);
                return;
            }
            autoDispatchBusinessTask(businessTaskId, customerService);
        }
    }

    @Override
    @Transactional
    public Long waitInAccountDeal(Long businessTaskId, LocalDateTime operTime, R resp, String operator, Long userId, Long deptId, String uuid, Integer checkType) {
        String operType = "RPA处理中";

        BusinessTask businessTask = getById(businessTaskId);
        if (Objects.isNull(businessTask) || businessTask.getIsDel() || Lists.newArrayList(BusinessTaskStatus.CLOSED.getCode(), BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.NEED_CHECK.getCode()).contains(businessTask.getStatus())) {
            return businessTaskId;
        } else {
            update(new LambdaUpdateWrapper<BusinessTask>()
                    .eq(BusinessTask::getId, businessTaskId)
                    .set(BusinessTask::getFinishResult, null)
                    .set(BusinessTask::getFinishTime, null)
                    .set(BusinessTask::getStatus, BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode())
                    .set(BusinessTask::getWaitInAccountTime, operTime)
                    .set(BusinessTask::getLastOperateUserName, operator)
                    .set(BusinessTask::getLastOperateType, BusinessTaskOperateType.WAIT_IN_ACCOUNT.getCode())
                    .set(BusinessTask::getLastOperateTime, operTime)
                    .set(BusinessTask::getLastOperateUserId, userId));
            boolean isSuccess = R.SUCCESS == resp.getCode();
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
            operContent.put("后置状态", BusinessTaskOperateType.RPA_DEALING.getName());
            operContent.put("查询结果", isSuccess ? "成功" : "失败");
            if (isSuccess) {
                if (resp.getData() instanceof BanksEnterprisesExtractDTO) {
                    BanksEnterprisesExtractDTO dto = (BanksEnterprisesExtractDTO) resp.getData();
                    operContent.put("待下载回单文件数", dto.getCount());
                    operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                    operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
                } else if (resp.getData() instanceof GenerateVoucherDTO) {
                    GenerateVoucherDTO data = (GenerateVoucherDTO) resp.getData();
                    if (!Objects.isNull(data.getSuccessCount())) {
                        operContent.put("生成凭证成功条数", data.getSuccessCount());
                    }
                    if (!Objects.isNull(data.getFailCount())) {
                        operContent.put("生成凭证失败条数", data.getFailCount());
                    }
                } else if (resp.getData() instanceof CheckFilesDTO) {
                    CheckFilesDTO checkFilesDTO = (CheckFilesDTO) resp.getData();
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileExist())) {
                        operContent.put("识别状态", getBankReceiptFileExistContent(checkFilesDTO.getBankReceiptFileExist()));
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCount())) {
                        operContent.put("总数", checkFilesDTO.getBankReceiptFileCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFailCount())) {
                        operContent.put("失败数", checkFilesDTO.getBankReceiptFailCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptSuccessCount())) {
                        operContent.put("成功数", checkFilesDTO.getBankReceiptSuccessCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCheckStatus())) {
                        operContent.put("识别结果", getBankReceiptFileCheckStatusContent(checkFilesDTO.getBankReceiptFileCheckStatus()));
                    }
                }
            }
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(businessTask.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operator)
                                .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                                .setCreateTime(operTime)
                                .setOperRemark(resp.getMsg())
                                .setOperUserId(userId)
                                .setCreateBy(Constants.RPA_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            return businessTaskId;
        }
    }

    @Override
    public Boolean checkBusinessTaskClose(CCustomerService customerService, CustomerServicePeriodMonth customerServicePeriodMonth) {
        if (Objects.isNull(customerServicePeriodMonth.getAccountingTopDeptId())) {
            return true;
        }
        if (!Objects.equals(customerServicePeriodMonth.getAccountingTopDeptId(), specialDeptIdProperties.getFjq())) {
            return true;
        }
        if (!Objects.isNull(customerServicePeriodMonth.getAccountingDeptId())) {
            SysDept sysDept = remoteDeptService.getDeptInfo(customerServicePeriodMonth.getAccountingDeptId()).getDataThrowException();
            Set<Long> zhuliDeptIds = zhuliDeptProperties.getMap().keySet();
            if (!Objects.isNull(sysDept) && "0".equals(sysDept.getDelFlag()) && Arrays.stream(sysDept.getAncestors().split(",")).map(StringUtils::stringToLong).anyMatch(zhuliDeptIds::contains)) {
                return true;
            }
        }
        List<CBusinessTagRelation> relations = businessTagRelationService.selectByBusinessIdAndBusinessType(customerService.getId(), TagBusinessType.CUSTOMER_SERVICE.getCode());
        return !ObjectUtils.isEmpty(relations) && relations.stream().anyMatch(row -> Objects.equals(row.getTagId(), specialTagProperties.getWxfprw()));
    }

    @Override
    public void autoDispatchBusinessTask(Long businessTaskId, CCustomerService customerService) {
        if (Objects.isNull(customerService.getAccountingDeptId())) {
            return;
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(customerService.getAccountingDeptId()).getDataThrowException();
        if (Objects.isNull(sysDept) || !"0".equals(sysDept.getDelFlag())) {
            return;
        }
        BusinessTask businessTask = getById(businessTaskId);
        if (Objects.isNull(businessTask) || !Objects.isNull(businessTask.getAdminUserId()) || !StringUtils.isEmpty(businessTask.getAdminUserName())) {
            return;
        }
        Map<Long, Long> zhuliDeptMap = zhuliDeptProperties.getMap();
        Long targetAdminDeptId = getTargetAdminDeptId(sysDept, zhuliDeptMap);
        log.info("自动分配任务，目标监管组ID:{}", targetAdminDeptId);
        Long adminUserId = zhuliDeptMap.get(targetAdminDeptId);
        log.info("自动分配任务，目标监管组管理员ID:{}", adminUserId);
        SysUser user = remoteUserService.getByUserId(adminUserId, SecurityConstants.INNER).getDataThrowException();
        LambdaUpdateWrapper<BusinessTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BusinessTask::getId, businessTaskId)
                .set(BusinessTask::getAdminUserId, adminUserId)
                .set(BusinessTask::getAdminUserName, Objects.isNull(user) ? "" : user.getNickName())
                .set(BusinessTask::getAdminDeptId, targetAdminDeptId);
        update(updateWrapper);
    }

    private Long getTargetAdminDeptId(SysDept sysDept, Map<Long, Long> zhuliDeptMap) {
        List<Long> sysDeptIds = Arrays.stream(sysDept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList());
        log.info("sysDeptIds:{}, zhuliDeptMap:{}", sysDeptIds, zhuliDeptMap);
        List<Long> deptIds = Lists.newArrayList();
        zhuliDeptMap.forEach((k, v) -> {
            if (!sysDeptIds.contains(k)) {
                deptIds.add(k);
            }
        });
        return baseMapper.selectLessTaskAdminDeptByDeptIds(deptIds);
    }

    @Override
    @Transactional
    public Long exceptionWaitDeal(Long businessTaskId, LocalDateTime operTime, R resp, String operator, Long userId, Long deptId, String uuid, Integer checkType) {
        String operType = "RPA入账处理完成";

        BusinessTask businessTask = getById(businessTaskId);
        if (Objects.isNull(businessTask) || businessTask.getIsDel() || Lists.newArrayList(BusinessTaskStatus.CLOSED.getCode(), BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.NEED_CHECK.getCode()).contains(businessTask.getStatus())) {
            return businessTaskId;
        } else {
            BusinessTask update = new BusinessTask().setId(businessTask.getId())
                    .setFinishResult(BusinessTaskFinishResult._6.getCode())
                    .setFinishTime(operTime)
                    .setStatus(BusinessTaskStatus.NEED_FINISH.getCode())
                    .setIsAssign(false)
                    .setAssignTime(null)
                    .setExecuteUserId(null)
                    .setExecuteUserName(null)
                    .setExecuteTime(null)
                    .setAdminUserName(null)
                    .setAdminUserId(null)
                    .setLastOperateUserName(operator)
                    .setLastOperateType(BusinessTaskOperateType.EXCEPTION_WAIT_CHECK.getCode())
                    .setLastOperateTime(operTime)
                    .setLastOperateUserId(userId);
            updateById(update);
            boolean isSuccess = R.SUCCESS == resp.getCode();
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("结果", "异常");
            operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
            operContent.put("后置状态", BusinessTaskStatus.getByCode(update.getStatus()).getName());
            operContent.put("查询结果", isSuccess ? "成功" : "失败");
            if (isSuccess) {
                if (resp.getData() instanceof BanksEnterprisesExtractDTO) {
                    BanksEnterprisesExtractDTO dto = (BanksEnterprisesExtractDTO) resp.getData();
                    operContent.put("待下载回单文件数", dto.getCount());
                    operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                    operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
                } else if (resp.getData() instanceof GenerateVoucherDTO) {
                    GenerateVoucherDTO data = (GenerateVoucherDTO) resp.getData();
                    if (!Objects.isNull(data.getSuccessCount())) {
                        operContent.put("生成凭证成功条数", data.getSuccessCount());
                    }
                    if (!Objects.isNull(data.getFailCount())) {
                        operContent.put("生成凭证失败条数", data.getFailCount());
                    }
                } else if (resp.getData() instanceof CheckFilesDTO) {
                    CheckFilesDTO checkFilesDTO = (CheckFilesDTO) resp.getData();
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileExist())) {
                        operContent.put("识别状态", getBankReceiptFileExistContent(checkFilesDTO.getBankReceiptFileExist()));
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCount())) {
                        operContent.put("总数", checkFilesDTO.getBankReceiptFileCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFailCount())) {
                        operContent.put("失败数", checkFilesDTO.getBankReceiptFailCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptSuccessCount())) {
                        operContent.put("成功数", checkFilesDTO.getBankReceiptSuccessCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCheckStatus())) {
                        operContent.put("识别结果", getBankReceiptFileCheckStatusContent(checkFilesDTO.getBankReceiptFileCheckStatus()));
                    }
                }
            }
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(businessTask.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operator)
                                .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                                .setCreateTime(operTime)
                                .setOperRemark(resp.getMsg())
                                .setOperUserId(userId)
                                .setCreateBy(Constants.RPA_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            CCustomerService customerService = customerServiceMapper.selectById(businessTask.getCustomerServiceId());
            CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(businessTask.getBizId());
            if (checkBusinessTaskClose(customerService, customerServicePeriodMonth)) {
                noDispatchClose(businessTaskId, operTime, deptId, userId, operator);
                return businessTaskId;
            }
            autoDispatchBusinessTask(businessTaskId, customerService);
            return businessTaskId;
        }
    }

    @Override
    @Transactional
    public void normalCompleteDeal(Long businessTaskId, LocalDateTime operTime, R resp, String operator, Long userId, Long deptId, String uuid, Integer checkType) {
        String operType = "RPA入账处理交付";

        BusinessTask businessTask = getById(businessTaskId);
        if (Objects.isNull(businessTask) || businessTask.getIsDel() || Lists.newArrayList(BusinessTaskStatus.CLOSED.getCode(), BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.NEED_CHECK.getCode()).contains(businessTask.getStatus())) {
            return;
        } else {
            BusinessTask update = new BusinessTask().setId(businessTask.getId())
                    .setFinishResult(BusinessTaskFinishResult.OK.getCode())
                    .setFinishTime(operTime)
                    .setStatus(BusinessTaskStatus.NEED_FINISH.getCode())
                    .setIsAssign(false)
                    .setLastOperateUserName(operator)
                    .setLastOperateType(BusinessTaskOperateType.NORMAL_WAIT_CHECK.getCode())
                    .setLastOperateTime(operTime)
                    .setLastOperateUserId(userId);
            updateById(update);
            boolean isSuccess = R.SUCCESS == resp.getCode();
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("结果", "正常");
            operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
            operContent.put("后置状态", BusinessTaskStatus.getByCode(update.getStatus()).getName());
            operContent.put("查询结果", isSuccess ? "成功" : "失败");
            if (isSuccess) {
                if (resp.getData() instanceof BanksEnterprisesExtractDTO) {
                    BanksEnterprisesExtractDTO dto = (BanksEnterprisesExtractDTO) resp.getData();
                    operContent.put("待下载回单文件数", dto.getCount());
                    operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                    operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
                } else if (resp.getData() instanceof GenerateVoucherDTO) {
                    GenerateVoucherDTO data = (GenerateVoucherDTO) resp.getData();
                    if (!Objects.isNull(data.getSuccessCount())) {
                        operContent.put("生成凭证成功条数", data.getSuccessCount());
                    }
                    if (!Objects.isNull(data.getFailCount())) {
                        operContent.put("生成凭证失败条数", data.getFailCount());
                    }
                }
            }
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(businessTask.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operator)
                                .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                                .setCreateTime(operTime)
                                .setOperRemark(resp.getMsg())
                                .setOperUserId(userId)
                                .setCreateBy(Constants.RPA_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            CCustomerService customerService = customerServiceMapper.selectById(businessTask.getCustomerServiceId());
            CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(businessTask.getBizId());
            if (checkBusinessTaskClose(customerService, customerServicePeriodMonth)) {
                noDispatchClose(businessTaskId, operTime, deptId, userId, operator);
                return;
            }
            autoDispatchBusinessTask(businessTaskId, customerService);
        }
    }

    @Override
    public boolean existsNeedCheckTask(CustomerServiceCashierAccounting customerServiceCashierAccounting) {
        return count(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .eq(BusinessTask::getStatus, BusinessTaskStatus.NEED_CHECK.getCode())
                .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .eq(BusinessTask::getItemType, BusinessTaskItemType.BANK_PAYMENT.getCode())
                .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())) > 0;
    }

    @Override
    public Map<String, List<BusinessTask>> getBatchNeedCheckBusinessTaskByAccountingCashier(List<CustomerServiceCashierAccounting> cashierList) {
        if (ObjectUtils.isEmpty(cashierList)) {
            return Collections.emptyMap();
        }
        return baseMapper.getBatchNeedCheckBusinessTaskByAccountingCashier(cashierList)
                .stream().collect(Collectors.groupingBy(row -> row.getBizId() + "_" + row.getBankAccountNumber()));
    }

    @Override
    public void dealBusinessTask(Integer deliverStatus, CustomerServiceCashierAccounting customerServiceCashierAccounting, String operName, Long userId, Long deptId, String operRemark) {
        BusinessTask businessTask = getOne(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .notIn(BusinessTask::getStatus, BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.CLOSED.getCode())
                .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .eq(BusinessTask::getItemType, BusinessTaskItemType.BANK_PAYMENT.getCode())
                .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())
                .last("limit 1"));
        if (!Objects.isNull(businessTask)) {
            String operType = "关闭";
            LocalDateTime operTime = LocalDateTime.now();
            BusinessTask update = new BusinessTask().setId(businessTask.getId())
                    .setFinishResult(!Objects.isNull(businessTask.getFinishResult()) ? businessTask.getFinishResult() : (Objects.equals(deliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode()) ? BusinessTaskFinishResult.OK.getCode() : BusinessTaskFinishResult._6.getCode()))
                    .setFinishTime(operTime)
                    .setStatus(BusinessTaskStatus.CLOSED.getCode())
                    .setLastOperateUserName(operName)
                    .setLastOperateType(BusinessTaskOperateType.CLOSE.getCode())
                    .setLastOperateTime(operTime)
                    .setLastOperateUserId(userId);
            updateById(update);
            Map<String, String> operContent = new LinkedHashMap<>();
            operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
            operContent.put("后置状态", BusinessTaskStatus.getByCode(update.getStatus()).getName());
            operContent.put("结果", BusinessTaskFinishResult.getByCode(update.getFinishResult()).getName());
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(businessTask.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operName)
                                .setCreateTime(operTime)
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setOperRemark(Objects.equals(deliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode()) ? "流水交付单已交付" : operRemark)
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    @Transactional
    public void dealBusinessTask(Integer deliverStatus, List<CustomerServiceCashierAccounting> cashierList, String operName, Long userId, Long deptId) {
        if (!ObjectUtils.isEmpty(cashierList)) {
            cashierList.forEach(customerServiceCashierAccounting -> dealBusinessTask(deliverStatus, customerServiceCashierAccounting, operName, userId, deptId, "异常交付关闭"));
        }
    }

    @Override
    public void businessTaskGenerateVoucher(Long businessTaskId, LocalDateTime operTime, R<GenerateVoucherDTO> resp, String operator, Long userId, Long deptId, String uuid, Object o) {
        boolean isSuccess = R.SUCCESS == resp.getCode();
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("结果", isSuccess ? "成功" : "失败");
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(businessTaskId)
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("生成凭证")
                            .setOperName(operator)
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setCreateTime(operTime)
                            .setOperRemark(resp.getMsg())
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void overTimeClose(Long businessTaskId, LocalDateTime operTime, Long userId, Long deptId, String operator) {
        if (Objects.isNull(businessTaskId)) {
            return;
        }
        BusinessTask businessTask = getById(businessTaskId);
        if (Objects.isNull(businessTask) || businessTask.getIsDel() || Lists.newArrayList(BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.CLOSED.getCode()).contains(businessTask.getStatus())) {
            return;
        }
        LocalDateTime time = operTime.plusSeconds(1);
        List<Long> userIds = Lists.newArrayList();
        userIds.add(specialUserIdIdProperties.getXtzl());
        userIds.add(specialUserIdIdProperties.getXtzlzg());
        Map<Long, SysUser> userMap = remoteUserService.getBatchUserByIds(userIds).getDataThrowException();
        SysUser adminUser = userMap.get(specialUserIdIdProperties.getXtzlzg());
        SysUser executeUser = userMap.get(specialUserIdIdProperties.getXtzl());
        BusinessTask update = new BusinessTask().setId(businessTask.getId())
                .setFinishResult(BusinessTaskFinishResult._6.getCode())
                .setFinishTime(time)
                .setStatus(BusinessTaskStatus.CLOSED.getCode())
                .setAdminUserId(specialUserIdIdProperties.getXtzlzg())
                .setAdminUserName(Objects.isNull(adminUser) ? "" : adminUser.getNickName())
                .setExecuteUserId(specialUserIdIdProperties.getXtzl())
                .setExecuteTime(time)
                .setExecuteUserName(Objects.isNull(executeUser) ? "" : executeUser.getNickName())
                .setIsAssign(true)
                .setLastOperateUserName(operator)
                .setLastOperateType(BusinessTaskOperateType.CLOSE.getCode())
                .setLastOperateTime(time)
                .setLastOperateUserId(userId);
        updateById(update);
        Map<String, String> operContent = new LinkedHashMap<>();
        operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
        operContent.put("后置状态", BusinessTaskStatus.getByCode(update.getStatus()).getName());
        operContent.put("结果", BusinessTaskFinishResult.getByCode(update.getFinishResult()).getName());
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(businessTask.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("自动关闭")
                            .setOperName(operator)
                            .setCreateTime(time)
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setOperRemark("超期交接")
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void noDispatchClose(Long businessTaskId, LocalDateTime operTime, Long deptId, Long userId, String operName) {
        if (Objects.isNull(businessTaskId)) {
            return;
        }
        BusinessTask businessTask = getById(businessTaskId);
        if (Objects.isNull(businessTask) || businessTask.getIsDel() || Lists.newArrayList(BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.CLOSED.getCode()).contains(businessTask.getStatus())) {
            return;
        }
        LocalDateTime time = operTime.plusSeconds(1);
        List<Long> userIds = Lists.newArrayList();
        userIds.add(specialUserIdIdProperties.getXtzl());
        userIds.add(specialUserIdIdProperties.getXtzlzg());
        Map<Long, SysUser> userMap = remoteUserService.getBatchUserByIds(userIds).getDataThrowException();
        SysUser adminUser = userMap.get(specialUserIdIdProperties.getXtzlzg());
        SysUser executeUser = userMap.get(specialUserIdIdProperties.getXtzl());
        BusinessTask update = new BusinessTask().setId(businessTask.getId())
                .setFinishResult(Objects.isNull(businessTask.getFinishResult()) ? BusinessTaskFinishResult._6.getCode() : businessTask.getFinishResult())
                .setFinishTime(time)
                .setStatus(BusinessTaskStatus.CLOSED.getCode())
                .setAdminUserId(specialUserIdIdProperties.getXtzlzg())
                .setAdminUserName(Objects.isNull(adminUser) ? "" : adminUser.getNickName())
                .setExecuteUserId(specialUserIdIdProperties.getXtzl())
                .setExecuteTime(time)
                .setExecuteUserName(Objects.isNull(executeUser) ? "" : executeUser.getNickName())
                .setIsAssign(true)
                .setLastOperateUserName(operName)
                .setLastOperateType(BusinessTaskOperateType.CLOSE.getCode())
                .setLastOperateTime(time)
                .setLastOperateUserId(userId);
        updateById(update);
        Map<String, String> operContent = new LinkedHashMap<>();
        operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
        operContent.put("后置状态", BusinessTaskStatus.getByCode(update.getStatus()).getName());
        operContent.put("结果", BusinessTaskFinishResult.getByCode(update.getFinishResult()).getName());
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(businessTask.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("自动关闭")
                            .setOperName(operName)
                            .setCreateTime(time)
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setOperRemark("无需分配任务")
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void checkFileClose(Long businessTaskId, LocalDateTime operTime, R<CheckFilesDTO> checkFileResp, String operator, Long userId, Long deptId, String uuid, Integer checkType) {
        if (Objects.isNull(businessTaskId)) {
            return;
        }
        BusinessTask businessTask = getById(businessTaskId);
        if (Objects.isNull(businessTask) || businessTask.getIsDel() || Lists.newArrayList(BusinessTaskStatus.FINISHED.getCode(), BusinessTaskStatus.CLOSED.getCode()).contains(businessTask.getStatus())) {
            return;
        }
        LocalDateTime time = operTime.plusSeconds(1);
        List<Long> userIds = Lists.newArrayList();
        userIds.add(specialUserIdIdProperties.getXtzl());
        userIds.add(specialUserIdIdProperties.getXtzlzg());
        Map<Long, SysUser> userMap = remoteUserService.getBatchUserByIds(userIds).getDataThrowException();
        SysUser adminUser = userMap.get(specialUserIdIdProperties.getXtzlzg());
        SysUser executeUser = userMap.get(specialUserIdIdProperties.getXtzl());
        BusinessTask update = new BusinessTask().setId(businessTask.getId())
                .setFinishResult(Objects.isNull(businessTask.getFinishResult()) ? BusinessTaskFinishResult._6.getCode() : businessTask.getFinishResult())
                .setFinishTime(time)
                .setStatus(BusinessTaskStatus.CLOSED.getCode())
                .setAdminUserId(specialUserIdIdProperties.getXtzlzg())
                .setAdminUserName(Objects.isNull(adminUser) ? "" : adminUser.getNickName())
                .setExecuteUserId(specialUserIdIdProperties.getXtzl())
                .setExecuteTime(time)
                .setExecuteUserName(Objects.isNull(executeUser) ? "" : executeUser.getNickName())
                .setIsAssign(true)
                .setLastOperateUserName(operator)
                .setLastOperateType(BusinessTaskOperateType.CLOSE.getCode())
                .setLastOperateTime(time)
                .setLastOperateUserId(userId);
        updateById(update);
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("前置状态", BusinessTaskStatus.getByCode(businessTask.getStatus()).getName());
        operContent.put("后置状态", BusinessTaskStatus.getByCode(update.getStatus()).getName());
        operContent.put("结果", BusinessTaskFinishResult.getByCode(update.getFinishResult()).getName());
        CheckFilesDTO checkFilesDTO = checkFileResp.getData();
        if (!Objects.isNull(checkFilesDTO.getBankReceiptFileExist())) {
            operContent.put("识别状态", getBankReceiptFileExistContent(checkFilesDTO.getBankReceiptFileExist()));
        }
        if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCount())) {
            operContent.put("总数", checkFilesDTO.getBankReceiptFileCount());
        }
        if (!Objects.isNull(checkFilesDTO.getBankReceiptFailCount())) {
            operContent.put("失败数", checkFilesDTO.getBankReceiptFailCount());
        }
        if (!Objects.isNull(checkFilesDTO.getBankReceiptSuccessCount())) {
            operContent.put("成功数", checkFilesDTO.getBankReceiptSuccessCount());
        }
        if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCheckStatus())) {
            operContent.put("识别结果", getBankReceiptFileCheckStatusContent(checkFilesDTO.getBankReceiptFileCheckStatus()));
        }
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(businessTask.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType("自动关闭")
                            .setOperName(operator)
                            .setCreateTime(time)
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setOperRemark("流水交付单当前状态为" + BusinessTaskStatus.getByCode(businessTask.getStatus()).getName())
                            .setOperUserId(userId)
                            .setCreateBy(Constants.RPA_CREATE_BY));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void waitInAccountBusinessTaskAutoCloseTask() {
        // 查询出 状态变更为 待入账处理的任务的变更时间超过24小时的数据
        List<BusinessTask> waitDealData = list(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .eq(BusinessTask::getStatus, BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode())
                .le(BusinessTask::getWaitInAccountTime, LocalDateTime.now().minusDays(1)));
        if (ObjectUtils.isEmpty(waitDealData)) {
            log.info("待入账处理超过24小时================无待处理数据");
            return;
        }
        Long userId = 1L;
        String operName = "系统";
        LocalDateTime operTime = LocalDateTime.now();
        Map<String, Object> taskOperContentMap = new LinkedHashMap<>();
        taskOperContentMap.put("结果", "异常");
        taskOperContentMap.put("前置状态", BusinessTaskStatus.WAIT_IN_ACCOUNT.getName());
        taskOperContentMap.put("后置状态", BusinessTaskStatus.CLOSED.getName());
        update(new LambdaUpdateWrapper<BusinessTask>().in(BusinessTask::getId, waitDealData.stream().map(BusinessTask::getId).collect(Collectors.toList()))
                .set(BusinessTask::getFinishResult, BusinessTaskFinishResult._6.getCode())
                .set(BusinessTask::getStatus, BusinessTaskStatus.CLOSED.getCode())
                .set(BusinessTask::getWaitInAccountTime, null)
                .set(BusinessTask::getLastOperateUserName, operName)
                .set(BusinessTask::getLastOperateTime, operTime)
                .set(BusinessTask::getLastOperateUserId, userId)
                .set(BusinessTask::getLastOperateType, BusinessTaskOperateType.EXCEPTION_DELIVER.getCode()));
        List<BizIdBankAccountDTO> dtoList = waitDealData.stream().filter(row -> !Objects.isNull(row.getBizId()) && !StringUtils.isEmpty(row.getBankAccountNumber())).map(row -> BizIdBankAccountDTO.builder()
                .bizId(row.getBizId())
                .bankAccountNumber(row.getBankAccountNumber()).build()).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(dtoList)) {
            List<CustomerServiceCashierAccounting> cashierAccountingList = customerServiceCashierAccountingMapper.selectBatchByCustomerServicePeriodMonthIdAndBankAccountNumber(dtoList);
            if (!ObjectUtils.isEmpty(cashierAccountingList)) {
                customerServiceCashierAccountingService.update(new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                        .in(CustomerServiceCashierAccounting::getId, cashierAccountingList.stream().map(CustomerServiceCashierAccounting::getId).collect(Collectors.toList()))
                        .set(CustomerServiceCashierAccounting::getDeliverStatus, AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode())
                        .set(CustomerServiceCashierAccounting::getDeliverResult, AccountingCashierDeliverResult.EXCEPTION.getCode())
                        .set(CustomerServiceCashierAccounting::getLastOperType, "入账处理异常交付")
                        .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                        .set(CustomerServiceCashierAccounting::getLastOperName, operName));
                cashierAccountingList.forEach(cashierAccounting -> {
                    Map<String, Object> operContentMap = new LinkedHashMap<>();
                    operContentMap.put("结果", "异常");
                    operContentMap.put("前置状态", AccountingCashierDeliverStatus.getByCode(cashierAccounting.getDeliverStatus()).getName());
                    operContentMap.put("后置状态", AccountingCashierDeliverStatus.WAIT_SUBMIT.getName());
                    try {
                        asyncLogService.saveBusinessLog(
                                new BusinessLogDTO()
                                        .setBusinessId(cashierAccounting.getId())
                                        .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                        .setDeptId(null)
                                        .setOperType("入账处理异常交付")
                                        .setOperName(operName)
                                        .setOperContent(JSONObject.toJSONString(operContentMap))
                                        .setCreateTime(operTime)
                                        .setOperUserId(userId));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
        }
        waitDealData.forEach(businessTask -> {
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(businessTask.getId())
                                .setBusinessType(BIZ_LOG_TYPE)
                                .setDeptId(null)
                                .setOperType(BusinessTaskOperateType.EXCEPTION_DELIVER.getName())
                                .setOperName(operName)
                                .setOperContent(JSONObject.toJSONString(taskOperContentMap))
                                .setCreateTime(operTime)
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });
    }

    @Override
    @Transactional
    public TCommonOperateDTO<BusinessTask> batchUpdateBankAccountNumber(BatchUpdateBankAccountNumberVO vo, Long deptId) {
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, userId);
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "编辑银行账号";
        if (ObjectUtils.isEmpty(vo.getIds())) {
            throw new ServiceException("请选择要编辑的数据");
        }
        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        List<BusinessTask> totalList = list(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .in(BusinessTask::getId, vo.getIds()));
        result.setTotal(totalList);
        if (ObjectUtils.isEmpty(totalList)) {
            result.setSuccess(Collections.emptyList());
            result.setFail(Collections.emptyList());
            return result;
        }
        List<BusinessTask> successList = Lists.newArrayList();
        List<BusinessTask> failList = Lists.newArrayList();
        Map<Long, List<CustomerServiceBankAccount>> bankMap = customerServiceBankAccountMapper.selectList(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                        .in(CustomerServiceBankAccount::getCustomerServiceId, totalList.stream().map(BusinessTask::getCustomerServiceId).distinct().collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(CustomerServiceBankAccount::getCustomerServiceId));
        Map<Long, String> bankNameMap = new HashMap<>();
        for (BusinessTask businessTask : totalList) {
            List<CustomerServiceBankAccount> bankList = bankMap.get(businessTask.getCustomerServiceId());
            if (ObjectUtils.isEmpty(bankList)) {
                failList.add(businessTask);
            } else {
                CustomerServiceBankAccount bankAccount = bankList.stream().filter(row -> Objects.equals(row.getBankAccountNumber(), vo.getBankAccountNumber())).findFirst().orElse(null);
                if (Objects.isNull(bankAccount)) {
                    failList.add(businessTask);
                } else {
                    if ((!Objects.isNull(bankAccount.getAccountOpenDate()) && businessTask.getPeriod() < Integer.parseInt(bankAccount.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM"))))
                            || (!Objects.isNull(bankAccount.getAccountCloseDate()) && businessTask.getPeriod() > Integer.parseInt(bankAccount.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM"))))) {
                        failList.add(businessTask);
                    } else {
                        successList.add(businessTask);
                        bankNameMap.put(businessTask.getId(), bankAccount.getBankName());
                    }
                }
            }
        }
        result.setSuccess(successList);
        result.setFail(failList);
        if (!ObjectUtils.isEmpty(successList)) {
            successList.forEach(businessTask -> {
                String bankName = bankNameMap.get(businessTask.getId());
                update(new LambdaUpdateWrapper<BusinessTask>()
                        .eq(BusinessTask::getId, businessTask.getId())
                        .set(BusinessTask::getBankAccountNumber, vo.getBankAccountNumber())
                        .set(BusinessTask::getBankName, bankName)
                        .set(BusinessTask::getTitle, String.format("流水任务单【%s-%s-%s】", bankName, vo.getBankAccountNumber(), businessTask.getPeriod()))
                        .set(BusinessTask::getLastOperateType, BusinessTaskOperateType.MODIFY_BANK_ACCOUNT_NUMBER.getCode())
                        .set(BusinessTask::getLastOperateUserName, operateUserInfoDTO.getOperName())
                        .set(BusinessTask::getLastOperateTime, operTime)
                        .set(BusinessTask::getLastOperateUserId, operateUserInfoDTO.getUserId())
                );
                Map<String, String> operContent = Maps.newLinkedHashMap();
                operContent.put("原账号", businessTask.getBankAccountNumber());
                operContent.put("现账号", vo.getBankAccountNumber());
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(businessTask.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(operateUserInfoDTO.getDeptId())
                            .setOperType(operType)
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setCreateTime(operTime)
                            .setOperUserId(operateUserInfoDTO.getUserId())
                            .setOperContent(JSONObject.toJSONString(operContent)));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return result;
    }

    @Override
    @Transactional
    public TCommonOperateDTO<BusinessTask> managerDelete(DeleteBatchVO vo, Long deptId) {
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "管理删除";
        if (ObjectUtils.isEmpty(vo.getIds())) {
            throw new ServiceException("请选择要删除的数据");
        }
        TCommonOperateDTO<BusinessTask> result = new TCommonOperateDTO<>();
        List<BusinessTask> totalList = list(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getIsDel, false)
                .in(BusinessTask::getId, vo.getIds()));
        result.setTotal(totalList);
        if (ObjectUtils.isEmpty(totalList)) {
            result.setSuccess(Collections.emptyList());
            result.setFail(Collections.emptyList());
            return result;
        }
        List<BusinessTask> successList = Lists.newArrayList();
        List<BusinessTask> failList = Lists.newArrayList();
        for (BusinessTask businessTask : totalList) {
            successList.add(businessTask);
        }
        result.setSuccess(successList);
        result.setFail(failList);
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream().map(row -> new BusinessTask()
                    .setId(row.getId())
                    .setIsDel(true)
                    .setLastOperateType(BusinessTaskOperateType.DELETE.getCode())
                    .setLastOperateTime(operTime)
                    .setLastOperateUserName(operateUserInfoDTO.getOperName())
                    .setLastOperateUserId(operateUserInfoDTO.getUserId())).collect(Collectors.toList()));
            successList.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BIZ_LOG_TYPE)
                            .setDeptId(deptId)
                            .setOperType(operType)
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setCreateTime(operTime)
                            .setOperUserId(operateUserInfoDTO.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return result;
    }

    private String getBankReceiptFileExistContent(Integer bankReceiptFileExist) {
        if (null == bankReceiptFileExist) {
            return "";
        }
        // -1 识别中，0 失败，1 成功，2 部分成功
        switch (bankReceiptFileExist) {
            case -1:
                return "识别中";
            case 0:
                return "全部失败";
            case 1:
                return "全部成功";
            case 2:
                return "部分成功";
            default:
                return "";
        }
    }

    private String getBankReceiptFileCheckStatusContent(Integer bankReceiptFileCheckStatus) {
        if (null == bankReceiptFileCheckStatus) {
            return "";
        }
        // 1=待再次查询，2=正常交付（有流水），3=正常交付（无流水），4=异常待会计核实，5=异常待顾问处理
        switch (bankReceiptFileCheckStatus) {
            case 1:
                return "待再次查询";
            case 2:
                return "正常交付（有流水）";
            case 3:
                return "正常交付（无流水）";
            case 4:
                return "异常待会计核实";
            case 5:
                return "异常待顾问处理";
            default:
                return "";
        }
    }

    private List<Long> handleIds(List<List<Long>> idsList) {
        List<Long> result = Lists.newArrayList();

        if (!ObjectUtils.isEmpty(idsList)) {

            for (List<Long> row : idsList) {

                if (!ObjectUtils.isEmpty(row)) {
                    row = row.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());

                    if (!ObjectUtils.isEmpty(row)) {
                        result.addAll(row);
                    }
                }
            }

            result = result.stream().distinct().collect(Collectors.toList());
        }

        return result;
    }

    private static void handleOperator(BusinessTask row, SysUser operatorUser, BusinessTaskOperateType businessTaskOperateType) {
        if (row != null && businessTaskOperateType != null) {
            row.setLastOperateTime(LocalDateTime.now());
            row.setLastOperateType(businessTaskOperateType.getCode());
            row.setLastOperateUserId(operatorUser == null ? null : operatorUser.getUserId());
            row.setLastOperateUserName(operatorUser == null ? null : operatorUser.getNickName());
        }
    }

    private static CustomerServiceInAccount handleForInAccountDeliver(BusinessTask row, LocalDate now, CustomerServiceInAccount customerServiceInAccount) {
        //可操作的条件：状态是“待审核”。确定后再进行操作。操作后按以下逻辑对入账交付单进行操作：
        //当任务单的结果不为“银行部分缺”时，需要去操作入账交付单。
        //如果入账交付单的银行流水为空时，将审核时间所在日写为银行流水录入时间。
        //且根据入账交付单的逻辑来影响入账交付单的状态（只有银行流水录入时间时，入账状态不变，有银行流水录入时间和入账时间时，取最晚为结账时间，且入账状态改为已入账已结账）

        if (row == null || customerServiceInAccount == null) {
            return null;
        }

        if (Objects.equals(row.getFinishResult(), BusinessTaskFinishResult._4.getCode()) || Objects.equals(row.getFinishResult(), BusinessTaskFinishResult._6.getCode())) {
            return null;
        }

        if (customerServiceInAccount.getBankPaymentInputTime() == null) {
            customerServiceInAccount.setBankPaymentInputTime(now);

            if (customerServiceInAccount.getInTime() != null) {
                LocalDate max;
                if (customerServiceInAccount.getBankPaymentInputTime().isAfter(customerServiceInAccount.getInTime())) {
                    max = customerServiceInAccount.getBankPaymentInputTime();
                } else {
                    max = customerServiceInAccount.getInTime();
                }

                customerServiceInAccount.setEndTime(max);

                customerServiceInAccount.setStatus(InAccountStatus.DONE.getCode());
            }
        }

        return customerServiceInAccount;
    }

    private static CustomerServiceInAccount handleForInAccountDeliverV2(BusinessTask row, LocalDate now, CustomerServiceInAccount customerServiceInAccount) {
        if (row == null || customerServiceInAccount == null) {
            return null;
        }
        if (Objects.equals(row.getFinishResult(), BusinessTaskFinishResult._6.getCode())) {
            return null;
        }

        Integer bangPaymentInputResult = BankPaymentInputResult.convertFromFinishResult(row.getFinishResult());
        customerServiceInAccount.setBankPaymentInputResult(bangPaymentInputResult);
        LocalDate inTime = customerServiceInAccount.getInTime();
        LocalDate paymentInputTime = customerServiceInAccount.getBankPaymentInputTime();
        if (Objects.isNull(paymentInputTime) && BankPaymentInputResult.setBankPaymentInputTimeResult().contains(bangPaymentInputResult)) {
            paymentInputTime = now;
            customerServiceInAccount.setBankPaymentInputTime(paymentInputTime);
            if (!Objects.isNull(inTime)) {
                LocalDate max;
                if (paymentInputTime.isAfter(inTime)) {
                    max = paymentInputTime;
                } else {
                    max = inTime;
                }
                customerServiceInAccount.setEndTime(max);
                customerServiceInAccount.setStatus(InAccountStatus.DONE.getCode());
            }
        }

        return customerServiceInAccount;
    }
}
