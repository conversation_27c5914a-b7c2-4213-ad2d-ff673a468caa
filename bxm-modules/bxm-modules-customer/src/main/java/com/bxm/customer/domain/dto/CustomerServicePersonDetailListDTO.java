package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePersonDetailListDTO {

    @ApiModelProperty("姓名")
    @Excel(name = "姓名")
    private String name;

    @ApiModelProperty("身份证号")
    @Excel(name = "身份证号")
    private String idNumber;

    @ApiModelProperty("手机号")
    @Excel(name = "手机号")
    private String mobile;

    @ApiModelProperty("公积金个人金额")
    @Excel(name = "公积金个人金额")
    private String gongjijinAmount;

    @ApiModelProperty("养老")
    private Boolean yanglao;

    @ApiModelProperty(hidden = true)
    @Excel(name = "养老")
    private String yanglaoValue;

    @ApiModelProperty("失业")
    private Boolean shiye;

    @ApiModelProperty(hidden = true)
    @Excel(name = "失业")
    private String shiyeValue;

    @ApiModelProperty("工伤")
    private Boolean gongshang;

    @ApiModelProperty(hidden = true)
    @Excel(name = "工伤")
    private String gongshangValue;

    @ApiModelProperty("医疗")
    private Boolean yiliao;

    @ApiModelProperty(hidden = true)
    @Excel(name = "医疗")
    private String yiliaoValue;

    @ApiModelProperty("生育")
    private Boolean shengyu;

    @ApiModelProperty(hidden = true)
    @Excel(name = "生育")
    private String shengyuValue;
}
