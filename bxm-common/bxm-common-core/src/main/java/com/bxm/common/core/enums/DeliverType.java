package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum DeliverType {

    // 1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算,8-年报，9-残保金
    MEDICAL_INSURANCE(1, "医保",20),
    SOCIAL_INSURANCE(2, "社保", 30),
    TAX(3, "个税（工资薪金）", 40),
    PRE_AUTH(5, "预认证", 10),
    NATIONAL_TAX(4, "国税", 50),
    TAX_OPERATING_INCOME(6, "个税（其他）", 35),
    SETTLE_ACCOUNTS(7, "汇算", 60),
    ANNUAL_REPORT(8, "工商年报", 70),
    RESIDUAL_BENEFITS(9, "残保金", 80),
    TIMES_REPORT(10, "次报", 90),
    UN_KNOW(99, "未知", 0),
    ;

    private final Integer code;

    private final String name;

    public static DeliverType getByCode(Integer code) {
        for (DeliverType deliverType : DeliverType.values()) {
            if (deliverType.getCode().equals(code)) {
                return deliverType;
            }
        }
        return UN_KNOW;
    }

    //排序：账务、国税、个税（工资薪金）、社保、医保、预认证、汇算、年报
    private final Integer sortNum;

    public static List<Integer> autoConfirmDeliverTypeList() {
        return Arrays.asList(MEDICAL_INSURANCE.getCode(), SOCIAL_INSURANCE.getCode(), TAX.getCode(), NATIONAL_TAX.getCode(), TAX_OPERATING_INCOME.getCode());
    }

    public static List<Integer> allTypes() {
        return Arrays.asList(MEDICAL_INSURANCE.getCode(), SOCIAL_INSURANCE.getCode(), TAX.getCode(), NATIONAL_TAX.getCode(),
                PRE_AUTH.getCode(), TAX_OPERATING_INCOME.getCode(),
                SETTLE_ACCOUNTS.getCode(), ANNUAL_REPORT.getCode(), RESIDUAL_BENEFITS.getCode(), TIMES_REPORT.getCode());
    }

    public static List<Integer> binlogUpdateDeliverType() {
    	return Arrays.asList(DeliverType.MEDICAL_INSURANCE.getCode(), DeliverType.SOCIAL_INSURANCE.getCode(), DeliverType.TAX.getCode(), DeliverType.NATIONAL_TAX.getCode(), DeliverType.TAX_OPERATING_INCOME.getCode(), DeliverType.PRE_AUTH.getCode());
    }
}
