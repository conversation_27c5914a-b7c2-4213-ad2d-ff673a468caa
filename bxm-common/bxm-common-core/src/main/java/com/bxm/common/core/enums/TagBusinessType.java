package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TagBusinessType {

    // 客户服务
    CUSTOMER_SERVICE(1),
    // 账期
    CUSTOMER_SERVICE_MONTH_PERIOD(2),
    // 新户流转
    NEW_CUSTOMER_TRANSFER(3),
    ;

    private final Integer code;

    public static TagBusinessType getByCode(Integer code) {
        for (TagBusinessType value : TagBusinessType.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
