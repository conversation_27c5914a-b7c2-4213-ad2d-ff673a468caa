package com.bxm.common.core.enums.customerService;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomerServiceEntityType {

    // 主体类型，1-企业、2-个体、3-民非、4-其他
    ENTERPRISE(1, "企业"),
    INDIVIDUAL(2, "个体"),
    MUNICIPAL_NONPROFIT(3, "民非"),
    OTHER(4, "其他"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static CustomerServiceEntityType getByCode(Integer code) {
        for (CustomerServiceEntityType value : CustomerServiceEntityType.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return UN_KNOW;
    }
}
