package com.bxm.common.core.utils.logstring;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字段处理器
 * 用于提取和处理对象字段信息
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
public class FieldProcessor {

    private final AnnotationExtractor annotationExtractor;
    private final ValueFormatter valueFormatter;
    private final SecurityMasker securityMasker;

    // 字段信息缓存
    private static final ConcurrentHashMap<Class<?>, List<FieldInfo>> fieldCache = new ConcurrentHashMap<>();

    public FieldProcessor() {
        this.annotationExtractor = new AnnotationExtractor();
        this.valueFormatter = new ValueFormatter();
        this.securityMasker = new SecurityMasker();
    }

    /**
     * 提取类的所有字段信息
     *
     * @param clazz 类对象
     * @param config 配置对象
     * @return 字段信息列表
     */
    public List<FieldInfo> extractFields(Class<?> clazz, LogStringConfig config) {
        if (clazz == null) {
            return new ArrayList<>();
        }

        // 如果启用缓存，先检查缓存
        if (config != null && config.isEnableCaching()) {
            List<FieldInfo> cachedFields = fieldCache.get(clazz);
            if (cachedFields != null) {
                return cachedFields;
            }
        }

        List<FieldInfo> fieldInfos = new ArrayList<>();

        // 获取所有字段，包括父类字段
        Field[] fields = getAllFields(clazz);

        for (Field field : fields) {
            if (annotationExtractor.shouldProcessField(field, config)) {
                FieldInfo fieldInfo = annotationExtractor.createFieldInfo(field, config);
                if (fieldInfo != null) {
                    fieldInfos.add(fieldInfo);
                }
            }
        }

        // 如果启用缓存，将结果放入缓存
        if (config != null && config.isEnableCaching()) {
            fieldCache.put(clazz, fieldInfos);
        }

        return fieldInfos;
    }

    /**
     * 格式化字段
     *
     * @param fieldInfo 字段信息
     * @param value 字段值
     * @param config 配置对象
     * @return 格式化后的字符串
     */
    public String formatField(FieldInfo fieldInfo, Object value, LogStringConfig config) {
        if (fieldInfo == null) {
            return "";
        }

        String formattedValue;

        // 检查是否有字段覆盖值
        if (config != null && config.isEnableFieldOverrides() &&
            config.getFieldOverrides().containsKey(fieldInfo.getOriginalName())) {
            formattedValue = config.getFieldOverrides().get(fieldInfo.getOriginalName());
        }
        // 如果是敏感字段且启用了掩码
        else if (fieldInfo.isSensitive() && config != null && config.isMaskSensitiveFields()) {
            formattedValue = securityMasker.maskValue(fieldInfo.getOriginalName(), value, config);
        } else {
            formattedValue = valueFormatter.formatValue(value, fieldInfo.getType(), config);
        }

        // 根据输出格式生成最终字符串
        return formatKeyValue(fieldInfo.getChineseLabel(), formattedValue, config);
    }

    /**
     * 检查是否应该处理该字段
     *
     * @param field 字段对象
     * @param config 配置对象
     * @return 是否应该处理
     */
    public boolean shouldProcessField(Field field, LogStringConfig config) {
        // 首先检查类特定字段忽略配置
        if (config != null && config.getClassSpecificIgnoreFields() != null) {
            String className = field.getDeclaringClass().getName();
            Set<String> ignoreFields = config.getClassSpecificIgnoreFields().get(className);
            if (ignoreFields != null && ignoreFields.contains(field.getName())) {
                return false;
            }
        }

        return annotationExtractor.shouldProcessField(field, config);
    }

    /**
     * 格式化键值对
     *
     * @param key 键
     * @param value 值
     * @param config 配置对象
     * @return 格式化后的键值对字符串
     */
    private String formatKeyValue(String key, String value, LogStringConfig config) {
        if (config == null) {
            return key + ": " + value;
        }

        switch (config.getFormat()) {
            case JSON_LIKE:
                return "\"" + key + "\": \"" + escapeJsonValue(value) + "\"";

            case STRUCTURED:
                return key + "=" + value;

            case MULTILINE:
                return key + config.getKeyValueSeparator() + value;

            case KEY_VALUE:
            default:
                return key + config.getKeyValueSeparator() + value;
        }
    }

    /**
     * 转义 JSON 值中的特殊字符
     *
     * @param value 原始值
     * @return 转义后的值
     */
    private String escapeJsonValue(String value) {
        if (value == null) {
            return "null";
        }

        return value.replace("\\", "\\\\")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }

    /**
     * 获取类的所有字段，包括父类字段
     *
     * @param clazz 类对象
     * @return 所有字段数组
     */
    private Field[] getAllFields(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();

        // 遍历当前类及其父类
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Field[] declaredFields = currentClass.getDeclaredFields();
            for (Field field : declaredFields) {
                fieldList.add(field);
            }
            currentClass = currentClass.getSuperclass();
        }

        return fieldList.toArray(new Field[0]);
    }

    /**
     * 清除字段缓存
     */
    public static void clearCache() {
        fieldCache.clear();
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存中的类数量
     */
    public static int getCacheSize() {
        return fieldCache.size();
    }
}
