package com.bxm.common.core.enums.businessTask;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/27 10:14
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum BusinessTaskOperateType {
    UN_KNOW(-1, "未知"),
    
    NEW(1, "新建"),
    UPDATE(2, "修改"),
    CLOSE(3, "关闭"),
    DELETE(4, "删除"),
    DISTRIBUTE(5, "分单"),
    ASSIGN(6, "派单"),
    FINISH(7, "完成"),
    CHECK(8, "审核"),
    HANDLE_EXCEPTION(9, "处理异常"),
    COMMENT(10, "评论"),
    DELIVER(11, "交付"),
    WAIT_IN_ACCOUNT(12, "入账处理中"),
    IN_ACCOUNT_COMPLETE(13, "入账处理完成"),
    NO_FLOW_WAIT_CHECK(14, "RPA无流水待核"),
    NORMAL_WAIT_CHECK(15, "RPA正常待核"),
    EXCEPTION_CLOSE(16, "RPA异常关闭"),
    RPA_DEALING(17, "RPA处理中"),
    EXCEPTION_WAIT_CHECK(18, "RPA异常待核"),
    MODIFY_BANK_ACCOUNT_NUMBER(19, "编辑银行账号"),
    RPA_OVER_TIME_CLOSE(20, "RPA超时关闭"),
    EXCEPTION_DELIVER(21, "入账处理异常交付"),

    ;

    private final Integer code;

    private final String name;

    public static BusinessTaskOperateType getByCode(Integer source) {
        for (BusinessTaskOperateType item : BusinessTaskOperateType.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
