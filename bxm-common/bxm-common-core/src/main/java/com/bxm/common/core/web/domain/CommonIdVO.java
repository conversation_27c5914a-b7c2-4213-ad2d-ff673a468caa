package com.bxm.common.core.web.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonIdVO {

    @ApiModelProperty("单个id")
    private Long id;

    @ApiModelProperty("id列表，批量操作用")
    private List<Long> ids;

    @ApiModelProperty("原因/备注")
    private String reason;

    @ApiModelProperty("文件")
    private List<CommonFileVO> files;

    @ApiModelProperty("1-顾问提交，2-会计提交")
    private Integer submitType;

    @ApiModelProperty("归还设置，0-无需归还，1-需要归还")
    private Integer returnSetting;

    @ApiModelProperty("目标小组id")
    private Long targetDeptId;

    @ApiModelProperty("目标员工id")
    private Long targetEmployeeId;

    @ApiModelProperty("目标状态")
    private Integer targetStatus;

    private Long deptId;

    private Long userId;
}
