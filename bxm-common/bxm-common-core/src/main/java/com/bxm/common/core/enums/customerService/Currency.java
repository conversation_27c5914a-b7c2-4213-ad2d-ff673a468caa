package com.bxm.common.core.enums.customerService;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum Currency {

    RMB("R", "人民币"),
    OTHER("O", "外币"),
    ;

    private final String code;

    private final String name;

    public static String getNameByCode(String code) {
        for (Currency value : Currency.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return "";
    }
}
